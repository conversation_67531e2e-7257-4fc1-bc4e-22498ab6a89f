import { useState, useCallback } from 'react';

type ActionStatus = 'idle' | 'processing' | 'success';

interface UseActionWithFeedbackOptions {
  successDuration?: number;
}

export const useActionWithFeedback = (action: () => Promise<void>, options: UseActionWithFeedbackOptions = {}) => {
  const { successDuration = 2000 } = options;
  const [status, setStatus] = useState<ActionStatus>('idle');

  const executeAction = useCallback(async () => {
    try {
      setStatus('processing');
      await action();
      setStatus('success');
      
      // Reset to idle after success duration
      setTimeout(() => {
        setStatus('idle');
      }, successDuration);
    } catch (error) {
      console.error('Action failed:', error);
      setStatus('idle');
    }
  }, [action, successDuration]);

  return {
    status,
    executeAction,
    isProcessing: status === 'processing',
    isSuccess: status === 'success',
    isIdle: status === 'idle'
  };
};