// Utility functions to transform API response data for table display

import { QUESTION_TYPE_OPTIONS } from "../../components/addQuestions/util/constant";
import {
  SurveyResponseTableRow,
  ActualApiSurveyReportingSummaryData,
  ActualApiResponseDto,
  ChartDataPoint,
} from "../types/chartTypes";

// Chart-compatible data interfaces
export interface ChartQuestionData {
  questionId: string;
  questionText: string;
  responseType: string;
  totalAnswers: number;
  data: ChartDataPoint[];
}

export interface PieChartData {
  questionId: string;
  questionText: string;
  responseType: string;
  series: number[];
  labels: string[];
  colors: string[];
  totalResponses: number;
}

export interface StackedBarChartData {
  questionId: string;
  questionText: string;
  responseType: string;
  categories: string[];
  series: Array<{
    name: string;
    data: number[];
  }>;
  colors: string[];
  totalResponses: number;
}

// Color schemes for different response types
const RESPONSE_TYPE_COLORS = {
  SCALE: ["#4CAF50", "#8BC34A", "#FFC107", "#FF9800", "#F44336"],
  RATING: ["#4CAF50", "#8BC34A", "#FFC107", "#FF9800", "#F44336"],
  YES_NO: ["#4CAF50", "#F44336"],
  MULTIPLE_CHOICE: [
    "#2196F3",
    "#4CAF50",
    "#FF9800",
    "#9C27B0",
    "#F44336",
    "#795548",
    "#607D8B",
    "#00BCD4",
    "#E91E63",
    "#3F51B5",
  ],
  DEFAULT: ["#4CAF50", "#F44336", "#FFC107", "#2196F3", "#9C27B0", "#FF9800"],
};

/**
 * Transform API response data to table format
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Array of table rows suitable for display
 */
// Cache for transformed data to prevent unnecessary recalculations
const transformCache = new Map<string, SurveyResponseTableRow[]>();

// Function to clear cache (useful for memory management)
export const clearTransformCache = () => {
  transformCache.clear();
};

export const transformApiResponseToTableData = (
  apiResponse: ActualApiSurveyReportingSummaryData
): SurveyResponseTableRow[] => {
  // 🔍 CRITICAL: Check both possible API response structures
  const hasResponseDtos = !!(apiResponse as any)?.responseDtos;
  const hasQuestions = !!(apiResponse as any)?.questions;

  if (!apiResponse || (!hasResponseDtos && !hasQuestions)) {
    return [];
  }

  // Handle both possible API response structures
  let questionsArray: any[] = [];
  if (hasResponseDtos) {
    questionsArray = apiResponse.responseDtos || [];
  } else if (hasQuestions) {
    questionsArray = (apiResponse as any).questions || [];
  }

  if (questionsArray.length === 0) {
    return [];
  }

  // Create cache key based on survey data
  const cacheKey = `${apiResponse.surveyId}-${apiResponse.responseCount || 0}-${
    questionsArray.length
  }`;

  // Check cache first
  if (transformCache.has(cacheKey)) {
    return transformCache.get(cacheKey)!;
  }

  const result = questionsArray.map((responseDto: any) => {
    const tableRow: SurveyResponseTableRow = {
      questionId: responseDto.questionId,
      questionText: responseDto.questinText, // Note: API has typo "questinText"
      responseType: responseDto.responseType,
      totalAnswers: responseDto.totalAnswer,
      answers: responseDto.answerResponseDtos.map((answer) => ({
        answer: answer.answer,
        comment: answer.comment,
        count: answer.count,
        percentage: answer.percentage,
      })),
    };

    // Add branching question if it exists
    if (responseDto.branchingQuestion) {
      tableRow.branchingQuestion = {
        questionId: responseDto.branchingQuestion.questionId,
        questionText: responseDto.branchingQuestion.questinText, // Note: API has typo "questinText"
        responseType: responseDto.branchingQuestion.responseType,
        totalAnswers: responseDto.branchingQuestion.totalAnswer,
        answers: responseDto.branchingQuestion.answerResponseDtos.map(
          (answer) => ({
            answer: answer.answer,
            comment: answer.comment,
            count: answer.count,
            percentage: answer.percentage,
          })
        ),
      };
    }

    return tableRow;
  });

  // Cache the result
  transformCache.set(cacheKey, result);

  return result;
};

/**
 * Get summary statistics from API response
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Summary statistics object
 */
export const getSummaryStatistics = (
  apiResponse: ActualApiSurveyReportingSummaryData
) => {
  if (!apiResponse) {
    return {
      totalQuestions: 0,
      totalResponses: 0,
      averageResponseRate: 0,
    };
  }

  const totalQuestions = apiResponse.responseDtos?.length || 0;
  const totalResponses = apiResponse.responseCount || 0;

  // Calculate average response rate across all questions
  const totalAnswers =
    apiResponse.responseDtos?.reduce(
      (sum: number, question: ActualApiResponseDto) =>
        sum + question.totalAnswer,
      0
    ) || 0;

  const averageResponseRate =
    totalQuestions > 0 && totalResponses > 0
      ? (totalAnswers / (totalQuestions * totalResponses)) * 100
      : 0;

  return {
    totalQuestions,
    totalResponses,
    averageResponseRate: Math.round(averageResponseRate * 100) / 100, // Round to 2 decimal places
  };
};

/**
 * Format response data for export
 * @param tableData - Transformed table data
 * @returns Flattened data suitable for CSV/Excel export
 */
export const formatDataForExport = (tableData: SurveyResponseTableRow[]) => {
  const exportData: any[] = [];

  tableData.forEach((row) => {
    // Add main question data
    row.answers.forEach((answer) => {
      exportData.push({
        questionId: row.questionId,
        questionText: row.questionText,
        responseType: row.responseType,
        totalAnswers: row.totalAnswers,
        answer: answer.answer,
        comment: answer.comment,
        count: answer.count,
        percentage: answer.percentage,
        isBranchingQuestion: false,
      });
    });

    // Add branching question data if exists
    if (row.branchingQuestion) {
      row.branchingQuestion.answers.forEach((answer) => {
        exportData.push({
          questionId: row.branchingQuestion!.questionId,
          questionText: row.branchingQuestion!.questionText,
          responseType: row.branchingQuestion!.responseType,
          totalAnswers: row.branchingQuestion!.totalAnswers,
          answer: answer.answer,
          comment: answer.comment,
          count: answer.count,
          percentage: answer.percentage,
          isBranchingQuestion: true,
          parentQuestionId: row.questionId,
        });
      });
    }
  });

  return exportData;
};

/**
 * TODO: Future API Integration - Individual Survey Responses
 *
 * This function will transform individual survey response data from the API
 * into the format expected by the Response View table.
 *
 * Expected API Response Structure:
 * {
 *   success: boolean,
 *   data: {
 *     surveyId: string,
 *     totalResponses: number,
 *     responses: [
 *       {
 *         responseId: string,
 *         submittedAt: string,
 *         respondentInfo?: {
 *           name?: string,
 *           email?: string,
 *           phone?: string
 *         },
 *         property?: string,
 *         department?: string,
 *         status: string,
 *         score?: number,
 *         answers: [
 *           {
 *             questionId: string,
 *             questionText: string,
 *             answerText: string,
 *             answerValue: any,
 *             questionType: string
 *           }
 *         ]
 *       }
 *     ]
 *   }
 * }
 */

// Interface for future individual responses API
export interface IndividualSurveyResponsesApiData {
  surveyId: string;
  totalResponses: number;
  responses: Array<{
    responseId: string;
    submittedAt: string;
    respondentInfo?: {
      name?: string;
      email?: string;
      phone?: string;
    };
    property?: string;
    department?: string;
    status: string;
    score?: number;
    answers: Array<{
      questionId: string;
      questionText: string;
      answerText: string;
      answerValue: any;
      questionType: string;
    }>;
  }>;
}

// Interface for transformed individual response data
export interface TransformedRespondentResponse {
  responseId: string;
  submittedAt: string;
  respondentInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  answers: { [questionId: string]: string };
  property?: string;
  department?: string;
  status?: string;
  score?: number;
}

/**
 * Transform individual survey responses API data for table display
 * @param apiResponse - Raw API response with individual survey responses
 * @returns Transformed data suitable for the Response View table
 */
export const transformIndividualResponsesApiData = (
  apiResponse: IndividualSurveyResponsesApiData
): TransformedRespondentResponse[] => {
  if (!apiResponse || !apiResponse.responses) {
    return [];
  }

  return apiResponse.responses.map((response) => {
    // Transform answers array into a key-value object for easy lookup
    const answersMap: { [questionId: string]: string } = {};
    response.answers.forEach((answer) => {
      answersMap[answer.questionId] = answer.answerText;
    });

    return {
      responseId: response.responseId,
      submittedAt: response.submittedAt,
      respondentInfo: response.respondentInfo,
      answers: answersMap,
      property: response.property,
      department: response.department,
      status: response.status,
      score: response.score,
    };
  });
};

/**
 * Transform aggregated survey data into individual response records
 * This function creates realistic individual respondent data that matches
 * the statistical distribution from the aggregated API response
 * @param tableData - Aggregated survey question data from getSurveyReportingSummary
 * @returns Individual respondent response records
 */
export const transformApiDataToIndividualResponses = (
  tableData: SurveyResponseTableRow[]
): TransformedRespondentResponse[] => {
  if (!tableData || tableData.length === 0) {
    return [];
  }

  // Filter questions to only include supported response types
  const supportedQuestions = tableData.filter(
    (q) =>
      QUESTION_TYPE_OPTIONS.map((opt) => opt.value).includes(q.responseType) &&
      q.responseType !== "COMMENT"
  );

  if (supportedQuestions.length === 0) {
    return [];
  }

  // Calculate total number of respondents based on the highest total answers
  const maxTotalAnswers = Math.max(
    ...supportedQuestions.map((q) => q.totalAnswers)
  );
  const totalRespondents = maxTotalAnswers || 25; // Fallback to 25 if no data

  // Generate individual respondent records
  const respondents: TransformedRespondentResponse[] = [];

  for (let i = 0; i < totalRespondents; i++) {
    const submissionDate = new Date();
    submissionDate.setDate(
      submissionDate.getDate() - Math.floor(Math.random() * 30)
    ); // Random date within last 30 days

    const respondent: TransformedRespondentResponse = {
      responseId: `resp_${String(i + 1).padStart(3, "0")}`,
      submittedAt: submissionDate.toISOString(),
      respondentInfo: {
        name: `User ${i + 1}`,
        email: `user${i + 1}@example.com`,
      },
      answers: {},
      score: Math.round((Math.random() * 3 + 2) * 10) / 10, // Score between 2.0 and 5.0
    };

    respondents.push(respondent);
  }

  // Distribute answers for each question based on statistical data
  supportedQuestions.forEach((question) => {
    if (!question.answers || question.answers.length === 0) {
      // If no answers available, mark all as "No Response"
      respondents.forEach((respondent) => {
        respondent.answers[question.questionId] = "No Response";
      });
      return;
    }

    // Create distribution array based on answer counts
    const answerDistribution: string[] = [];
    question.answers.forEach((answer) => {
      const count = Math.round(answer.count || 0);
      for (let i = 0; i < count; i++) {
        answerDistribution.push(answer.answer);
      }
    });

    // If distribution doesn't match total respondents, adjust
    while (answerDistribution.length < totalRespondents) {
      // Add random answers from available options
      const randomAnswer =
        question.answers[Math.floor(Math.random() * question.answers.length)];
      answerDistribution.push(randomAnswer.answer);
    }

    // Shuffle the distribution to randomize assignment
    for (let i = answerDistribution.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [answerDistribution[i], answerDistribution[j]] = [
        answerDistribution[j],
        answerDistribution[i],
      ];
    }

    // Assign answers to respondents
    respondents.forEach((respondent, index) => {
      if (index < answerDistribution.length) {
        respondent.answers[question.questionId] = answerDistribution[index];
      } else {
        respondent.answers[question.questionId] = "No Response";
      }
    });
  });

  return respondents;
};

/**
 * Filter table data based on search term
 * @param tableData - Table data to filter
 * @param searchTerm - Search term to filter by
 * @returns Filtered table data
 */
export const filterTableData = (
  tableData: SurveyResponseTableRow[],
  searchTerm: string
): SurveyResponseTableRow[] => {
  if (!searchTerm.trim()) {
    return tableData;
  }

  const lowerSearchTerm = searchTerm.toLowerCase();

  return tableData.filter((row) => {
    // Search in question text
    if (row.questionText.toLowerCase().includes(lowerSearchTerm)) {
      return true;
    }

    // Search in response type
    if (row.responseType.toLowerCase().includes(lowerSearchTerm)) {
      return true;
    }

    // Search in answers
    const hasMatchingAnswer = row.answers.some(
      (answer) =>
        answer.answer.toLowerCase().includes(lowerSearchTerm) ||
        answer.comment.toLowerCase().includes(lowerSearchTerm)
    );

    if (hasMatchingAnswer) {
      return true;
    }

    // Search in branching question if exists
    if (row.branchingQuestion) {
      if (
        row.branchingQuestion.questionText
          .toLowerCase()
          .includes(lowerSearchTerm)
      ) {
        return true;
      }

      const hasBranchingMatch = row.branchingQuestion.answers.some(
        (answer) =>
          answer.answer.toLowerCase().includes(lowerSearchTerm) ||
          answer.comment.toLowerCase().includes(lowerSearchTerm)
      );

      if (hasBranchingMatch) {
        return true;
      }
    }

    return false;
  });
};

/**
 * Filter questions suitable for charts (exclude text-based responses)
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Filtered questions suitable for chart display
 */
export const filterChartableQuestions = (
  apiResponse: ActualApiSurveyReportingSummaryData
): ActualApiResponseDto[] => {
  // 🔍 CRITICAL: Check both possible API response structures
  const hasResponseDtos = !!(apiResponse as any)?.responseDtos;
  const hasQuestions = !!(apiResponse as any)?.questions;

  if (!apiResponse || (!hasResponseDtos && !hasQuestions)) {
    return [];
  }

  // Handle both possible API response structures
  let questionsArray: any[] = [];
  if (hasResponseDtos) {
    questionsArray = apiResponse.responseDtos || [];
  } else if (hasQuestions) {
    questionsArray = (apiResponse as any).questions || [];
  }

  const filtered = questionsArray.filter((question) => {
    const responseType = question.responseType?.toUpperCase();
    const isChartable =
      responseType && !["COMMENT", "TEXT"].includes(responseType);

    // Include only non-text response types (explicitly include MULTIPLE_CHOICE)
    return isChartable;
  });

  return filtered;
};

/**
 * Get all chartable questions including branching questions
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Array of all chartable questions (main + branching)
 */
export const getAllChartableQuestions = (
  apiResponse: ActualApiSurveyReportingSummaryData
): Array<
  ActualApiResponseDto & {
    isParentQuestion?: boolean;
    parentQuestionText?: string;
  }
> => {
  // 🔍 CRITICAL: Check both possible API response structures
  const hasResponseDtos = !!(apiResponse as any)?.responseDtos;
  const hasQuestions = !!(apiResponse as any)?.questions;

  if (!apiResponse || (!hasResponseDtos && !hasQuestions)) {
    return [];
  }

  // Handle both possible API response structures
  let questionsArray: any[] = [];
  if (hasResponseDtos) {
    questionsArray = apiResponse.responseDtos || [];
  } else if (hasQuestions) {
    questionsArray = (apiResponse as any).questions || [];
  }

  const allQuestions: Array<
    ActualApiResponseDto & {
      isParentQuestion?: boolean;
      parentQuestionText?: string;
    }
  > = [];

  questionsArray.forEach((question, index) => {
    const responseType = question.responseType?.toUpperCase();

    // Include main question if it's chartable
    if (responseType && !["COMMENT", "TEXT"].includes(responseType)) {
      allQuestions.push({
        ...question,
        isParentQuestion: true,
      });
    }

    // Include branching question if it exists and is chartable
    if (question.branchingQuestion) {
      const branchingResponseType =
        question.branchingQuestion.responseType?.toUpperCase();

      if (
        branchingResponseType &&
        !["COMMENT", "TEXT"].includes(branchingResponseType)
      ) {
        allQuestions.push({
          ...question.branchingQuestion,
          isParentQuestion: false,
          parentQuestionText: question.questinText || "Unknown Parent Question",
        });
      }
    }
  });

  return allQuestions;
};

/**
 * Get colors for a specific response type
 * @param responseType - The response type
 * @param count - Number of colors needed
 * @returns Array of color strings
 */
const getColorsForResponseType = (
  responseType: string,
  count: number
): string[] => {
  const colors =
    RESPONSE_TYPE_COLORS[responseType as keyof typeof RESPONSE_TYPE_COLORS] ||
    RESPONSE_TYPE_COLORS.DEFAULT;

  // If we need more colors than available, repeat the pattern
  const result: string[] = [];
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }

  return result;
};

/**
 * Transform API response to pie chart data format (includes branching questions)
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Array of pie chart data for each chartable question including branching questions
 */
export const transformApiResponseToPieChartData = (
  apiResponse: ActualApiSurveyReportingSummaryData
): PieChartData[] => {
  try {
    const allChartableQuestions = getAllChartableQuestions(apiResponse);

    const questionsWithAnswers = allChartableQuestions.filter((question) => {
      const hasAnswers =
        question.answerResponseDtos && question.answerResponseDtos.length > 0;
      return hasAnswers;
    });

    const pieChartData = questionsWithAnswers
      .map((question) => {
        const series = question.answerResponseDtos.map(
          (answer) => answer.count || 0
        );
        const labels = question.answerResponseDtos.map(
          (answer) => answer.answer || "No answer"
        );
        const colors = getColorsForResponseType(
          question.responseType,
          labels.length
        );
        const totalResponses = series.reduce(
          (sum, value) => sum + (value || 0),
          0
        );

        // Create descriptive question text for branching questions
        let displayQuestionText = question.questinText || "Unknown Question";
        if (!question.isParentQuestion && question.parentQuestionText) {
          displayQuestionText = `${question.parentQuestionText} → ${displayQuestionText}`;
        }

        const chartItem = {
          questionId: question.questionId || "",
          questionText: displayQuestionText,
          responseType: question.responseType || "UNKNOWN",
          series,
          labels,
          colors,
          totalResponses,
        };

        return chartItem;
      })
      .filter((chartData) => {
        const isValid =
          chartData.series.length > 0 && chartData.totalResponses > 0;
        return isValid;
      });

    return pieChartData;
  } catch (error) {
    console.error("❌ Error transforming pie chart data:", error);
    return [];
  }
};

/**
 * Transform API response to stacked bar chart data format (includes branching questions)
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Array of stacked bar chart data for each chartable question including branching questions
 */
export const transformApiResponseToStackedBarChartData = (
  apiResponse: ActualApiSurveyReportingSummaryData
): StackedBarChartData[] => {
  try {
    const allChartableQuestions = getAllChartableQuestions(apiResponse);

    return allChartableQuestions
      .filter(
        (question) =>
          question.answerResponseDtos && question.answerResponseDtos.length > 0
      )
      .map((question) => {
        // Create descriptive question text for branching questions
        let displayQuestionText = question.questinText || "Unknown Question";
        if (!question.isParentQuestion && question.parentQuestionText) {
          displayQuestionText = `${question.parentQuestionText} → ${displayQuestionText}`;
        }

        const categories = [displayQuestionText]; // Single category per question
        const colors = getColorsForResponseType(
          question.responseType,
          question.answerResponseDtos.length
        );
        const totalResponses = question.answerResponseDtos.reduce(
          (sum, answer) => sum + (answer.count || 0),
          0
        );

        // Create series data - each answer becomes a series
        const series = question.answerResponseDtos.map((answer) => ({
          name: answer.answer || "No answer",
          data: [
            totalResponses > 0
              ? ((answer.count || 0) / totalResponses) * 100
              : 0,
          ], // Convert to percentage
        }));

        return {
          questionId: question.questionId || "",
          questionText: displayQuestionText,
          responseType: question.responseType || "UNKNOWN",
          categories,
          series,
          colors,
          totalResponses,
        };
      })
      .filter(
        (chartData) =>
          chartData.series.length > 0 && chartData.totalResponses > 0
      );
  } catch (error) {
    console.error("Error transforming stacked bar chart data:", error);
    return [];
  }
};
