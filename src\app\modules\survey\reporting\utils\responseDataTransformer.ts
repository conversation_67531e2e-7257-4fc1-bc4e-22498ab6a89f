// Utility functions to transform API response data for table display

import {
  SurveyResponseTableRow,
  ActualApiSurveyReportingSummaryData,
  ActualApiResponseDto
} from '../types/chartTypes';

/**
 * Transform API response data to table format
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Array of table rows suitable for display
 */
// Cache for transformed data to prevent unnecessary recalculations
const transformCache = new Map<string, SurveyResponseTableRow[]>();

// Function to clear cache (useful for memory management)
export const clearTransformCache = () => {
  console.log("🧹 Clearing transform cache");
  transformCache.clear();
};

export const transformApiResponseToTableData = (
  apiResponse: ActualApiSurveyReportingSummaryData
): SurveyResponseTableRow[] => {
  // console.log("🔄 transformApiResponseToTableData called");

  if (!apiResponse || !apiResponse.responseDtos) {
    console.log("❌ No responseDtos found in API response");
    return [];
  }

  // Create cache key based on survey data
  const cacheKey = `${apiResponse.surveyId}-${apiResponse.responseCount}-${apiResponse.responseDtos.length}`;

  // Check cache first
  if (transformCache.has(cacheKey)) {
    console.log("✅ Using cached transformed data for key:", cacheKey);
    return transformCache.get(cacheKey)!;
  }

  // console.log("🔄 Transforming API response - cache miss for key:", cacheKey);
  // console.log("🔄 Processing", apiResponse.responseDtos.length, "questions");

  const result = apiResponse.responseDtos.map((responseDto: ActualApiResponseDto) => {
    const tableRow: SurveyResponseTableRow = {
      questionId: responseDto.questionId,
      questionText: responseDto.questinText, // Note: API has typo "questinText"
      responseType: responseDto.responseType,
      totalAnswers: responseDto.totalAnswer,
      answers: responseDto.answerResponseDtos.map(answer => ({
        answer: answer.answer,
        comment: answer.comment,
        count: answer.count,
        percentage: answer.percentage
      }))
    };

    // Add branching question if it exists
    if (responseDto.branchingQuestion) {
      tableRow.branchingQuestion = {
        questionId: responseDto.branchingQuestion.questionId,
        questionText: responseDto.branchingQuestion.questinText, // Note: API has typo "questinText"
        responseType: responseDto.branchingQuestion.responseType,
        totalAnswers: responseDto.branchingQuestion.totalAnswer,
        answers: responseDto.branchingQuestion.answerResponseDtos.map(answer => ({
          answer: answer.answer,
          comment: answer.comment,
          count: answer.count,
          percentage: answer.percentage
        }))
      };
    }

    return tableRow;
  });

  // Cache the result
  transformCache.set(cacheKey, result);
  // console.log("✅ Cached transformed result for key:", cacheKey);
  // console.log("🎉 Final transformed result:", result);

  return result;
};

/**
 * Get summary statistics from API response
 * @param apiResponse - The raw API response from getSurveyReportingSummary
 * @returns Summary statistics object
 */
export const getSummaryStatistics = (
  apiResponse: ActualApiSurveyReportingSummaryData
) => {
  if (!apiResponse) {
    return {
      totalQuestions: 0,
      totalResponses: 0,
      averageResponseRate: 0
    };
  }

  const totalQuestions = apiResponse.responseDtos?.length || 0;
  const totalResponses = apiResponse.responseCount || 0;

  // Calculate average response rate across all questions
  const totalAnswers = apiResponse.responseDtos?.reduce(
    (sum: number, question: ActualApiResponseDto) => sum + question.totalAnswer,
    0
  ) || 0;

  const averageResponseRate = totalQuestions > 0 && totalResponses > 0
    ? (totalAnswers / (totalQuestions * totalResponses)) * 100
    : 0;

  return {
    totalQuestions,
    totalResponses,
    averageResponseRate: Math.round(averageResponseRate * 100) / 100 // Round to 2 decimal places
  };
};

/**
 * Format response data for export
 * @param tableData - Transformed table data
 * @returns Flattened data suitable for CSV/Excel export
 */
export const formatDataForExport = (tableData: SurveyResponseTableRow[]) => {
  const exportData: any[] = [];

  tableData.forEach(row => {
    // Add main question data
    row.answers.forEach(answer => {
      exportData.push({
        questionId: row.questionId,
        questionText: row.questionText,
        responseType: row.responseType,
        totalAnswers: row.totalAnswers,
        answer: answer.answer,
        comment: answer.comment,
        count: answer.count,
        percentage: answer.percentage,
        isBranchingQuestion: false
      });
    });

    // Add branching question data if exists
    if (row.branchingQuestion) {
      row.branchingQuestion.answers.forEach(answer => {
        exportData.push({
          questionId: row.branchingQuestion!.questionId,
          questionText: row.branchingQuestion!.questionText,
          responseType: row.branchingQuestion!.responseType,
          totalAnswers: row.branchingQuestion!.totalAnswers,
          answer: answer.answer,
          comment: answer.comment,
          count: answer.count,
          percentage: answer.percentage,
          isBranchingQuestion: true,
          parentQuestionId: row.questionId
        });
      });
    }
  });

  return exportData;
};

/**
 * Filter table data based on search term
 * @param tableData - Table data to filter
 * @param searchTerm - Search term to filter by
 * @returns Filtered table data
 */
export const filterTableData = (
  tableData: SurveyResponseTableRow[], 
  searchTerm: string
): SurveyResponseTableRow[] => {
  if (!searchTerm.trim()) {
    return tableData;
  }

  const lowerSearchTerm = searchTerm.toLowerCase();

  return tableData.filter(row => {
    // Search in question text
    if (row.questionText.toLowerCase().includes(lowerSearchTerm)) {
      return true;
    }

    // Search in response type
    if (row.responseType.toLowerCase().includes(lowerSearchTerm)) {
      return true;
    }

    // Search in answers
    const hasMatchingAnswer = row.answers.some(answer => 
      answer.answer.toLowerCase().includes(lowerSearchTerm) ||
      answer.comment.toLowerCase().includes(lowerSearchTerm)
    );

    if (hasMatchingAnswer) {
      return true;
    }

    // Search in branching question if exists
    if (row.branchingQuestion) {
      if (row.branchingQuestion.questionText.toLowerCase().includes(lowerSearchTerm)) {
        return true;
      }

      const hasBranchingMatch = row.branchingQuestion.answers.some(answer =>
        answer.answer.toLowerCase().includes(lowerSearchTerm) ||
        answer.comment.toLowerCase().includes(lowerSearchTerm)
      );

      if (hasBranchingMatch) {
        return true;
      }
    }

    return false;
  });
};
