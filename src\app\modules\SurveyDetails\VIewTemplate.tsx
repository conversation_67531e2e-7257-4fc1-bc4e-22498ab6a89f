import { useNavigate } from "react-router";
import QuestionSetup from "../survey/createSurvey/QuestionSetup";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import { MdContentCopy, MdOutlineEdit } from "react-icons/md";
import { useDuplicateSurveyMutation } from "../../apis/survaysAPI";
import SwalMessage from "../common/SwalMessage";
import { swalMessages } from "../../utils/CommonUtils";

const VIewTemplate = () => {
  const navigate = useNavigate();
  const { surveyId, surveyType } = useSurveyUtil();
  const [duplicateSurvey, { isLoading: isDuplicateLoading }] =
    useDuplicateSurveyMutation();

  const handleDuplicate = async () => {
    const confirm = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.duplicateTemplate,
      "Duplicate",
      swalMessages.icon.info,
      true
    );
    if (confirm) {
      duplicateSurvey({ surveyId })
        .unwrap()
        .then((res) => {
          const isSuccess = res?.success;
          if (isSuccess) {
            SwalMessage(
              null,
              "Survey Template has been duplicated successfully. Keep in mind you will need the property update,to whom the survey goes to and any ticket assignment fields prior to the QR code generation.",
              "Ok",
              "success",
              false
            );
          } else {
            SwalMessage(
              null,
              res?.errormsg || "Failed to duplicate survey",
              "Ok",
              "error",
              false
            );
          }
        })
        .catch((error) => {
          SwalMessage(null, "Failed to duplicate survey", "Ok", "error", false);
        });
    }
  };

  const handleQuestionEdit = () => {
    navigate(`/surveys/survey-create?surveyType=${surveyType}`, {
      state: {
        id: surveyId,
      },
    });
  };

  return (
    <div>
      <div>
        <button className="btn rx-btn me-3" onClick={handleQuestionEdit}>
          <MdOutlineEdit className="me-4" />
          Edit Survey Template
        </button>
        <button className="btn rx-btn me-3" onClick={handleDuplicate}>
          <MdContentCopy className="me-4" /> Duplicate Template
        </button>
        <button className="btn rx-btn me-3">Inactivate Template</button>
      </div>
      <QuestionSetup hideFooter={true} hideAddBtn={true} />
    </div>
  );
};

export default VIewTemplate;
