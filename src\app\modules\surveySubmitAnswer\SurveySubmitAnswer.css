/* Glassmorphism color variables for light/dark mode */
/* .survey-answer-container {
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-bg-blur: 8px;
}
@media (prefers-color-scheme: dark) {
  .survey-answer-container {
    --glass-bg: rgba(30, 32, 38, 0.85);
  }
} */
.survey-answer-container {
  --glass-bg-blur: 8px;
}
/* SurveyAnswerContainer glassmorphism and layout styles */
.survey-bg {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.survey-card {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-bg-blur));
  -webkit-backdrop-filter: blur(var(--glass-bg-blur));
  border-radius: 0rem;
  box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.15);
  max-width: 700px;
  margin: 0 auto;
  /* background: rgba(255, 255, 255, 0.8); */
}

.survey-card-body {
  display: flex;
  flex-direction: column;
  height: 80vh;
  min-height: 400px;
  padding: 0;
  background: var(--glass-bg-wrapper);
  /* backdrop-filter: blur(var(--glass-bg-blur)); */
  /* -webkit-backdrop-filter: blur(var(--glass-bg-blur)); */
}

.survey-sticky-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-bg-blur));
  -webkit-backdrop-filter: blur(var(--glass-bg-blur));
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  /* box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.05); */
}

.no-margin-bottom {
  margin-bottom: 0 !important;
}

.survey-question-area {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-bg-blur));
  -webkit-backdrop-filter: blur(var(--glass-bg-blur));
}

.survey-sticky-footer {
  position: sticky;
  bottom: 0;
  z-index: 2;
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-bg-blur));
  -webkit-backdrop-filter: blur(var(--glass-bg-blur));
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
  padding: 1rem 1.5rem;
  /* box-shadow: 0 -2px 8px 0 rgba(31, 38, 135, 0.05); */
}
.survey-submit-answer-page {
  /* min-height: 100vh; */
  /* background-color: #f8f9fa; */
  /* padding: 20px 0; */
}

.survey-welcome {
  /* min-height: 100vh; */
  /* display: flex; */
  /* align-items: center; */
  /* background-color: #f8f9fa; */
}

.survey-answer-container,
.survey-welcome {
  /* min-height: 100vh; */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  /* background-color: #f8f9fa; */
}

.survey-answer-container .card,
.survey-welcome .card {
  width: 85%;
}

.survey-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.survey-question-step {
  margin-bottom: 2rem;
}

.survey-navigation {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.question-counter {
  font-size: 1.1rem;
  color: #6c757d;
}

.progress {
  height: 8px;
  border-radius: 4px;
}

.progress-bar {
  background-color: #007bff;
  border-radius: 4px;
}

/* Responsive adjustments */
/* @media (max-width: 768px) {
  .survey-submit-answer-page {
    padding: 10px 0;
  }
  
  .survey-navigation .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .survey-navigation .question-counter {
    order: -1;
    text-align: center;
  }
} */
