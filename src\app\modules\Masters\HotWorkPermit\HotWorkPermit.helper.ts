import { content } from "html2canvas/dist/types/css/property-descriptors/content";
import { APIs } from "../../../serverconfig/apiURLs";
import axiosInstance, { HotworkInstance } from "../../../serverconfig/axiosInstance";
import encryptDecryptUtil from "../../../utils/encrypt-decrypt-util";

class HotWorkPermitService {
  hotworkpermittemplategrid = (payload: any) => {

    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.hotworkpermittemplategrid, {
      encryptedData: encPayload,
    });
  };

  savetemplatename = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.savetemplatename, {
      encryptedData: encPayload,
    });
  };

  getInsuranceCompany = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(
      APIs.ALL_HEADERS.getHotWorkPermitInsuranceCompany,
      {
        encryptedData: encPayload,
      }
    );
  };

  getWorktobeperformed = () => {
    return HotworkInstance.post(
      APIs.ALL_HEADERS.getHotWorkPermitworktobeperformed
    );
  };

  saveWorktobeperformed = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(
      APIs.ALL_HEADERS.saveHotWorkPermitworktobeperformed,
      {
        encryptedData: encPayload,
      }
    );
  };

  getHotWorkPermitPrecaution = (templateid: any) => {
    const payload = {
      templateid: templateid,
    };
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    // console.log(encPayload);
    return HotworkInstance.post(APIs.ALL_HEADERS.getHotWorkPrecaution, {
      encryptedData: encPayload,
    });
  };

  saveHotWorkPermitPrecaution = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.saveHotWorkPrecaution, {
      encryptedData: encPayload,
    });
  };

  getPostWorkInspection = (templateid: any) => {
    const payload = {
      templateid: templateid,
    };
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    // console.log(encPayload);
    return HotworkInstance.post(APIs.ALL_HEADERS.getPostWorkInspection, {
      encryptedData: encPayload,
    });
  };

  savePostWorkInspection = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.savePostWorkInspection, {
      encryptedData: encPayload,
    });
  };

  getUserDropDown(
    search: any,
    propertyId: any,
    departmentId: any,
    ticketId: any
  ) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const payload = {
      search: search ? search : "",
      propertyid: propertyId ? propertyId : "",
      departmentid: departmentId ? departmentId : "",
      ticketid: ticketId ? ticketId : "",
    };
    let encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return axiosInstance.post(APIs.ALL_HEADERS.userDropdownData, {
      encryptedData: encPayload,
    });
  }

  getMembersForTemplate(search: any, hwptId: any, propertyId: any, departmentId: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const payload = {
      search: search ? search : "",
      hwptid: hwptId ? hwptId : "",
      propertyId: propertyId,
      departmentId: departmentId
    };
    let encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
    return HotworkInstance.post(APIs.ALL_HEADERS.getMembersForTemplate, {
      encryptedData: encPayload,
    });
  }

  saveEscalationProtocol(payload: any) {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.saveEscalationProtocolData, {
      encryptedData: encPayload,
    });
  }

  saveHotperformedat = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.saveHotperformedat, {
      encryptedData: encPayload,
    });
  };

  savepermitexpiry = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savepermitexpiry, {
      encryptedData: encPayload,
    });
  };

  savecheckinduration = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savecheckinduration, {
      encryptedData: encPayload,
    });
  };

  savepostworkduration = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savepostworkduration, {
      encryptedData: encPayload,
    });
  };

  saveHotWorkPermissions = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.saveHotWorkPermissions, {
      encryptedData: encPayload,
    });
  };

  savegeofencing = (payload: any) => {
    console.log("payload", payload);

    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savegeofencing, {
      encryptedData: encPayload,
    });
  };

  savelocationofwork = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savelocationofwork, {
      encryptedData: encPayload,
    });
  };

  savebreakandhandoff = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savebreakandhandoff, {
      encryptedData: encPayload,
    });
  };

  savenewinsurancecompany = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savenewinsurancecompany, {
      encryptedData: encPayload,
    });
  };

  saveinsurancecompany = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.saveinsurancecompany, {
      encryptedData: encPayload,
    });
  };

  getEmergencyContact = (search: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const payload = {
      search: search ? search : "",
    };
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.getemergencycontacts, {
      encryptedData: encPayload,
    })
  }

  saveemergencycontacts = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.saveemergencycontacts, {
      encryptedData: encPayload,
    });
  };

  createnewemergencycontact = (payload: any, file: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
    const formData = new FormData();
    formData.append("encryptedData", encPayload);
    formData.append("files", file)
    return HotworkInstance.post(APIs.MULTIPART.createnewemergencycontact, formData);
  }

  saveFireWatch = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.saveFireWatcher, {
      encryptedData: encPayload,
    });
  }

  savepermitAuthorizer = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.savePermitAuthorizer, {
      encryptedData: encPayload,
    });
  };

  getEditTemplateData = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    return HotworkInstance.post(APIs.ALL_HEADERS.geteditdata, {
      encryptedData: encPayload,
    });
  };

  saveIntructions = (payload: any, file: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil
      .encryptData(JSON.stringify(payload), keyinfo.syckey)
      .toString();
    const formData = new FormData();
    formData.append("encryptedData", encPayload);
    formData.append("files", file);

    return HotworkInstance.post(APIs.MULTIPART.saveintructions, formData);
  };


  publichotworktemplate = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.publichotworktemplate, {
      encryptedData: encPayload,
    });
  };

  createduplicatetemplate = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.createduplicatetemplate, {
      encryptedData: encPayload,
    });
  }
  getHotWorkPermitView = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.gethotworkpermitview, {
      encryptedData: encPayload,
    });
  }

  getHotWorkPermitGrid = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.GRIDCALLS.gethotworkpermitfilldata, {
      encryptedData: encPayload,
    });
  };

  changeactivestatusforhotwork = (payload: any) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const encPayload = encryptDecryptUtil.encryptData(
      JSON.stringify(payload),
      keyinfo.syckey
    );
    return HotworkInstance.post(APIs.ALL_HEADERS.changeactivestatusforhotwork, {
      encryptedData: encPayload,
    });
  }

}

export const hotworkspermitService = new HotWorkPermitService();
