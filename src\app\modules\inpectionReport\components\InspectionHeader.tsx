import { Button } from "react-bootstrap";
import { BsFileText } from "react-icons/bs";
import { InspectionReportDetails } from "../../../apis/type";
import ReportActions from "./ReportActions";

interface InspectionHeaderProps {
  questionCount: string;
  onPrint: () => void;
  report?: InspectionReportDetails;
}

const InspectionHeader: React.FC<InspectionHeaderProps> = ({
  questionCount,
  onPrint,
  report,
}) => (
  <div className="d-flex flex-column flex-md-row justify-content-between align-items-md-start mb-4 gap-3">
    <div>
      <h1 className="fs-4 fw-semibold mb-0">Inspectee Report</h1>
      <p className="small mb-0">{Number(questionCount)} Questions</p>
    </div>

    <ReportActions onPrint={onPrint} report={report} />
  </div>
);

export default InspectionHeader;
