import { orderBy } from "@progress/kendo-data-query";
import {
  GridColumn as Column,
  Grid,
  GridToolbar,
} from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import React, { useEffect, useState } from "react";
import { Dropdown } from "react-bootstrap";
import { FaFileDownload, FaRegEye } from "react-icons/fa";
import { HiQrcode } from "react-icons/hi";
import { IoMdAdd, IoMdMore } from "react-icons/io";
import { IoFilter } from "react-icons/io5";
import { MdOutlineDeleteForever, MdOutlineEdit } from "react-icons/md";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import { setProcedureVaultData } from "../../../../redux/ProcedureValut/ProcedureValutSlice";
import { dropdownObject, filterObject, swalMessages } from "../../../../utils/CommonUtils";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../common/SwalMessage";
import Add_Logout_Togout_Card_Modal from "../../../pages/Add_Logout_Togout_Card_Modal";
import Logout_Tagout_Filter_Modal from "../../../pages/Logout_Tagout_Filter_Modal";
import Print_QRCode_Modal from "../../../pages/Print_QRCode_Modal";
import { lockoutTagoutService } from "./Lockout_Tagout.helper";
import View_LockOut_Modal from "./ViewLockoutTagout/View_LockOut_Modal";

function Logout_Tagout({ refreshKey, currentLoto, PropertyData, setGridLoading, gridLoading }: any) {
  const dispatch = useDispatch();

  const navigate = useNavigate();
  const [gridData, setGridData] = useState<any[]>([]);
  const [totalCount, setTotalCount] = useState<any>();
  const [viewFilterArray, setViewFilterArray] = useState<filterObject>({
    properties: [],
    locktype: {},
    search: "",
    departmentIds: []
  });
  const [lotoformEditResponse, setLotoformEditResponse] = useState<any>();
  const [lotoformEditId, setLotoformEditId] = useState<any>("");
  // const [gridLoading, setGridLoading] = useState<Boolean>(false);
  const [qrBase64, setQrBase64] = useState();
  const handleQRView = (qrBase64Param: any, id: any) => {
    setqrmodal(true);
    setQrBase64(qrBase64Param);
    setLotoformEditId(id);
  };
  const permissiontype = JSON.parse(
    localStorage.getItem("userdetail") as string
  )?.permission;
  const initialSort: Array<any> = [{ field: "User Name", dir: "asc" }];
  const [sort, setSort] = useState(initialSort);
  const [modal, setmodal] = useState(false);
  const [qrmodal, setqrmodal] = useState(false);
  const [filtermodal, setfiltermodal] = useState(false);
  const [viewmodal, setviewmodal] = useState(false);

  const handleDelete = async (id: any) => {
    const confirm = SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.deleteCompanyMsg,
      "Delete",
      swalMessages.icon.info,
      true
    );
    if (await confirm) {
      setGridLoading(true)
      lockoutTagoutService
        .deletelockouttagoutgridData(id)
        .then((response) => {
          if (response.status == 200) {
            if (response.data.success == true) {
              setGridLoading(false);
              SwalMessage(null, response.data.errormsg, "Ok", "success", false).then((isConfirm) => {
                if (isConfirm) {
                  lockoutTagoutGrid(viewFilterArray);
                }
              })
            } else {
              SwalMessage(null, response.data.errormsg, "Ok", "error", false);
              setGridLoading(false);
            }
          }
        })
        .catch((error) => {
          if (error.response.status == 401) {
            // localStorage.removeItem("islogin");
            navigate("/dashboard");
          }
          SwalMessage(null, error?.message, "Ok", "error", false);
          setGridLoading(false);
        })
        .finally(() => {
          setGridLoading(false);
        });
    }
  };

  const handleedit = (guid: any, isView: boolean) => {
    let keyinfo = JSON.parse(localStorage.keyinfo);
    setGridLoading(true);
    lockoutTagoutService
      .fetchsingleLotoForm(guid)
      .then((response: any) => {
        if (response.status == 200) {
          if (response.data.applicationStatusCode == 9001) {
            SwalMessage(null, response.data?.httpStatus, "Ok", "error", false);
            return;
          } else {
            if (response?.data?.success === false) {
              SwalMessage(null, response.data.errormsg, "Ok", "error", false);
              setGridLoading(false);
            } else {
              const result = encryptDecryptUtil.decryptData(
                response.data.data,
                keyinfo.syckey
              );
              const encResponse = JSON.parse(result);
              setLotoformEditId(guid);
              setLotoformEditResponse(encResponse);
              isView ? setviewmodal(true) : setmodal(true);
              setGridLoading(false);
            }
          }
        }
      })
      .catch((error: any) => {
        if (error.response.status == 401) {
          // localStorage.removeItem("islogin");
          navigate("/dashboard");
        }
        setGridLoading(false);
        SwalMessage(null, error?.message, "Ok", "error", false);
      })
      .finally(() => {
        setGridLoading(false);
      });
  };
  useEffect(() => {
    if (!modal && !viewmodal) {
      setLotoformEditResponse("");
      setLotoformEditId("");
    }
  }, [modal, viewmodal]);
  // console.log

  useEffect(() => {
    if (currentLoto === "LOTO_FORM") {
      const handler = setTimeout(() => {
        if (gridData?.length === 0 && viewFilterArray) {
          lockoutTagoutGrid(viewFilterArray);
          return
        } else if (viewFilterArray) {
          lockoutTagoutGrid(viewFilterArray);
          return
        }
      }, 400);
      return () => {
        clearTimeout(handler);
      };
    }
  }, [viewFilterArray, currentLoto]);

  const lockoutTagoutGrid = (viewFilterArray: filterObject) => {
    setGridLoading(true);
    lockoutTagoutService
      .getLockoutTagoutGridData(viewFilterArray)
      .then((response: any) => {
        if (response.data?.applicationStatusCode === 9001) {
          SwalMessage(null, response.data?.httpStatus, "Ok", "error", false);
          return;
        }
        if (response.data?.success === true) {
          let keyinfo = JSON.parse(localStorage.keyinfo);
          const result = encryptDecryptUtil.decryptData(
            response.data?.data,
            keyinfo?.syckey
          );
          const parseData = JSON.parse(result);
          // setTotalCount(parseData);
          // console.log(parseData.data)
          setGridData(parseData?.data);
          setGridLoading(false);
        } else {
          SwalMessage(null, response.data.errormsg, "Ok", "error", false);
          setGridLoading(false);
        }
      })
      .catch((error: any) => {
        if (error.response?.status == 401) {
          // localStorage.removeItem("islogin");
          navigate("/dashboard");
        } else if (error?.code == "ERR_NETWORK") {
          SwalMessage(null, error?.message, "Ok", "error", false);
          return;
        }
        setGridLoading(false);
        SwalMessage(null, error?.message, "Ok", "error", false);
      })
      .finally(() => {
        setGridLoading(false);
      });
  };

  // Pagination start
  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >();
  const pageChange = (event: any) => {
    const targetEvent = event.targetEvent as any;
    const take =
      targetEvent.value === "All" ? gridData.length : event.page.take;

    if (targetEvent.value) {
      setPageSizeValue(targetEvent.value);
    }
    setPage({
      ...event.page,
      take,
    });
  };
  // Pagination End

  // start action
  const renderaction = ({ content }: any) => {
    return (
      <td className="k-table-td">
        {/* <div className="text-center menu-hover">
          <a
            className="text-center"
            data-kt-menu-trigger="hover"
            data-kt-menu-placement="bottom-end"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </a>
          <div
            className="menu menu-sub menu-sub-dropdown  mt-1"
            data-kt-menu="true"
          >
            <div className="menu-item ">
              <a
                className="menu-link "
                onClick={() => handleedit(content, false)}
              >
                <MdOutlineEdit className="me-4" />
                <span className="menu-title ">Edit</span>
              </a>
              <a
                className="menu-link "
                onClick={() => handleedit(content, true)}
              >
                <FaRegEye className="me-4" />

                <span className="menu-title">View</span>
              </a>
              <a className="menu-link " onClick={() => handleDelete(content)}>
                <MdOutlineDeleteForever className="me-4" />
                <span className="menu-title">Delete</span>
              </a>
            </div>
          </div>
        </div> */}
        <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
          <Dropdown.Toggle
            as="span"
            className="fs-1 cursor-pointer ms-2"
          >
            <IoMdMore className="td-icon cursor-pointer" />
          </Dropdown.Toggle>
          <Dropdown.Menu align="end">
            <Dropdown.Item onClick={() => handleedit(content, false)}>
              <span className="fs-5">
                <MdOutlineEdit className="me-4" size={18} />
                Edit
              </span>
            </Dropdown.Item>
            <Dropdown.Item onClick={() => handleedit(content, true)}>
              <span className="fs-5">
                <FaRegEye className="me-4" size={16} />
                View
              </span>
            </Dropdown.Item>
            <Dropdown.Item onClick={() => handleDelete(content)}>
              <span className="fs-5">
                <MdOutlineDeleteForever className="me-4" size={18} />
                Delete
              </span>
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </td>
    );
  };
  // end action

  const moveToProcedure = (propertyId: any, equipmentId: any, departmentId: any) => {
    if (viewmodal) {
      setviewmodal(false);
    }
    // console.log(propertyId, equipmentId)
    dispatch(
      setProcedureVaultData({
        propertyId,
        equipmentId,
        isTransfer: false,
        departmentId
      })
    );

    // Re-direct Procedure Tab.
    const procedureVaultTab = document.querySelector(
      'a[href="#kt_tab_panes_5"]'
    ) as HTMLAnchorElement;
    if (procedureVaultTab) {
      procedureVaultTab.click();
    }
  };

  const customProcedureCell = (props: any) => {
    const { dataItem } = props;
    return (
      <td className="k-table-td text-center">
        <a
          onClick={() => moveToProcedure(dataItem.propertyId, dataItem.id, dataItem?.departmentId)}
          className="cursor-pointer"
        >
          {dataItem.completedProcedures + "/" + dataItem.totalProcedures}
        </a>
      </td>
    );
  };

  const customProgressCell = (props: any) => {
    const { dataItem } = props;
    return (
      <td className="k-table-td text-center">
        <a
          onClick={() => moveToProcedure(dataItem.propertyId, dataItem.id, dataItem?.departmentId)}
          className="cursor-pointer"
        >
          {dataItem.anyInProgressProcedures == false ? "No" : "Yes"}
        </a>
      </td>
    );
  };
  // start image
  const renderQrcode = (qrcode: any, id: any) => {
    return (
      <td className="text-center">
        <HiQrcode
          className="td-icon cursor-pointer user-image p-1"
          onClick={() => handleQRView(qrcode, id)}
        />
      </td>
    );
  };
  // end image

  //  start tooltip
  const renderTooltipCell = (props: any) => {
    const { dataItem, field, content } = props;
    if (field === "propertyName") {
      return (
        <td className="k-table-td">
          <span
            className="ellipsis-cell"
            title={
              permissiontype === "D"
                ? dataItem?.propertyName + " / " + dataItem?.departmentName
                : dataItem?.propertyName
            }
          >
            {permissiontype === "D"
              ? dataItem?.propertyName + " / " + dataItem?.departmentName
              : dataItem?.propertyName}
          </span>
        </td>
      );
    } else {
      return (
        <td className="k-table-td">
          <span className="ellipsis-cell" title={content}>
            {dataItem[field]}
          </span>
        </td>
      );
    }
  };
  // end tooltip
  const CustomIDCell = (props: any) => {
    const { dataItem } = props;
    return (
      <td onClick={() => setmodal(true)} className="k-table-td id_style">
        {dataItem.id}
      </td>
    );
  };

  const LotoFormDisplayId = (props: any) => {
    const { dataItem } = props;
    return (
      <td
        onClick={() => handleedit(dataItem?.id, true)}
        className="k-table-td id_style"
        title={dataItem?.id}
      >
        <div className="p-2">{dataItem.lotoformdisplayid}</div>
      </td>
    );
  };

  const handleRemoverFilter = (filter: any) => {
    const filterArray =
      viewFilterArray?.properties?.filter(
        (item: any) => item.value !== filter.value
      ) || [];
    setViewFilterArray({
      ...viewFilterArray,
      properties: filterArray,
    });
  };

  const excelexport = async () => {
    if (gridData.length < 1) {
      SwalMessage(null, "No Data Available", "Ok", "error", false);
      return;
    }
    setGridLoading(true);
    try {
      const date = new Date();
      await lockoutTagoutService
        .excelExport(null)
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute(
            "download",
            `LockoutTagout_${date.getFullYear() + "_" + date.getMonth() + "_" + date.getDate()
            }.xlsx`
          );
          document.body.appendChild(link);
          link.click();
          window.URL.revokeObjectURL(url);
        })
        .catch((e: any) => {
          console.info(e);
          if (e.response.status == 401) {
            navigate("/dashboard");
          }
          SwalMessage(null, e.message, "Ok", "error", false);
        });
    } catch (error) {
      console.error("Error downloading data:", error);
    } finally {
      setGridLoading(false);
    }
  };

  const handleFilterDelete = (key: string, valueToDelete: any) => {
    setViewFilterArray((prevFilter: any) => {
      if (Array.isArray(prevFilter[key as keyof filterObject])) {
        return {
          ...prevFilter,
          [key]: prevFilter[key as keyof filterObject].filter(
            (item: dropdownObject) => item.value !== valueToDelete
          ),
        };
      } else {
        return {
          ...prevFilter,
          [key]:
            prevFilter[key as keyof filterObject].value === valueToDelete
              ? {}
              : prevFilter[key],
        };
      }
    });
  };

  return (
    <>
      {/* {gridLoading && <Spinner />} */}
      <div className="row pageheader mb-7">
        <div className=" col-xl-6 col-lg-4 col-sm-6   mt-auto mb-auto">
          <h1 className="page-title mobile-margin mb-0">LOTO Form</h1>
        </div>
        <div className=" col-xl-6 col-lg-8 col-sm-6 mt-auto mb-auto text-end">
          <input
            type="search"
            autoFocus
            // "search-box" classname for search input visiable
            className="form-control d-none  mobile-margin"
            placeholder="Search..."
            value={viewFilterArray.search}
            onChange={(e: any) =>
              setViewFilterArray({ ...viewFilterArray, search: e.target.value })
            }
          />
          <button
            // href="javascript:void(0)"
            className="btn rx-btn ms-3 mobile-margin mb-lg-3"
            onClick={() => setfiltermodal(true)}
          >
            <IoFilter className="btn-icon-custom" />
            Filter
          </button>
          <button className="btn rx-btn ms-3 mobile-margin mb-lg-3" onClick={excelexport}>
            <FaFileDownload className="btn-icon-custom" />
            Export
          </button>
          <button className="btn rx-btn ms-3 mobile-margin mb-lg-3" onClick={() => setmodal(true)}>
            <IoMdAdd className="btn-icon-custom" />
            Add
          </button>
        </div>
      </div>
      <div className="card mt-0">
        <div className="card-body p-0">
          <div className="table_div saftyform-table" style={{ width: "100%" }}>
            <Tooltip position="bottom" anchorElement="target">
              <Grid
                key={refreshKey}
                data={
                  Array.isArray(gridData) && gridData.length > 0
                    ? orderBy(gridData, sort).slice(
                      page.skip,
                      page.skip + page.take
                    )
                    : []
                }
                skip={page.skip}
                take={page.take}
                total={gridData?.length}
                pageable={{
                  buttonCount: 4,
                  pageSizes: [5, 10, 15, "All"],
                  pageSizeValue: pageSizeValue,
                  // responsive:true,
                }}
                onPageChange={pageChange}
                sortable={true}
                sort={sort}
                onSortChange={(e: any) => {
                  setSort(e.sort);
                }}
              >
                <GridToolbar>
                  {Object.entries(viewFilterArray).map(([key, value]) => (
                    <React.Fragment key={key}>
                      {Array.isArray(value)
                        ? value.map((item, index) => (
                          <span key={index} className="custom-tag">
                            {item.label}
                            <i
                              className="fas fa-times close_icon ms-2 cursor-pointer d-flex align-items-center"
                              onClick={() =>
                                handleFilterDelete(key, item.value)
                              }
                            ></i>
                          </span>
                        ))
                        : (value as dropdownObject)?.label && (
                          <span className="custom-tag">
                            {(value as dropdownObject).label}
                            <i
                              className="fas fa-times close_icon ms-2 cursor-pointer d-flex align-items-center"
                              onClick={() =>
                                handleFilterDelete(
                                  key,
                                  (value as dropdownObject).value
                                )
                              }
                            ></i>
                          </span>
                        )}
                    </React.Fragment>
                  ))}
                </GridToolbar>
                <Column
                  title="QR Code"
                  headerClassName="center-header"
                  width="90px"
                  cell={(props) =>
                    renderQrcode(props.dataItem.qrCodeBase64, props.dataItem.id)
                  }
                />
                <Column
                  title="ID"
                  field="lotoformdisplayid"
                  width="82px"
                  cell={(props) =>
                    LotoFormDisplayId({
                      ...props,
                      content: props.dataItem.lotoformdisplayid,
                    })
                  }
                  headerClassName="center-header"
                />
                <Column
                  title="Equipement ID"
                  field="equipmentId"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.equipmentId,
                    })
                  }
                />
                <Column
                  title={`Property ${permissiontype === "D" ? "/ Department" : "Name"
                    }`}
                  field="propertyName"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.propertyName,
                    })
                  }
                />
                <Column
                  title="Assigned Lockbox"
                  field="lockBoxNumber"
                  width="160px"
                  cell={(props) =>
                    renderTooltipCell({
                      ...props,
                      content: props.dataItem.lockBoxNumber,
                    })
                  }
                />
                <Column
                  title="Assigned Locks"
                  field="noOfLocks"
                  // width="120px"
                  headerClassName="center-header"
                  className="text-center"
                />
                <Column
                  title="Started Procedures"
                  field="startedprocedure"
                  // width="150px"
                  headerClassName="center-header"
                  className="text-center"
                  cell={(props) =>
                    customProcedureCell({
                      ...props,
                      content: props.dataItem.anyInProgressProcedures,
                    })
                  }
                />
                <Column
                  title="In Progress"
                  field="anyInProgressProcedures"
                  width="130px"
                  headerClassName="center-header"
                  className="text-center"
                  cell={(props) =>
                    customProgressCell({
                      ...props,
                      content: props.dataItem.anyInProgressProcedures,
                    })
                  }
                />
                <Column
                  title="Action"
                  width="80px"
                  cell={(props) =>
                    renderaction({ ...props, content: props.dataItem?.id })
                  }
                />
              </Grid>
            </Tooltip>
          </div>
        </div>
      </div>
      {/* <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover={false}
        theme="light"
      /> */}
      <Print_QRCode_Modal
        qrmodal={qrmodal}
        setqrmodal={setqrmodal}
        qrBase64={qrBase64}
        setGridLoading={setGridLoading}
      />
      <View_LockOut_Modal
        viewmodal={viewmodal}
        setviewmodal={setviewmodal}
        lotoformEditId={lotoformEditId}
        lotoformEditResponse={lotoformEditResponse}
        setGridLoading={setGridLoading}
        moveToProcedure={moveToProcedure}
        PropertyData={PropertyData}
      />
      <Add_Logout_Togout_Card_Modal
        modal={modal}
        setmodal={setmodal}
        lotoformEditId={lotoformEditId}
        lotoformEditResponse={lotoformEditResponse}
        setGridLoading={setGridLoading}
        gridLoading={gridLoading}
        lockoutTagoutGrid={lockoutTagoutGrid}
        viewFilterArray={viewFilterArray}
        PropertyData={PropertyData}
      />
      <Logout_Tagout_Filter_Modal
        setfiltermodal={setfiltermodal}
        filtermodal={filtermodal}
        lockoutTagoutGrid={lockoutTagoutGrid}
        setViewFilterArray={setViewFilterArray}
        viewFilterArray={viewFilterArray}
        setGridLoading={setGridLoading}
        PropertyData={PropertyData?.properties}
      />
    </>
  );
}

export default Logout_Tagout;
