import React from "react";
import { SurveyQuestionResponseDto } from "../../apis/type";
import QuestionR<PERSON>ponseViewer from "../survey/components/addQuestions/QuestionResponseViewer";
import { ISurveyQuestionListItem } from "../../apis/type";
import { Form, Formik } from "formik";

interface Props {
  question: SurveyQuestionResponseDto;
  questionNumber: number;
  answer: any;
  onAnswerChange: (answer: any) => void;
  onUploadingStateChange?: (isUploading: boolean) => void;
}

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachments: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
  }>;
  branchingAnswer?: {
    answers: string[];
    comment: string;
    attachments: Array<{
      attachmentId: string;
      attachmentUrl: string;
      attachmentType: string;
    }>;
  };
}

const SurveyQuestionStep: React.FC<Props> = ({
  question,
  questionNumber,
  answer,
  onAnswerChange,
  onUploadingStateChange,
}) => {
  // Convert SurveyQuestionResponseDto to ISurveyQuestionListItem format
  const convertedQuestion: ISurveyQuestionListItem = {
    id: question.id,
    surveyId: question.surveyId,
    questionText: question.questionText,
    responseType: question.responseType,
    options: question.options || [],
    rattingIcon: question.rattingIcon,
    isRequired: question.isRequired,
    allowAttachment: question.allowAttachment,
    attachmentType: question.attachmentType,
    allowComment: question.allowComment,
    comment: "",
    autoTicketGeneration: question.autoTicketGeneration,
    allowBranching: question.allowBranching,
    questionOrder: question.questionOrder,
    branchingQuestion: question.branchingQuestion
      ? {
          id: question.branchingQuestion.id,
          surveyId: question.branchingQuestion.surveyId,
          questionText: question.branchingQuestion.questionText,
          responseType: question.branchingQuestion.responseType,
          options:
            question.branchingQuestion.options?.map((option) => ({
              optionText: option,
              optionValue: option,
              isTicketRequired: false,
            })) || [],
          // rattingIcon: question.branchingQuestion.rattingIcon || "",
          // isRequired: question.branchingQuestion.isRequired || false,
          allowAttachment: question.branchingQuestion.allowAttachment,
          attachmentType: question.branchingQuestion.attachmentType || [],
          allowComment: question.branchingQuestion.allowComment || false,
          // comment: question.branchingQuestion.comment || "",
          // autoTicketGeneration:
            // question.branchingQuestion.autoTicketGeneration || false,
          // allowBranching: question.branchingQuestion.allowBranching || false,
          // questionOrder: question.branchingQuestion.questionOrder || 0,
        }
      : null,
  };

  // Handle answer changes from QuestionResponseViewer
  const handleAnswerUpdate = (updatedAnswer: any) => {
    // The QuestionResponseViewer will provide the answer in the format we need
    onAnswerChange(updatedAnswer);
  };

  return (
    <div className="survey-question-step">
      <Formik initialValues={{}} onSubmit={() => {}}>
        <Form>
          <QuestionResponseViewer
            question={convertedQuestion}
            questionNumber={questionNumber}
            readOnly={false}
            onAnswerChange={handleAnswerUpdate}
            initialAnswer={answer}
            onUploadingStateChange={onUploadingStateChange}
          />
        </Form>
      </Formik>
    </div>
  );
};

export default SurveyQuestionStep;
