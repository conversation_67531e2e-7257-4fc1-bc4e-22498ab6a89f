import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import React, { useEffect, useState } from "react";
import { IoSettingsOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import { ClipLoader } from "react-spinners";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import { getImage } from "../../utils/CommonUtils";
import { mock_inspection_template_table_data } from "../../../mockData";
import { Badge, Dropdown } from "react-bootstrap";
import { IoMdMore } from "react-icons/io";
import { FaRegEye } from "react-icons/fa";
import {
  MdOutlineEdit,
  MdOutlineLocalPrintshop,
  MdOutlineRemoveRedEye,
} from "react-icons/md";
import { FilterSvg, SortingSvg } from "../../utils/SvgUtils";
import usersvg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import {
  GetAllInspectionTemplateRes,
  useGetInspectionListMutation,
} from "../../apis/inspectionListAPI";
import FiltersModal from "../Masters/Ticktes/Modal/FiltersModal";
import SortingModal from "../Masters/Ticktes/Modal/SortingModal";
import { processApiResponse } from "../../utils/helper";
import SearchBar from "../../shared/component/SearchBar";

interface InspectionProps {
  extraPayload?: Record<string, any>;
  hideManageTemplate?: boolean;
}

const Inspection: React.FC<InspectionProps> = ({ extraPayload, hideManageTemplate }) => {
  const initialSort: Array<any> = [{ field: "", dir: "asc" }];
  const [sort, setSort] = useState(initialSort);
  const [filterCount, setFilterCount] = useState<number>(0);
  const [showFilterModal, setshowFilterModal] = useState<boolean>(false);
  const [showSortingModal, setshowSortingModal] = useState<boolean>(false);
  const [tickettype, settickettype] = useState<any[]>([]);
  const [ticketstatus, setticketstatus] = useState<any[]>([]);

  const [searchKey, setSearhKey] = useState<string>("");
  const [inspectionRes, setInspectionRes] =
    useState<GetAllInspectionTemplateRes | null>(null);

  const [getInspectionList, { data, isLoading }] =
    useGetInspectionListMutation();

  const gridTotalCount = inspectionRes?.data?.totalCount || 0;
  const navigate = useNavigate();

  // Pagination start
  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >(initialDataState.take);
  let keyinfo = JSON.parse(localStorage.keyinfo);
  const { setLabels } = useBreadcrumbContext();

  useEffect(() => {
    setLabels([{ path: "", state: {}, breadcrumb: "Inspections" }]);
  }, []);

  const pageSizesArray = [
    { label: "5", value: 5 },
    { label: "10", value: 10 },
    { label: "15", value: 15 },
    { label: "All", value: gridTotalCount },
  ];

  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );

  const pageChange = (event: any) => {
    const { skip, take } = event.page;
    const targetEvent = event.targetEvent as any;
    const newTake = targetEvent.value == "All" ? gridTotalCount : take;
    const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

    setPage({ skip, take: newTake });
    setPageSizeValue(newPageSizeValue);
  };

  // useEffect(() => {
  //   const payload: any = {
  //     page: 1,
  //     size: 10,
  //     search: "",
  //     // sortingColumns: [
  //     //   {
  //     //     sortOrder: 0,
  //     //     columnName: "",
  //     //   },
  //     // ],
  //     ...(extraPayload || {}),
  //   };
  //   getInspectionList(payload);
  // }, []);

  const renderUserandimage = (props: any) => {
    const { dataItem, content } = props;

    return (
      <td className={`k-table-td cursor-pointer `}>
        <img
          src={
            dataItem?.imageUrl
              ? getImage(dataItem?.inspectedByImageUrl)
              : usersvg
          }
          className={`rounded-circle`}
          width={"32px"}
          height={"32px"}
        />
        <span
          className={`user-name`}
          style={{
            display: "inlineBlock",
            paddingLeft: "10px",
            verticalAlign: "middle",
            lineHeight: "32px",
          }}
          title={content}
        >
          {content}
        </span>
      </td>
    );
  };

  const renderTooltipCell = (props: any) => {
    const { content } = props;
    return (
      <td className="k-table-td">
        <span className="ellipsis-cell cursor-pointer " title={content}>
          {content}
        </span>
      </td>
    );
  };

  const renderTitle = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <span
          className="ellipsis-cell cursor-pointer "
          title={content.inspectionTitle}
          onClick={() =>
            navigate(
              `/inspections/inspections-details?id=${content?.inspectionId}`
            )
          }
        >
          {content.inspectionTitle}
        </span>
      </td>
    );
  };

  // Start Render Priority
  const renderPriority = (props: any) => {
    const { dataItem } = JSON.parse(JSON.stringify(props));
    const priority = dataItem?.inspectionTemplatePriority?.toLowerCase();

    const background =
      priority === "low"
        ? "success"
        : priority === "high"
        ? "warning"
        : priority === "medium"
        ? "primary"
        : priority === "urgent"
        ? "danger"
        : "";

    return (
      <td>
        <Badge
          bg={background}
          className="text-white ellipsis-cell text-capitalize"
          title={priority}
        >
          {priority}
        </Badge>
      </td>
    );
  };
  // End Render Priority

  // Start Handle Status
  const renderStatus = (props: any) => {
    const { dataItem } = JSON.parse(JSON.stringify(props));

    const status = dataItem?.inspectionStatus;
    const background =
      status?.toLowerCase() === "completed"
        ? "success"
        : status?.toLowerCase() === "in progress"
        ? "warning"
        : status?.toLowerCase() === "completed & flag"
        ? "primary"
        : status?.toLowerCase()?.includes("%")
        ? "secondary"
        : "";

    return (
      <td className="k-table-td">
        <Badge
          bg={background}
          className="text-white ellipsis-cell text-capitalize"
          title={status}
        >
          {status}
        </Badge>
      </td>
    );
  };
  // End Handle Status

  const renderaction = (props: any) => {
    const content = props.dataItem;
    return (
      <>
        <td className="k-table-td">
          <Dropdown className="new-chat-btn" style={{ position: "static" }}>
            <Dropdown.Toggle as="span" className="fs-1 cursor-pointer ms-2">
              <IoMdMore className="td-icon cursor-pointer" />
            </Dropdown.Toggle>
            <Dropdown.Menu align="end">
              <Dropdown.Item
                onClick={() =>
                  navigate(
                    `/inspections/inspections-details?id=${content?.inspectionId}`
                  )
                }
              >
                <span className="fs-5">
                  <MdOutlineRemoveRedEye className="me-4" />
                  View Report
                </span>
              </Dropdown.Item>
              <Dropdown.Item>
                <span className="fs-5">
                  <MdOutlineLocalPrintshop className="me-4" />
                  Print Report
                </span>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </td>
      </>
    );
  };

  const fetchTemplate = async () => {
    const pageNumber = Math.floor(page.skip / page.take) + 1;
    const payload = {
      page: pageNumber,
      size: page.take,
      search: searchKey,
      // sortingColumns: [],
      ...(extraPayload || {}),
    };
    await getInspectionList(payload)
      .unwrap()
      .then((res) => {
        processApiResponse({
          res,
          onSuccess: () => {
            setInspectionRes(res);
          },
        });
      })
      .catch((err) => {});
  };

  //Get Inspection List
  useEffect(() => {
    fetchTemplate();
  }, [searchKey, page]); //Add dep

  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : null;

  const isValidUserPermission =
    userinfo?.isfullaccess?.toString() === "1" &&
    userinfo?.displayusertype === "COMPANY_ADMIN";

  return (
    <div>
      {isLoading && spinner}
      <div className="row pageheader mb-7">
        <div className=" col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto">
          <div className="row">
            <div className=" col-xl-6 col-lg-6 col-sm-6 col-6">
              <SearchBar
                type="text"
                onChange={(e) => setSearhKey(e.target.value)}
              />
            </div>
            {/* <div className=" col-xl-6 col-lg-6 col-sm-6 col-6">
              <div className="d-flex gap-3 align-items-center settingSlider">
                <div
                  className="user-image text-center mt-3 cursor-pointer "
                  onClick={() => setshowSortingModal(true)}
                >
                  <SortingSvg width="16" height="16" className="svgicon" />
                </div>
                <div
                  className="user-image filter-div text-center mt-3 cursor-pointer"
                  onClick={() => setshowFilterModal(true)}
                >
                  <FilterSvg width="16" height="16" className="svgicon" />
                  <div className="filter-count">
                    <span>{filterCount}</span>
                  </div>
                </div>
              </div>
            </div> */}
          </div>
        </div>
        {isValidUserPermission && !hideManageTemplate && (
          <div className=" col-xl-6 col-lg-6 col-sm-6 text-end">
            <div className="d-flex justify-content-end">
              <div className=" p-0">
                <Link
                  to={"/inspections/manage-templates"}
                  className="btn  rx-btn ms-3 mt-3 mt-md-0"
                >
                  <IoSettingsOutline className="btn-icon-custom" />
                  Manage Templates
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="card mt-0 ">
        <div className="card-body p-0" style={{ margin: "0.8px" }}>
          <div className="table_div" style={{ width: "100%" }}>
            <Tooltip position="bottom" anchorElement="target">
              {mock_inspection_template_table_data && (
                <Grid
                  data={orderBy(inspectionRes?.data?.data || [], sort)}
                  skip={page.skip}
                  take={page.take}
                  total={gridTotalCount}
                  pageable={{
                    buttonCount: 4,
                    pageSizes: pageSizesArray.map((size: any) => size.label),
                    pageSizeValue: pageSizeValue,
                  }}
                  onPageChange={pageChange}
                  sortable
                  sort={sort}
                  onSortChange={(e: any) => setSort(e.sort)}
                >
                  {/* ID */}
                  <Column title="ID" field="id" width={"120px"} />

                  <Column
                    title="Title"
                    field="inspectionTitle"
                    cell={(props) =>
                      renderTitle({
                        ...props,
                      })
                    }
                  />

                  {/* Property */}
                  <Column
                    title="Property/Department"
                    field="property"
                    cell={(props) =>
                      renderTooltipCell({
                        ...props,
                        content:
                          props.dataItem.departmentName ||
                          props.dataItem.propertyName,
                      })
                    }
                  />

                  {/* Inspected by Column */}
                  <Column
                    title="Inspected by"
                    field="inspectedByName"
                    cell={(props) =>
                      renderUserandimage({
                        ...props,
                        content: props.dataItem.inspectedByName,
                      })
                    }
                  />

                  <Column
                    title="Inspection For"
                    field="inspectionFor"
                    cell={(props) =>
                      renderTooltipCell({
                        ...props,
                        content: props.dataItem.inspectionFor,
                      })
                    }
                  />

                  {/* Priority */}
                  <Column
                    title="Priority"
                    field="inspectionTemplatePriority"
                    headerClassName="center-header"
                    width="90px"
                    cell={(props) =>
                      renderPriority({
                        ...props,
                      })
                    }
                  />

                  {/* Status */}
                  <Column
                    title="Status"
                    field="inspectionStatus"
                    width="120px"
                    cell={(props) =>
                      renderStatus({
                        ...props,
                      })
                    }
                  />

                  {/* Action */}
                  <Column
                    title="Action"
                    width="80px"
                    cell={(props) =>
                      renderaction({
                        ...props,
                      })
                    }
                  />
                </Grid>
              )}
            </Tooltip>
          </div>
        </div>
      </div>
      {/* <FiltersModal
        showFilterModal={showFilterModal}
        setShowFilterModal={setshowFilterModal}
        settickettype={settickettype}
        setticketstatus={setticketstatus}
        setisGridLoading={setisGridLoading}
        setFilterCount={setFilterCount}
        setfilterData={setFilter}
        filterDatas={Filter}
        setAddTicketValue={setAddTicketValue}
      />
      <SortingModal
        setShowSortingModal={setshowSortingModal}
        ShowSortingModal={showSortingModal}
        setSortingData={setFilter}
        sortingData={Filter}
      /> */}
    </div>
  );
};

export default Inspection;
