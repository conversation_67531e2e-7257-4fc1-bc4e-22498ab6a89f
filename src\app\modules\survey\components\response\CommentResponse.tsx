import React from 'react'

interface CommentResponseProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  readOnly?: boolean
}

const CommentResponse: React.FC<CommentResponseProps> = ({
  value = '',
  onChange,
  placeholder = 'Enter your comment...',
  readOnly,
}) => {
  return (
    <textarea
      className='form-control'
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      placeholder={placeholder}
      rows={3}
      readOnly={readOnly}
    />
  )
}

export default CommentResponse