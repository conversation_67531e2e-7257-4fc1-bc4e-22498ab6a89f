import { ChangeEvent, useEffect, useState } from "react";
import { Modal, Row, Col, Form, Card } from "react-bootstrap";
import { BsSave } from "react-icons/bs";
import { SlClose, SlCloudUpload, SlEye, SlTrash } from "react-icons/sl";
import { useNavigate } from "react-router-dom";
import Dropzone from "react-dropzone";
import { FaUpload } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import SwalMessage from "../../../common/SwalMessage";
import { shiftManagamentService } from "./shiftManagement.helper";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import SingleSelectDropdown from "../../../common/SingleSelectDropdown";
import { Calendar } from "primereact/calendar";
import moment from 'moment';
import TimePicker from 'react-time-picker';
import 'react-time-picker/dist/TimePicker.css';
import 'react-clock/dist/Clock.css';

function AddShiftManagementModal({
    openAddShiftModal,
    setOpenAddShiftModal,
    setGridLoading,
    shiftGrid,
    shiftId,
    setShiftId,
    editData,
    setEditData
}: any) {
    const navigate = useNavigate();
    const [shiftName, setShiftName] = useState<string>("");
    const [startTime, setStartTime] = useState<any>("");
    const [endTime, setEndTime] = useState<any>("");
    const [daysData, setDaysData] = useState<any>([]);
    const [repeatData, setRepeatData] = useState<any>([]);
    // const [selectedDays, setSelectedDays] = useState<any>([]);
    const [selectedRepeatTime, setSelectedRepeatTime] = useState<any>({});
    const [active, setActive] = useState<boolean>(false);
    const [errors, setErrors] = useState<any>({});

    // day of week onchange
    // const handleDayChange = (value: any) => {
    //     setSelectedDays((prev: any[]) =>
    //         prev.some((day) => day.value === value.value)
    //             ? prev.filter((day) => day.value !== value.value)
    //             : [...prev, value]
    //     );
    // };

    // form validation
    const validatePayload = () => {
        const newErrors: any = {};

        if (!shiftName) {
            newErrors.shiftName = "Please Enter Shift Name.";
        }

        if (!startTime) {
            newErrors.startTime = "Please Enter Start Time.";
        }

        if (!endTime) {
            newErrors.endTime = "Please Enter End Time.";
        }
        // if (selectedRepeatTime?.value !== "5953752b4664456f73714b586762787955774a6d6d513d3d") {
        //     if (selectedDays?.length === 0) {
        //         newErrors.selectedDays = "Please Select Day.";
        //     }
        // }

        if (!selectedRepeatTime?.value) {
            newErrors.selectedRepeatTime = "Please Select Repeat Time.";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    //edit data 
    // console.log("editData", editData)
    useEffect(() => {
        if (editData && openAddShiftModal && repeatData?.length > 0 && daysData.length > 0) {
            // const Days = editData?.weekOfDays || [];
            // const selectedDays = daysData.filter((day: any) =>
            //     Days.includes(day.value)
            // );
            setShiftName(editData?.shiftName);

            const startTimeDate = moment(editData?.shiftStartTime, "hh:mm A").toDate();
            const endTimeDate = moment(editData?.shiftEndTime, "hh:mm A").toDate();
            setStartTime(startTimeDate);
            setEndTime(endTimeDate);
            setSelectedRepeatTime(repeatData?.find((item: any) => item?.value === editData?.shiftRepeatPattern));
            // setSelectedDays(selectedDays || []);
            setActive(editData?.active == 1 ? true : false);
        }
    }, [editData, openAddShiftModal, repeatData, daysData]);

    // get shift dropdown
    // const getShiftDropdown = () => {
    //     setGridLoading(true);
    //     let keyinfo = JSON.parse(localStorage.ke9yinfo);
    //     shiftManagamentService
    //         .getshiftsfordropdown()
    //         .then((response) => {
    //             if (response.status == 200) {
    //                 if (response.data.success == true) {
    //                     const result = encryptDecryptUtil.decryptData(
    //                         response.data.data,
    //                         keyinfo.syckey
    //                     );
    //                     setGridLoading(false);
    //                     const encResponse = JSON.parse(result);
    //                     console.log("encResponse", encResponse)
    //                 } else {
    //                     SwalMessage(null, response.data.errormsg, "Ok", "error", false);
    //                 }
    //             }
    //         })
    //         .catch((error: any) => {
    //             if (error.response.status == 401) {
    //                 setGridLoading(false);
    //                 // localStorage.removeItem("islogin");
    //                 navigate("/admindashboard");
    //                 // navigate(0);
    //             }
    //             setGridLoading(false);
    //             SwalMessage(null, error.response.data.message, "Ok", "error", false);
    //         })
    //         .finally(() => setGridLoading(false));
    // }
    // useEffect(() => {
    //     if (openAddShiftModal) {
    //         setTimeout(() => {
    //             getShiftDropdown();
    //         }, 500);
    //     }
    // }, [openAddShiftModal]);

    // get Day of week
    const getDayOfWeek = () => {
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        shiftManagamentService
            .getdayofweek()
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        setGridLoading(false);
                        const encResponse = JSON.parse(result);
                        // console.log("encResponse", encResponse)
                        setDaysData(encResponse);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error: any) => {
                if (error.response.status == 401) {
                    setGridLoading(false);
                    // localStorage.removeItem("islogin");
                    navigate("/admindashboard");
                    // navigate(0);
                }
                setGridLoading(false);
                SwalMessage(null, error.response.data.message, "Ok", "error", false);
            })
            .finally(() => setGridLoading(false));
    }
    useEffect(() => {
        if (openAddShiftModal) {
            setTimeout(() => {
                getDayOfWeek();
            }, 500);
        }
    }, [openAddShiftModal]);

    // get Repeat Patterns
    const getRepeatPatterns = () => {
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        shiftManagamentService
            .getrepeatpatterns()
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        setGridLoading(false);
                        const encResponse = JSON.parse(result);
                        // console.log("encResponse", encResponse)
                        setRepeatData(encResponse);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error: any) => {
                if (error.response.status == 401) {
                    setGridLoading(false);
                    // localStorage.removeItem("islogin");
                    navigate("/admindashboard");
                    // navigate(0);
                }
                setGridLoading(false);
                SwalMessage(null, error.response.data.message, "Ok", "error", false);
            })
            .finally(() => setGridLoading(false));
    }
    useEffect(() => {
        if (openAddShiftModal) {
            setTimeout(() => {
                getRepeatPatterns();
            }, 500);
        }
    }, [openAddShiftModal]);

    // Handle Submit
    const handleSubmit = () => {
        if (!validatePayload()) {
            return
        }
        setGridLoading(true);
        const payload = {
            shiftId: shiftId ? shiftId : "",
            shiftName: shiftName,
            // startTime: startTime,
            // endTime: endTime,
            startTime: moment(startTime, "HH:mm").format("hh:mm A"),
            endTime: moment(endTime, "HH:mm").format("hh:mm A"),
            repeatTime: selectedRepeatTime?.value,
            active: active == true ? 1 : 0,
            // days: selectedDays?.map((item: any) => item?.value),
            companyId: [],
            isAllSelected: 0
        }
        console.log("savePayload", payload)
        shiftManagamentService
            .generateshift(payload)
            .then(async (response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        setGridLoading(false);
                        const confirmed = await SwalMessage(
                            null,
                            response.data.errormsg,
                            "Ok",
                            "success",
                            false
                        );
                        if (confirmed) {
                            setOpenAddShiftModal(false);
                            setGridLoading(false);
                            shiftGrid()
                        }
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error: any) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error.response.data.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    }

    // empty state
    useEffect(() => {
        if (!openAddShiftModal) {
            setShiftName("");
            setStartTime("");
            setEndTime("");
            // setSelectedDays([]);
            setSelectedRepeatTime({});
            setActive(false);
            setErrors({});
            setShiftId("");
            setEditData({});
        }
    }, [openAddShiftModal])

    return (
        <>
            <Modal
                scrollable={true}
                show={openAddShiftModal}
                className="modal-right modal-right-small p-0"
            >
                <Modal.Header className="border-0 p-0">
                    <Row>
                        <Col sm={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">{shiftId ? "Edit" : "Add"} Shift Management</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setOpenAddShiftModal(false)}>
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="mt-5 p-0">
                    <Row>
                        <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Shift Name <span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                autoFocus
                                className="form-control"
                                placeholder="Enter Shift Name"
                                value={shiftName}
                                onChange={(e: any) => setShiftName(e.target.value)}
                            />
                            {errors.shiftName && (
                                <div className="text-danger">{errors.shiftName}</div>
                            )}
                        </Col>
                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Start Time <span className="text-danger">*</span>
                            </span>
                            <TimePicker
                                onChange={setStartTime}
                                value={startTime}
                                format="hh:mm a"
                                clearIcon={null}
                                disableClock={true}
                                className="form-control"
                            />
                            {/* <Calendar
                                value={startTime}
                                onChange={(e: any) => setStartTime(e.target.value)}
                                timeOnly
                                hourFormat="12"
                                placeholder="hh:mm AM/PM"
                            /> */}
                            {errors.startTime && (
                                <div className="text-danger">{errors.startTime}</div>
                            )}
                        </Col>
                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                End Time <span className="text-danger">*</span>
                            </span>
                            <TimePicker
                                onChange={setEndTime}
                                value={endTime}
                                format="hh:mm a"
                                clearIcon={null}
                                disableClock={true}
                                className="form-control"
                            />
                            {/* <Calendar
                                value={endTime}
                                onChange={(e: any) => setEndTime(e.target.value)}
                                timeOnly
                                hourFormat="12"
                                placeholder="hh:mm AM/PM"
                            /> */}
                            {errors.endTime && (
                                <div className="text-danger">{errors.endTime}</div>
                            )}
                        </Col>
                        <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Repeat Time <span className="text-danger">*</span>
                            </span>
                            <SingleSelectDropdown
                                data={repeatData}
                                getter={selectedRepeatTime}
                                setter={setSelectedRepeatTime}
                                placeholder="Select Repeat Time"
                            />
                            {errors.selectedRepeatTime && (
                                <div className="text-danger">{errors.selectedRepeatTime}</div>
                            )}
                        </Col>
                        {/* {selectedRepeatTime?.value !== "5953752b4664456f73714b586762787955774a6d6d513d3d" && (
                            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3 mobile-margin">
                                <span className="form-label">
                                    Week Of Day <span className="text-danger">*</span>
                                </span>
                                <div className="day-round mt-2">
                                    {daysData.map((day: any) => (
                                        <div key={day.value} className="day-wrapper">
                                            <input
                                                type="checkbox"
                                                id={day.label}
                                                value={day.value}
                                                checked={selectedDays.some((d: any) => d.value === day.value)}
                                                onChange={() => handleDayChange(day)}
                                            />
                                            <label htmlFor={day.label} />
                                            <span className="day-label">{day.label}</span>
                                        </div>
                                    ))}
                                </div>
                                {errors.selectedDays && (
                                    <div className="text-danger">{errors.selectedDays}</div>
                                )}
                            </Col>
                        )} */}
                    </Row>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 d-block">
                    <Row className="align-items-center">
                        <Col sm={3}>
                            {shiftId && (
                                <>
                                    <input
                                        type="checkbox"
                                        className="form-check-input me-3 mt-1"
                                        name="isdefault"
                                        checked={active}
                                        onChange={(e) => setActive(e.target.checked)}
                                    />
                                    <span className="fs-5" >
                                        Active
                                    </span>
                                </>
                            )}
                        </Col>
                        <Col sm={9} className="text-end">
                            <span className="btn rx-btn" onClick={handleSubmit}>
                                Save
                            </span>
                        </Col>
                    </Row>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default AddShiftManagementModal;
