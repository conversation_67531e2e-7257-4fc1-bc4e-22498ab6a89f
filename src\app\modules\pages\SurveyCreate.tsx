import React, { useEffect, useState } from "react";
import { Col, Row } from "react-bootstrap";
import { Stepper } from "@progress/kendo-react-layout";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import SurveyConfiguration from "../survey/createSurvey/SurveyConfiguration";
import GeneralSetupFormWrapper from "../survey/createSurvey/GeneralSetupFormWrapper";
import QuestionSetup from "../survey/createSurvey/QuestionSetup";
import { useSearchParams } from "react-router-dom";
import { SurveyType } from "../../apis/type";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";

const SurveyCreate: React.FC = () => {
  const [activeStep, setActiveStep] = useState(1);
  const { setLabels } = useBreadcrumbContext();
  const { surveyId } = useSurveyUtil();

  const [searchParams] = useSearchParams();
  const surveyType =
    (searchParams.get("surveyType")?.toUpperCase() as SurveyType) || "GLOBAL";

  // Stepper configuration
  const stepperList = [
    { label: "General Setup" },
    { label: "Add Questions" },
    { label: "Survey Configuration" },
  ];

  useEffect(() => {
    setLabels([
      { path: "/surveys", state: {}, breadcrumb: "Survey" },
      { path: "", state: {}, breadcrumb: `Create ${surveyType} Survey Template` },
    ]);
  }, [setLabels]);

  const handleNext = () => {
    if (activeStep < stepperList.length - 1) {
      setActiveStep(activeStep + 1);
    }
  };

  const handlePrevious = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleFinalStep = () => {
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <GeneralSetupFormWrapper
            handleNext={handleNext}
            handleBack={handlePrevious}
          />
        );
      case 1:
        return (
          <QuestionSetup handleNext={handleNext} handleBack={handlePrevious} />
        );
      case 2:
        return (
          <SurveyConfiguration
            handleNext={handleNext}
            handleBack={handlePrevious}
          />
        );
      default:
        return (
          <GeneralSetupFormWrapper
            handleNext={handleFinalStep}
            handleBack={handlePrevious}
          />
        );
    }
  };



  return (
    <div className="h-100">
      <Row>
        <Col xs={12}>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h1 className="m-0">Survey Template</h1>
          </div>
        </Col>
      </Row>
      {/* Stepper */}
      {stepperList.length > 0 && (
        <Row
          className=""
          style={{
            marginTop: 10,
            pointerEvents: "none",
            paddingTop: 1,
          }}
        >
          <Col xs={12}>
            <Stepper
              className="rx-stepper"
              value={activeStep}
              onChange={() => {}}
              items={stepperList}
              onFocus={() => console.log("Stepper focused")}
            />
          </Col>
        </Row>
      )}
      {renderStepContent()}
    </div>
  );
};

export default SurveyCreate;
