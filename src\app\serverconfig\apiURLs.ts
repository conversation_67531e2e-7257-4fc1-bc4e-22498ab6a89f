export const APIs = {
  // Inspection Link Items API endpoints
  INSPECTION_LINK_ITEMS: {
    GET_LINKED_TICKETS: "resourceapi/inspectionLinkItems/getLinkedTickets",
    LINK_TICKETS: "resourceapi/inspectionLinkItems/linkTickets",
    LINK_WEB_LINKS: "resourceapi/inspectionLinkItems/linkWebLinks",
    DELETE_LINK_WEBLINK: "resourceapi/inspectionLinkItems/deleteLinkWeblink",
  },
  NO_HEADERS: {
    securityDetails: "api/securitydetails",
    escalationcompleted: "api/escalation/completed",
  },
  PARTIAL_HEADERS: {
    signin: "api/signin",
    forgotPassword: "api/settemporarypassword",
    getStarted: "getstarted/api/verifypasscode",
    termsandconditions: "getstarted/api/termsandconditions",
    setpassword: "getstarted/api/setpassword",
    setForgotpassword: "api/setpassword",
    forgotOtpVerify: "api/forgotpassword/verifyotp",
    forgotEmailOtpSend: "api/forgotpassword/sendotp",
  },
  ALL_HEADERS: {
    verifyOTP: "resourceapi/verifyotp",
    resendOTP: "resourceapi/resendotp",
    signOut: "resourceapi/signout",
    resetPassword: "resourceapi/resetchangepassword",
    // superadmin
    dashboardCount: "resourceapi/dashboard/getdashboardcounts",

    createCompany: "resourceapi/company/createcompany",
    editCompany: "resourceapi/company/getcompanydetail",
    deleteCompany: "resourceapi/company/deletecompany",
    companyStatus: "resourceapi/company/deactivatecompany",
    companyExport: "resourceapi/company/exportcompanydata",
    resendadminPasscode: "resourceapi/user/resendPasscode",
    viewadminPasscode: "resourceapi/user/viewPasscode",
    createUser: "resourceapi/user/createuser",
    editUser: "resourceapi/user/getuserdetail",
    deleteUser: "resourceapi/user/deleteuser",
    userStatus: "resourceapi/user/deactivateuser",
    userExport: "resourceapi/user/exportuserdata",
    deleteCompanyDocument: "resourceapi/company/deletecompanydocument",

    usertype: "resourceapi/dropdown/getusertype",
    company: "resourceapi/dropdown/getcompany",
    role: "resourceapi/dropdown/getrolefromusertype",
    twoFAdata: "resourceapi/dropdown/gettwofactordropdown",
    switchTheme: "resourceapi/common/switchtheme",
    exportChatAction: "goosechat/exportgoosechatreactiondata",
    validatecompanycode: "resourceapi/company/validatecompanycode",
    // uesr management
    getuserrole: "resourceapi/usermanagement/getuserrole",
    createusermaster: "resourceapi/usermanagement/createuser",
    getedituserdata: "resourceapi/usermanagement/getedituserdetail",
    userDropdownData: "resourceapi/usermanagement/getuserdropdown",
    getDepartmentHead:
      "resourceapi/usermanagement/getuserdropdownfordepartmenthead",
    getSingleuserdata: "resourceapi/usermanagement/getprofileuserdetails",
    getUserQr: "resourceapi/usermanagement/getqrcodedata",
    getcountrycodes: "resourceapi/dropdown/getcountrycodes",
    // usre csv
    downloadUserCsv: "resourceapi/usermanagement/exportcsvtemplate",
    saveUserCsv: "resourceapi/usermanagement/savedatafromcsv",
    // clientdashboard count
    clientdashboard: "resourceapi/dashboard/clientdashboard",
    // department.
    createdepartment: "resourceapi/department/createdepartment",
    deleteDepartment: "resourceapi/department/deletedepartment",
    editdepartmentdata: "resourceapi/department/geteditdepartmentdetail",
    assignDepartmentList: "resourceapi/department/assigndepartmentlist",
    userMemberDataList: "resourceapi/usermanagement/getuserdropdown",
    assignmemberAddDepartmentView:
      "resourceapi/department/assignuserstodepartment",

    // Property
    addProperty: "resourceapi/propertymanagement/createproperty",
    getSingleProperty: "resourceapi/propertymanagement/geteditpropertydetails",
    getpropertyDropdown: "resourceapi/propertymanagement/getpropertydropdown",
    getpropertyFormDepartment:
      "resourceapi/propertymanagement/getpropertydropdownfordepartmentcreation",
    addMemberToProperty:
      "resourceapi/propertymanagement/assignmembertoproperty",
    addDepartmentToProperty:
      "resourceapi/propertymanagement/assigndepartmenttoproperty",
    deleteProperty: "resourceapi/propertymanagement/deleteproperty",
    downloadPropertyCsv:
      "resourceapi/propertymanagement/downloadcsvforproperty",
    savePropertyCsv: "resourceapi/propertymanagement/savepropertydatafromcsv",

    // Ticket
    // createTicket: "resourceapi/tickets/create",
    getticketfilterdropdowndata: "resourceapi/tickets/getfilterdropdowns",
    getdepartmnetlistbyproperties:
      "resourceapi/department/getdepartmnetlistbyproperties",
    getpriority: "resourceapi/dropdown/getpriority",
    gettags: "resourceapi/dropdown/gettags",
    getsingleticket: "resourceapi/tickets/getTicketInfo",
    getTicketCommemts: "resourceapi/ticketcomment/getComments",
    createtag: "resourceapi/tickets/createtag",
    chageTicketStatus: "resourceapi/tickets/changestatus",
    chnageTicketType: "resourceapi/tickets/movetoshareandinternal",
    getSingleTicketForEdit: "resourceapi/tickets/getticketeditdata",
    deleteTicket: "resourceapi/tickets/deleteticket",
    getDepartemntusers: "resourceapi/tickets/getdepartmentusers",
    updateTicketMember: "resourceapi/tickets/editticketassignmember",
    removeMemberFromTicket: "resourceapi/tickets/deleteTicketMember",
    getPestType: "resourceapi/dropdown/getpests",
    getticketsdropdown: "resourceapi/dropdown/getticketsdropdown",
    getuserswithjobtitle: "resourceapi/usermanagement/getuserswithjobtitle",
    linktickets: "resourceapi/ticketlinkitems/linktickets",
    linkWebLink: "resourceapi/ticketlinkitems/linkweblinks",
    deleteWebLink: "resourceapi/ticketlinkitems/deletelinkweblink",
    getlinkedtickets: "resourceapi/ticketlinkitems/getlinkedtickets",
    unarchiveTicket: "resourceapi/tickets/unarchiveTicket",
    createEvent: "resourceapi/ticketevent/addevent",
    deleteEvent: "resourceapi/ticketevent/deleteevent",
    getEventByDate: "resourceapi/ticketevent/getallevents",
    getEventsByMonth: "resourceapi/ticketevent/geteventsbymonth",
    getEventDetail: "resourceapi/ticketevent/geteventdetail",
    updateMembersToEvent: "resourceapi/ticketevent/updatememberstoevent",
    deleteMemberFromEvent: "resourceapi/ticketevent/deletememberfromevent",
    getTicketLogs: "resourceapi/ticketlogs/getticketlogs",
    ticketLogExport: "resourceapi/ticketlogs/exportlogs",
    getTicketCategoryData: "resourceapi/categoryreactionlist/getticketdata",
    createReaction: "resourceapi/categoryreactionlist/saveticketreaction",
    getTicketMembers: "/resourceapi/tickets/getticketmembers",
    getTicketRecurringflow: "resourceapi/tickets/getticketrecurringflow",
    getuserticketdropdownforticket:
      "/resourceapi/tickets/getuserdropdownforticket",
    getMemberForPropertyDepartment:
      "/resourceapi/tickets/getmemberforpropertydepartment",
    // checkin grid.
    checkinGrid: "resourceapi/ticketcheckin/checkingrid",

    // archive grid.
    archiveGrid: "resourceapi/tickets/movetoarchive",

    // ticket more notification.
    ticketmorenotification: "resourceapi/tickets/turnnotificationonoff",

    // Company permission
    getCompanyPermission: "resourceapi/company/getcompanypermission",
    getDepartmentFromProperty: "lotoform/getdepartmentdropdown",

    // Lotoform APIs
    lotoform_List: "lotoform/list",
    lotoform_commonDropdowns: "lotoform/dropdowns-data",
    lotoform_getUsersByPropertyId: "lotoform/getusersbyproperty",
    lotoform_lockboxDropdown: "lockbox/dropdown",
    lotoform_create: "lotoform",
    lotoform_delete: "lotoform",
    lotofrom_getLotofrombyId: "lotoform",
    lotoform_xls: "lotoform/exportlotoformdata",
    // Lotoform-Comment
    lotoform_comment_create: "lotoform/comment",
    lotoform_comment_get: "lotoform/comment",
    lotoform_comment_delete: "lotoform/comment",
    lotoform_comment_edit: "lotoform/comment",
    // Lotoform-Video
    lotoform_video_create: "lotoform/video",
    lotoform_video_get: "lotoform/video",
    lotoform_video_delete: "lotoform/video",
    lotoform_video_download: "lotoform/video/download",
    // Lotoform-Image
    lotoform_saveimage: "/lotoform/image",
    lotoform_imagegrid: "/lotoform/image",
    lotoform_downloadimage: "/lotoform/image/download",
    //LotoTransForm
    getLotoTranferGridData: "lototransferform/list",
    deleteLotoTransform: "lototransferform",
    fetchSingleLotoTransform: "lototransferform",
    createLotoTransform: "lototransferform",
    editLotoTransform: "lototransferform",
    lototransfer_xls: "lototransferform/exportlotoformdata",
    lototransferprocedure: "lototransferprocedure",
    lototransferprocedureDelete: "lototransferprocedure",
    lototransfer_lockboxDropdown: "lototransferform/lockbox/dropdown",
    lototransferform_commonDropdowns: "lototransferform/dropdowns-data",
    // lotoTransform-Image
    lotoTransform_saveimage: "lototransferform/image",
    lotoTransform_imagegrid: "lototransfer/image",
    lotoTransform_deleteimage: "lototransferform/image",
    lotoTransform_downloadimage: "lototransferform/image/download",
    // lotoTransform-Video
    lotoTransform_video_create: "lototransferform/video",
    lotoTransform_video_get: "lototransfer/video",
    lotoTransform_video_delete: "lototransferform/video",
    lotoTransform_video_download: "lototransferform/video/download",
    // lotoTransform-Comment
    lototransform_comment_create: "lototransferform/comment",
    lototransform_comment_get: "lototransfer/comment",
    lototransform_comment_delete: "lototransferform/comment",
    lototransform_comment_edit: "lototransferform/comment",
    // lotoTransform-Document
    lotoTransform_documentgrid: "lototransfer/document",
    lotoTransform_savedocument: "lototransferform/document",
    lotoTransform_deletedocument: "lototransferform/document",
    lotoTransform_documentdownload: "lototransferform/document/download",
    // Lotoform-Image
    lotoform_editimage: "lotoform/image",
    lotoform_updateimage: "lotoform/image",
    lotoform_deleteimage: "lotoform/image",
    // Lotoform-Document
    lotoform_documentgrid: "lotoform/document",
    lotoform_savedocument: "lotoform/document",
    lotoform_deletedocument: "lotoform/document",
    lotoform_documentdownload: "lotoform/document/download",
    // Lock
    lockList: "lock/list",
    deletelock: "lock",
    getPropertDropdown: "lotoprocedure/properties-dropdown",
    userprofile: "lotoform/getusersbyproperty",
    createLock: "lock",
    getlock: "lock",
    // updatelock: "lock",
    deleteLockImage: "lock",
    lock_xls: "lock/exportlockdata",

    // LotoProcedure
    lotoProcedure: "lotoprocedure",
    getLotoProcedure: "lotoprocedure",
    deleteProcedure: "lotoprocedure",
    equipmentDropdown: "lotoprocedure/equipments-dropdown",

    // Lockox APIs
    lockDropdown: "lock/dropdown",
    // Grid
    deleteLockbox: "lockbox", // Delete Lockbox
    fetchSingleLockbox: "lockbox", // edit lockbox
    createLockbox: "lockbox",
    // updateLockbox: "lockbox",
    deleteLockboxImage: "lockbox",
    lockbox_xls: "lockbox/exportlockboxdata",
    lockboxlist: "lockbox/list",

    // goose chat
    sendmsg: "goosechat/sendmessage",
    gooselist: "goosechat/list",
    goosereactanswer: "goosechat/reactanswer",
    goosedislikemessage: "goosechat/dislikemessage",
    goosegrouplist: "goosechat/grouplist",
    goosegrouplistdelete: "goosechat/deletegroup",
    goosegrouplistrename: "goosechat/renamegroup",
    goosegetgreetings: "goosechat/getgreetings",

    // Goose Chat Configuration
    gooseChatConfigGrid: "gooseconfiguration/getgooseconfigurations",
    addinstancetomodel: "gooseconfiguration/addinstancetomodel",
    createorupdatenewpool: "gooseconfiguration/createorupdatenewpool",
    deleteinstance: "gooseconfiguration/deleteinstance",
    deletepool: "gooseconfiguration/deletepool",
    getpoolsforassociation: "gooseconfiguration/getpoolsforassociation",
    deleteassociationpoolwithinstance:
      "gooseconfiguration/deleteassociationpoolwithinstance",
    associatepoolwithinstance: "gooseconfiguration/associatepoolwithinstance",
    getinstanceforedit: "gooseconfiguration/getinstanceforedit",

    // Goose Chat FAQs
    getfaqsgrid: "goosefaqs/getfaqs",
    createafaq: "goosefaqs/createafaq",
    getsimilarquestions: "goosefaqs/getsimilarquestions",
    geteditdata: "goosefaqs/geteditdata",
    deleteafaq: "goosefaqs/deleteafaq",
    getsimilarquestionsfromfaq: "goosefaqs/getsimilarquestionsfromfaq",
    changestatusofpermission: "goosefaqs/changestatusofpermission",

    // Goose Chat Agents
    getGooseChatAgentsGrid: "gooseagent/grid",
    editGooseChatAgent: "gooseagent/editdata",
    updateAgent: "gooseagent/updateagent",
    resetllmagentprompt: "gooseconfiguration/resetllmagentprompt",

    //usernotification
    // getusernotificationsetting: "/resourceapi/notificationaccess/getnotificationaccess",
    // saveusernotificationsetting: "/resourceapi/notificationaccess/savenotificationaccess",

    // Loop vendor
    getloopvendorcompanies:
      "/resourceapi/loopvendordata/getcompaniesforvendorassociation",
    getloopvendorcompanymembers:
      "/resourceapi/loopvendordata/getvendorassociatedmembers",
    getvendoruserassignwithpropertyanddepartment:
      "/resourceapi/loopvendordata/getvendoruserassignwithpropertyanddepartment",
    associateVendor:
      "/resourceapi/loopvendordata/associatevendortopropertyanddepartment",
    getdataformemberassociation:
      "/resourceapi/loopvendordata/getcompanyandvendordataforvendorassociation",
    getusersbycompanyid: "/resourceapi/usermanagement/getusersbycompanyid",
    associatememberstocompany:
      "/resourceapi/loopvendordata/associatememberstovendorandcompany",
    assignmultiplepropertydepartmenttouser:
      "/resourceapi/loopvendordata/assignmultiplepropertydepartmenttouser",
    getalreadyassociatedvendorsfordropdown:
      "/resourceapi/loopvendordata/getalreadyassociatedvendorsfordropdown",
    assignmultipleuserstopropertydepartment:
      "/resourceapi/loopvendordata/assignmultipleuserstopropertydepartment",
    changeassociatedvendorstatus:
      "/resourceapi/loopvendordata/changeassociatedvendorstatus",
    removeassociatedvendor:
      "/resourceapi/loopvendordata/removeassociatedvendor",
    dissociatepropertydepartmentfromuser:
      "/resourceapi/loopvendordata/dissociatepropertydepartmentfromuser",
    geteditdataforcompanyvendorassociation:
      "/resourceapi/loopvendordata/geteditdataforcompanyvendorassociation",
    getusernotificationsetting:
      "/resourceapi/notificationaccess/getnotificationaccess",
    saveusernotificationsetting:
      "/resourceapi/notificationaccess/savenotificationaccess",
    getassociatedmembersforpropertydepartment:
      "/resourceapi/loopvendordata/getassociatedmembersforpropertydepartment",
    removevendorandpropertydepartmentassociation:
      "/resourceapi/loopvendordata/removevendorandpropertydepartmentassociation",
    getclientandloopvendorgrid:
      "/resourceapi/loopvendordata/getclientandloopvendorgrid",
    getLoopVendorLogsGrid: "/resourceapi/logs/getvendorlogs",

    //Permission
    getmoduleandactiondropdown:
      "/resourceapi/permission/getmoduleandactiondropdown",
    getcompaniesforpermission:
      "/resourceapi/permission/getcompaniesforpermission",
    createPermission: "/resourceapi/permission/createpermission",
    getpermissiondataforedit:
      "/resourceapi/permission/getpermissiondataforedit",
    getpermissions: "resourceapi/permission/getpermissions",
    getviewpermissiondata: "resourceapi/permission/getpermissionview",
    getmembersforpermission: "resourceapi/permission/getmembersforpermission",
    savememberforpermission: "resourceapi/permission/savememberstopermission",

    // Escalation
    getEscalationData: "/resourceapi/escalation/editData",
    escalationuserdropdown: "/resourceapi/escalation/escalationuserdropdown",
    createEscalation: "/resourceapi/escalation/create",
    escalationactive: "resourceapi/escalation/activeinactive",

    // Goose Documents
    gooseDocGrid: "/resourceapi/company/companydocument",
  },
  GRIDCALLS: {
    companyGrid: "resourceapi/company/getallcompanydetails",
    userGrid: "resourceapi/usermanagement/getuserdetails",
    departmentGrid: "resourceapi/department/getalldepartmentdetails",
    propertyGridData: "resourceapi/propertymanagement/getpropertydetails",
    ticketGridData: "resourceapi/tickets/getall",
    userAdminGrid: "resourceapi/user/getalluserdetails",
    goosechataction: "goosechat/dislikeanswerlist",
    getassociatedcompanies:
      "/resourceapi/loopvendordata/getalreadyassociatedcompanies",
    getpropertydepartmentvendorgrid:
      "/resourceapi/loopvendordata/getpropertydepartmentvendorgrid",
    getmultiplepropertiesdepartmentsforusers:
      "/resourceapi/loopvendordata/getmultiplepropertiesdepartmentsforusers",

    //permisssion grid
    permissionGrid: "/resourceapi/permission/getallpermissions",

    // escalation grid
    escalationGrid: "/resourceapi/escalation/grid",

    // Escaltion Log Grid
    escalationloggrid: "/resourceapi/escalation/escalationloggrid",
    escalationmodulelogs: "/resourceapi/escalation/escalationmodulelogs",
    getescalationlogs: "resourceapi/logs/getescalationlogs",
  },
  MULTIPART: {
    companyUploadDocument: "resourceapi/company/uploadocument",
    ticketAddComment: "resourceapi/ticketcomment/addcomment",
    updateLockbox: "lockbox",
    updatelock: "lock",
    validateCsv: "resourceapi/usermanagement/validatecsvdata",
    validatePropertyCsv: "resourceapi/propertymanagement/validatecsvdata",
    exportUserCsv: "resourceapi/usermanagement/exportuserdata",
    exportPropertyCsv: "resourceapi/propertymanagement/exportpropertydata",
    FILE_UPLOAD: "resourceapi/company/uploadfiledata",
    ticketDocument: "resourceapi/dashboard/uploadDocument",
    createTicket: "resourceapi/tickets/create",
  },
  APIWITHID: {
    // Lotoform APIs
    lotoform_edit: "lotoform",
    lotoform_getUsersByPropertyId: "lotoform/getusersbyproperty",
    lotoform_lockboxDropdown: "lockbox/dropdown",
  },

  //* CHAT
  GET_PROPERTY_LIST: "resourceapi/propertymanagement/getpropertydropdown",
  GET_TICKET_LIST: "resourceapi/tickets/getall",
  GET_USER_LIST: "resourceapi/usermanagement/getuserdropdown",
  GET_DEPARTMENT_LIST: "resourceapi/department/getalldepartmentdetails",
  GET_LOTOFORM_LIST: "lotoform/listforchat",
  GET_LOTOFORM_USET_LIST: "lotoform/getmembersfromchat",
  GET_USER_DETAILS_BY_TICKET: "resourceapi/chat/getUserDetailsByTicket",
  GET_USER_DETAILS_BY_PROPERTY: "resourceapi/chat/getUserDetailsByProperty",
  GET_USER_DETAILS_BY_DEPARTMENT: "resourceapi/chat/getUserDetailsByDepartment",
  GET_USER_DETAILS_BY_INSPECTION:
    "resourceapi/inspection/getUserDetailsByInspectionId",
  GET_ROOM_DETAILS_BY_ROOM_ID: "resourceapi/chat/getRoomDetailsByRoomId",
  GET_UNREAD_CHAT_COUNT: "resourceapi/chat/unread-chat-count",
  GET_SEARCH_GROUPS: "resourceapi/chat/search-groups",
  GET_SEARCH_MESSAGES: "resourceapi/chat/search-messages",
  MENTION_NOTIFICATION: "resourceapi/chat/mention-notification",
  GET_EUIPMENT_ID_FOR_CHAT: "lotoform/getequipmentidsforchat",
  GET_ROOM_ATTACHMENT: "resourceapi/chat/room/attachment",

  //* Notification
  GET_NOTIFICATION_LIST: "resourceapi/notification/notification-histories",
  MARK_READ_NOTIFICATION: "resourceapi/notification/mark-read",
  MARK_ALL_READ_NOTIFICATION: "resourceapi/notification/mark-all",
  DELETE_NOTIFICATION: "resourceapi/notification/remove-notification-histories",
  NOTIFICATION_COUNT: "resourceapi/notification/count-notification-histories",
  SUBSCRIBE_NOTIFICATION: "resourceapi/notification/subscribe",
  UNSUBSCRIBE_NOTIFICATION: "resourceapi/notification/unsubscribe",

  //* Inspection
  GET_INSPECTION_LIST: "resourceapi/inspection/getAll",
  GET_INSPECTION_TEMPLATE_LIST: "resourceapi/inspectionTemplate/getAll",
  CREATE_INSPECTION_TEMPLATE: "resourceapi/inspectionTemplate/create",
  GET_INSPECTION_TEMPLATE_BY_ID:
    "resourceapi/inspectionTemplate/getTemplateInfo",
  DUPLICATE_INSPECTION_TEMPLATE_BY_ID:
    "resourceapi/inspectionTemplate/duplicateInspectionTemplate",
  DELETE_INSPECTION_TEMPLATE_BY_ID:
    "resourceapi/inspectionTemplate/deleteInspectionTemplate",
  PUBLISH_TEMPLATE: "resourceapi/inspectionTemplate/publishTemplate",
  GET_INSPECTION_REPORT: "resourceapi/inspection/getInspectionFullDetails",
  GET_INSPECTION_KNOWLEDGE_BASE:
    "resourceapi/inspectionTemplate/getAllAttachment",
  UPLOAD_INSPECTION_KNOWLEDGE_BASE:
    "resourceapi/inspectionTemplate/uploadAttachment",
  DELETE_INSPECTION_KNOWLEDGE_BASE:
    "resourceapi/inspectionTemplate/deleteAttachment",
  GET_TICKET_INSPECTION_QUESTION:
    "resourceapi/inspection/getQuestionsForTicket",
  UPDATE_TICKET_INSPECTION_QUESTION_CHECK:
    "/resourceapi/inspection/questionCheckedOrUnchecked",
  AUTO_SAVE_INSPECTION: "/resourceapi/inspectionTemplate/autoSave",

  //* LoopVender
  GET_LOOP_VENDOR_LIST:
    "resourceapi/loopvendordata/getpropertydepartmentvendorgrid",

  SEND_MAIL_REPORT: "resourceapi/company/sendEmail",

  GET_LOTO_CONVERSION: "resourceapi/chat/lotoConversion",

  //Survay
  CREATE_SURVEY_GENERAL_SETUP: "resourceapi/survey/create",
  GET_ALL_SURVEY: "resourceapi/survey/getAll",
  ADD_QUESTION_TO_SURVEY: "resourceapi/survey-question/create",
  GET_SURVEY_QUESTION: "resourceapi/survey-question/get-survey-question",
  GET_FULL_SURVEY_DETAILS: "resourceapi/survey/get-survey-details",
  SET_SURVEY_CONFIGURATION: "resourceapi/survey/survey-configuration",
  GET_QR_SURVEY_CODE: "resourceapi/survey/survey-qrcode",
  DUPLICATE_SURVEY: "resourceapi/survey-question/duplicate-survey",
  SUBMIT_SURVEY_RESPONSE: "resourceapi/survey-question-answer/save-answer",
  GET_SURVEY_REPORTING_SUMMARY:
    "resourceapi/survey-response/reporting-summary",
  INACTIVE_SURVEY: 'resourceapi/survey/inactive-survey',
  // TODO: Future API endpoint for individual survey responses
  GET_SURVEY_INDIVIDUAL_RESPONSES:
    "resourceapi/survey-response/get-individual-responses",
};
