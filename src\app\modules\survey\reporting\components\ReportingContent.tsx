import React, { useEffect } from "react";
import { Card } from "react-bootstrap";
import SecondaryTabs from "./SecondaryTabs";
import FilterPanel from "./FilterPanel";
import SummaryCard from "./SummaryCard";
import ChartContainer from "./ChartContainer";
import FullResponseList from "./FullResponseList";
import { ChartContextProvider } from "../context/ChartContext";
import { useSurveyData } from "../context/SurveyDataContext";
import { Loader } from "../../../../component";
import ViewAllUserResponses from "./ViewAllUserResponses";

interface ReportingContentProps {
  surveyId: string;
  activeSubTab: string;
  onSubTabChange: (tab: string) => void;
}

const ReportingContent: React.FC<ReportingContentProps> = ({
  surveyId,
  activeSubTab,
  onSubTabChange,
}) => {
  const { fetchSurveyData, isLoading, error } = useSurveyData();

  // // Fetch survey data when component mounts or surveyId changes
  useEffect(() => {
    if (surveyId) {
      console.log(
        "🔄 ReportingContent: Fetching survey data for surveyId:",
        surveyId
      );
      fetchSurveyData(surveyId);
    }
  }, [surveyId, fetchSurveyData]);

  if (isLoading) {
    return <div>{isLoading && <Loader />}</div>;
  }

  // if (error) {
  //   return (
  //     <div className="alert alert-danger" role="alert">
  //       <h4 className="alert-heading">Error Loading Survey Data</h4>
  //       <p>{error}</p>
  //     </div>
  //   );
  // }

  return (
    <>
      <ChartContextProvider>
        {/* Secondary tabs */}
        <div className="mb-4">
          <SecondaryTabs
            activeTab={activeSubTab}
            onTabChange={onSubTabChange}
          />
        </div>

        {/* Filter panel */}
        <div className="mb-4">
          {(activeSubTab === "goose" || activeSubTab === "summary") && (
              <>
                {activeSubTab === "goose" && <FilterPanel />}

                {/* Chart Container */}
                <div className="mb-4">
                  <ChartContainer
                    activeTab={activeSubTab}
                    surveyId={surveyId}
                  />
                </div>

                {/* Content area - only show SummaryCard for goose tab */}
                {activeSubTab === "goose" && (
                  <div className="mb-4">
                    <SummaryCard />
                  </div>
                )}
              </>
          )}

          {/* Full Response List Tab */}
          {activeSubTab === "responses" && (
            <div className="mb-4">
              <FullResponseList surveyId={surveyId} activeTab={activeSubTab} />
            </div>
          )}

          {activeSubTab === 'allUserResponses' && (
            <ViewAllUserResponses />
          )}
        </div>
      </ChartContextProvider>
    </>
  );
};

export default ReportingContent;
