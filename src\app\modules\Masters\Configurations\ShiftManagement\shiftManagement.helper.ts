import { APIs } from "../../../../serverconfig/apiURLs";
import axiosInstance from "../../../../serverconfig/axiosInstance";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";




class ShiftManagamentService {

    async getGridData(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.GRIDCALLS.getshiftsgrid, {
            encryptedData: payloadData,
        });
    }

    async getshiftsfordropdown() {
        return await axiosInstance.post(APIs.ALL_HEADERS.getshiftsfordropdown, {
            encryptedData: ""
        })
    }

    async getdayofweek() {
        return await axiosInstance.post(APIs.ALL_HEADERS.getdayofweek, {
            encryptedData: ""
        })
    }

    async getrepeatpatterns() {
        return await axiosInstance.post(APIs.ALL_HEADERS.getrepeatpatterns, {
            encryptedData: ""
        })
    }

    async generateshift(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.generateshift, {
            encryptedData: payloadData,
        });
    }

    async getshiftdataforedit(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.getshiftdataforedit, {
            encryptedData: payloadData,
        });
    }

    async deleteshift(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.deleteshift, {
            encryptedData: payloadData,
        });
    }

}
export const shiftManagamentService = new ShiftManagamentService();
