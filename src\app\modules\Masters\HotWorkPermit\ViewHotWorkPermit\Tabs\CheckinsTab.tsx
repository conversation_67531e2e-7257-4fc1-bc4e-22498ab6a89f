import React, { useState } from "react";
import { MdOutlineInfo } from "react-icons/md";
import { Card, Col, Row } from "react-bootstrap";
import userimage from "../../../../../../efive_assets/images/user.jpg";
import galleryimage from "../../../../../../efive_assets/images/gallery.svg";
import sortingsvg from "../../../../../../efive_assets/images/sortingsvg.svg";
import { RiArrowDropDownLine, RiArrowDropUpLine } from "react-icons/ri";
import HotWorkCheckInUploadDoc from "../HotWorkViewModal/HotWorkCheckInUploadDoc";
import Spinner from "../../../../common/Spinner";

export const CheckinsTab = () => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [attachmentModal, setAttachmentModal] = useState<boolean>(false)
  const [loading,setLoading]=useState<boolean>(false)
  const toggleDetails = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
    {loading && <Spinner/>}
      <div className="d-flex justify-content-between">
        <div className="title d-flex flex-column gap-5">
          <span>During hot work</span>
          <span className="text-muted">March 06 2024</span>
        </div>
        <div className="notification">
          <span className="text-muted cursor-pointer">
            <MdOutlineInfo size={15} />
          </span>
          <span className="tooltip-text">
            Indicates whether the check-in has been completed. Green means
            completed, red means pending.
          </span>
        </div>
      </div>

      <div className="mt-3 check-ins-details">
        {/* Container for cards with overflow control */}
        <div
          className={`custom-card-container ${isExpanded ? "expanded" : ""}`}
        >
          {/* First 2 cards are always shown */}
          <Card className="mb-2 custom-card cursor-pointer">
            <Card.Body className="p-2">
              <div className="d-flex align-items-center justify-content-between gap-2">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Eleanor Pena</span>
                    <p className="text-muted ">Check in at 5:17 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <div>
                    <span className="mx-3">
                      <img src={sortingsvg} alt="" className="mb-3" />
                    </span>
                  </div>
                  <div className="d-flex flex-column">
                    <span>1 hr 32 m</span>
                    <span className="text-muted">Session</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
          <Card className="mb-2 custom-card cursor-pointer">
            <Card.Body className="p-2">
              <div className="d-flex align-items-center justify-content-between gap-2">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Eleanor Pena</span>
                    <p className="text-muted ">Check in at 3:50 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <div>
                    <span>
                      <img src={sortingsvg} alt="" className="mb-3" />
                    </span>
                    <span className="mx-3">
                      <img src={galleryimage} alt="" className="mb-3" onClick={()=>setAttachmentModal(true)} />
                    </span>
                  </div>
                  <div className="d-flex flex-column">
                    <span>1 hr 32 m</span>
                    <span className="text-muted">Session</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>

          <Row>
            <Col sm={6}>
              <div className="d-flex align-items-center justify-content-between gap-2 mb-2 p-2 Check-pending px-5">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Shift End</span>
                    <p className="text-muted fs-8">Pending at 3:00 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <div className="d-flex flex-column">
                    <span>Ralph Edwards</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col sm={6}>
              <div className="d-flex align-items-center justify-content-between gap-2 mb-2 p-2 check-success px-5">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Shift Start</span>
                    <p className="text-muted fs-8">Completed at 3:00 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <div className="d-flex flex-column">
                    <span className="">Eleanor Pena</span>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          <Card className="mb-2 custom-card cursor-pointer">
            <Card.Body className="p-2">
              <div className="d-flex align-items-center justify-content-between gap-2">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Eleanor Pena</span>
                    <p className="text-muted ">Check in at 3:50 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <div>
                    <span>
                      <img src={sortingsvg} alt="" className="mb-3" />
                    </span>
                    <span className="mx-3">
                      <img src={galleryimage} alt="" className="mb-3" />
                    </span>
                  </div>
                  <div className="d-flex flex-column">
                    <span>1 hr 32 m</span>
                    <span className="text-muted">Session</span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
          {isExpanded && (
            <>
              <Card className="mb-2 custom-card cursor-pointer">
                <Card.Body className="p-2">
                  <div className="d-flex align-items-center justify-content-between gap-2">
                    <div className="d-flex align-items-center gap-5">
                      <div>
                        <img
                          src={userimage}
                          alt="uselogo"
                          height={"48px"}
                          width={"48px"}
                          className="rounded-circle"
                        />
                      </div>
                      <div className="mt-3">
                        <span>Eleanor Pena</span>
                        <p className="text-muted ">Check in at 3:50 PM</p>
                      </div>
                    </div>
                    <div className="time d-flex flex-end">
                      <div>
                        <span>
                          <img src={sortingsvg} alt="" className="mb-3" />
                        </span>
                        <span className="mx-3">
                          <img src={galleryimage} alt="" className="mb-3" />
                        </span>
                      </div>
                      <div className="d-flex flex-column">
                        <span>1 hr 32 m</span>
                        <span className="text-muted">Session</span>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
              <Card className="mb-2 custom-card cursor-pointer">
                <Card.Body className="p-2">
                  <div className="d-flex align-items-center justify-content-between gap-2">
                    <div className="d-flex align-items-center gap-5">
                      <div>
                        <img
                          src={userimage}
                          alt="uselogo"
                          height={"48px"}
                          width={"48px"}
                          className="rounded-circle"
                        />
                      </div>
                      <div className="mt-3">
                        <span>Eleanor Pena</span>
                        <p className="text-muted ">Check in at 3:50 PM</p>
                      </div>
                    </div>
                    <div className="time d-flex flex-end">
                      <div>
                        <span>
                          <img src={sortingsvg} alt="" className="mb-3" />
                        </span>
                        <span className="mx-3">
                          <img src={galleryimage} alt="" className="mb-3" />
                        </span>
                      </div>
                      <div className="d-flex flex-column">
                        <span>1 hr 32 m</span>
                        <span className="text-muted">Session</span>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </>
          )}
        </div>

        <div className="show-hide d-flex justify-content-center mt-3 mb-3">
          <span onClick={toggleDetails} className="cursor-pointer">
            {isExpanded ? (
              <span className="text-center check-details-expand">
                Hide Details <RiArrowDropUpLine size={20} />
              </span>
            ) : (
              <span className="text-center check-details-expand">
                Show More Details <RiArrowDropDownLine size={20} />
              </span>
            )}
          </span>
        </div>
      </div>
      <HotWorkCheckInUploadDoc attachmentModal={attachmentModal} setAttachmentModal={setAttachmentModal} setisGridLoading={setLoading}/>
    </>
  );
};

