import React, { createContext, useContext, useState, ReactNode } from "react";
import surveyConfig from "../data/surveyConfig.json";

type ChartType = "pie" | "bar" | "stackedBar";

interface ChartOption {
  type: ChartType;
  id?: number;
  category?: string;
  label: string;
}

interface ChartContextType {
  selectedChartType: ChartType;
  setSelectedChartType: (type: ChartType) => void;
  chartOptions: ChartOption[];
  selectedChartOption: ChartOption;
  setSelectedChartOption: (option: ChartOption) => void;
  getChartsOfType: (type: ChartType) => ChartOption[];
}

// Create context with default values
const ChartContext = createContext<ChartContextType>({
  selectedChartType: "pie",
  setSelectedChartType: () => {},
  chartOptions: [],
  selectedChartOption: { type: "pie", id: 1, label: "Default Chart" },
  setSelectedChartOption: () => {},
  getChartsOfType: () => [],
});

// Create a provider component
export const ChartContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>("pie");

  // Generate chart options from surveyConfig
  const generateChartOptions = (): ChartOption[] => {
    const options: ChartOption[] = [];

    // Add pie chart options with simple labels
    surveyConfig.chartData.pieCharts.forEach((chart, index) => {
      options.push({
        type: "pie",
        id: chart.id,
        label: `Pie Chart ${index + 1}`,
      });
    });

    // Add bar chart options with simple labels - each with their own unique data
    surveyConfig.chartData.barCharts.forEach((chart, index) => {
      options.push({
        type: "bar",
        id: chart.id,
        label: `Bar Chart ${index + 1}`,
      });
    });

    // Add stacked bar chart options
    // Create multiple stacked bar options using different data combinations
    options.push({
      type: "stackedBar",
      id: 1,
      label: "Stacked Bar Chart 1",
    });

    options.push({
      type: "stackedBar",
      id: 2,
      label: "Stacked Bar Chart 2",
    });

    // Add category chart options with simple labels
    // surveyConfig.chartData.categoryCharts.forEach((chart, index) => {
    //   options.push({
    //     type: "category",
    //     category: chart.category,
    //     label: `Category Chart ${index + 1}`,
    //   });
    // });

    return options;
  };

  const chartOptions = generateChartOptions();
  const [selectedChartOption, setSelectedChartOption] = useState<ChartOption>(
    chartOptions[0] || { type: "pie", id: 1, label: "Default Chart" }
  );

  // Function to get all charts of a specific type
  const getChartsOfType = (type: ChartType): ChartOption[] => {
    return chartOptions.filter((option) => option.type === type);
  };

  return (
    <ChartContext.Provider
      value={{
        selectedChartType,
        setSelectedChartType,
        chartOptions,
        selectedChartOption,
        setSelectedChartOption,
        getChartsOfType,
      }}
    >
      {children}
    </ChartContext.Provider>
  );
};

// Create a hook to use this context
export const useChartContext = () => useContext(ChartContext);
