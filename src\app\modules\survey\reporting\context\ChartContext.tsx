import React, { createContext, useContext, useState, ReactNode, useMemo, useCallback } from "react";

/**
 * Chart type definition - currently supporting pie and stacked bar charts
 */
type ChartType = "pie" | "stackedBar";

/**
 * Chart option interface defining the structure of chart options
 */
interface ChartOption {
  type: ChartType;
  id?: number;
  category?: string;
  label: string;
}

/**
 * Chart context interface defining the shape of the context
 */
interface ChartContextType {
  selectedChartType: ChartType;
  setSelectedChartType: (type: ChartType) => void;
  chartOptions: ChartOption[];
  selectedChartOption: ChartOption;
  setSelectedChartOption: (option: ChartOption) => void;
  getChartsOfType: (type: ChartType) => ChartOption[];
}

/**
 * Default chart options available in the application
 */
const DEFAULT_CHART_OPTIONS: ChartOption[] = [
  {
    type: "pie",
    id: 1,
    label: "Pie Chart",
  },
  {
    type: "stackedBar",
    id: 2,
    label: "Stacked Bar Chart",
  },
];

// Create the context with default values
const ChartContext = createContext<ChartContextType>({
  selectedChartType: "pie",
  setSelectedChartType: () => {},
  chartOptions: DEFAULT_CHART_OPTIONS,
  selectedChartOption: DEFAULT_CHART_OPTIONS[0],
  setSelectedChartOption: () => {},
  getChartsOfType: () => [],
});

/**
 * Provider component that manages chart selection state
 */
export const ChartContextProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State for chart type and selected option
  const [selectedChartType, setSelectedChartType] = useState<ChartType>("pie");
  const [selectedChartOption, setSelectedChartOption] = useState<ChartOption>(DEFAULT_CHART_OPTIONS[0]);
  
  // Use constant chart options - no need to change during runtime
  const chartOptions = DEFAULT_CHART_OPTIONS;

  // Function to filter charts by type
  const getChartsOfType = useCallback(
    (type: ChartType): ChartOption[] => {
      return chartOptions.filter((option) => option.type === type);
    },
    []
  );

  // Create context value
  const contextValue = useMemo(
    () => ({
      selectedChartType,
      setSelectedChartType,
      chartOptions,
      selectedChartOption,
      setSelectedChartOption,
      getChartsOfType,
    }),
    [selectedChartType, selectedChartOption, getChartsOfType]
  );

  return (
    <ChartContext.Provider value={contextValue}>
      {children}
    </ChartContext.Provider>
  );
};

/**
 * Hook to access the chart context
 * @returns The chart context value
 */
export const useChartContext = () => useContext(ChartContext);
