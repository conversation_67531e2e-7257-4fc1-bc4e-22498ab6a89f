import React, { createContext, useContext, useState, ReactNode } from "react";

type ChartType = "pie" | "stackedBar";

interface ChartOption {
  type: ChartType;
  id?: number;
  category?: string;
  label: string;
}

interface ChartContextType {
  selectedChartType: ChartType;
  setSelectedChartType: (type: ChartType) => void;
  chartOptions: ChartOption[];
  selectedChartOption: ChartOption;
  setSelectedChartOption: (option: ChartOption) => void;
  getChartsOfType: (type: ChartType) => ChartOption[];
}

// Create context with default values
const ChartContext = createContext<ChartContextType>({
  selectedChartType: "pie",
  setSelectedChartType: () => {},
  chartOptions: [],
  selectedChartOption: { type: "pie", id: 1, label: "Default Chart" },
  setSelectedChartOption: () => {},
  getChartsOfType: () => [],
});

// Create a provider component
export const ChartContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>("pie");

  // Generate chart options for available chart types
  const generateChartOptions = (): ChartOption[] => {
    const options: ChartOption[] = [];

    // Add pie chart option
    options.push({
      type: "pie",
      id: 1,
      label: "Pie Chart",
    });

    // Add stacked bar chart option
    options.push({
      type: "stackedBar",
      id: 2,
      label: "Stacked Bar Chart",
    });

    return options;
  };

  const chartOptions = generateChartOptions();
  const [selectedChartOption, setSelectedChartOption] = useState<ChartOption>(
    chartOptions[0] || { type: "pie", id: 1, label: "Default Chart" }
  );

  // Function to get all charts of a specific type
  const getChartsOfType = (type: ChartType): ChartOption[] => {
    return chartOptions.filter((option) => option.type === type);
  };

  return (
    <ChartContext.Provider
      value={{
        selectedChartType,
        setSelectedChartType,
        chartOptions,
        selectedChartOption,
        setSelectedChartOption,
        getChartsOfType,
      }}
    >
      {children}
    </ChartContext.Provider>
  );
};

// Create a hook to use this context
export const useChartContext = () => useContext(ChartContext);
