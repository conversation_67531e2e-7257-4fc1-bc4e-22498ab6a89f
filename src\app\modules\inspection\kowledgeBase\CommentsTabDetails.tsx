/**
 * CommentsTabDetails Component
 *
 * This component integrates chat functionality for inspection reports.
 * It follows the same pattern as the TicketDetail component for chat integration.
 *
 * The component:
 * 1. Uses the report data passed as props from the parent component
 * 2. Extracts the active room from the report data
 * 3. Loads chat history when a room is available
 * 4. Provides UI controls for file attachments, audio recording, and emoji picker
 * 5. Displays a simple message if chat is not available
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { Card, Row, Col } from 'react-bootstrap';
import { FaWifi } from 'react-icons/fa';
import { FiRefreshCcw } from 'react-icons/fi';
import Chats from '../../Chat/Chats';
import { useChat } from '../../Chat/services/useChat';
import { useAppSelector } from '../../../redux/store';

import { getStatusColor } from '../../../utils/CommonUtils';
import {
  cleanChatThreadHistory,
  clearTicketPageChatHistory,
  setChatList,
  setLoader,
  setThreadinitiatMessage,
  setTicketPageActiveRoom,
} from '../../../apis/slices/socketSlice';
import {
  ChatHistoryItem,
  ChatThreadHistoryItem,
  RoomsList,
} from '../../Chat/chatType';

interface Props {
  templateId?: string;
  report?: any; // Report data passed from parent component
}

const CommentsTabDetails: React.FC<Props> = ({ templateId, report }) => {
  const dispatch = useDispatch();
  const { getChatRoomMessagesHistory, chatRoomMessageSend, reConnect } = useChat();
  const {
    socket,
    ticketPageChatHistory,
    threadInitiatMessage,
    chatThreadHistory,
    connectionStatus
  } = useAppSelector((state) => state.socketConfig);

  // Chat-related states
  const [openChatThread, setOpenThread] = useState(false);
  const [showFilesOptionTicket, setShowFilesOptionTicket] = useState(false);
  const [showRecorderTicket, setShowRecorderTicket] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Get active room from report data
  const activeRoom: RoomsList = useMemo(() => {
    return report?.listUserChatResponse?.chats[0];
  }, [report?.listUserChatResponse?.chats[0]?.roomId]);

  // Fetch chat history when active room is available
  useEffect(() => {
    if (activeRoom && socket) {
      dispatch(cleanChatThreadHistory());
      dispatch(
        setLoader({
          history: true,
        })
      );

      const payload = {
        userToken: sessionStorage.getItem("userId") as string,
        roomId: activeRoom.roomId,
        size: 25,
        page: 0,
        deviceUniqueId: sessionStorage.getItem("deviceUniqueId") as string,
      };

      getChatRoomMessagesHistory(payload); // First time load
      dispatch(setTicketPageActiveRoom(activeRoom));
    }
  }, [activeRoom, socket, dispatch]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      dispatch(setChatList([]));
      dispatch(setTicketPageActiveRoom(null));
      dispatch(setThreadinitiatMessage(null));
      dispatch(clearTicketPageChatHistory());
    };
  }, []);

  // UI control functions
  const toggleFileOption = () => {
    setShowFilesOptionTicket((prev) => !prev);
  };

  const handleAttachmentClose = () => {
    setShowFilesOptionTicket(false);
    setTimeout(() => {
      setShowRecorderTicket(false);
    }, 310);
  };

  const handleToggleRecorder = () => {
    setShowRecorderTicket((prev) => !prev);
  };

  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  // Handle outside click for emoji picker
  const handleOutsideClick = (event: MouseEvent) => {
    if (!(event.target as HTMLElement).closest(".emoji-picker-container")) {
      setShowEmojiPicker(false);
    }
  };

  useEffect(() => {
    if (showEmojiPicker) {
      document.addEventListener("mousedown", handleOutsideClick);
    } else {
      document.removeEventListener("mousedown", handleOutsideClick);
    }
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [showEmojiPicker]);

  // No need for loading indicator as we're using report data from props

  // If chat functionality is available, show the chat component
  if (activeRoom) {
    return (
      <Card>
        <Card.Body className="p-0">
          <div className="d-flex align-items-center gap-2 mb-4 px-3 pt-3">
            <h4 className="mb-0">COMMENTS</h4>
            <div>
              {connectionStatus === "failed" ? (
                <FiRefreshCcw
                  className="fs-4 cursor-pointer"
                  onClick={(e) => reConnect(e)}
                />
              ) : (
                <FaWifi
                  className="fs-4"
                  color={getStatusColor(connectionStatus)}
                />
              )}
            </div>
          </div>

          <div className="custom-card-container inspectionPageChat">
            {activeRoom && (
              <Chats
                activeRoom={activeRoom}
                chatHistory={ticketPageChatHistory}
                threadInitiatMessage={
                  threadInitiatMessage as ChatThreadHistoryItem
                }
                onThreadClose={() => setOpenThread(false)}
                handleThread={(message) => {
                  dispatch(cleanChatThreadHistory());
                  setOpenThread(true);
                }}
                openThread={openChatThread}
                chatThreadHistory={chatThreadHistory}
                hideMainLoader={true}
                showFilesOptionTicket={showFilesOptionTicket}
                showRecorderTicket={showRecorderTicket}
                toggleFileOption={toggleFileOption}
                handleAttachmentClose={handleAttachmentClose}
                handleToggleRecorder={handleToggleRecorder}
                toggleEmojiPicker={toggleEmojiPicker}
                showEmojiPicker={showEmojiPicker}
              />
            )}
          </div>
        </Card.Body>
      </Card>
    );
  }

  // Display a simple message if chat is not available
  // This will be shown when:
  // 1. The report data doesn't contain chat information
  // 2. The socket connection is not established
  // 3. There's no active chat room for this inspection
  return (
    <Card>
      <Card.Body className="text-center py-5 p-0">
        <h4 className="mb-3">Comments</h4>
        <p className="text-muted">
          No chat room available for this inspection.
        </p>
      </Card.Body>
    </Card>
  );
};

export default CommentsTabDetails;