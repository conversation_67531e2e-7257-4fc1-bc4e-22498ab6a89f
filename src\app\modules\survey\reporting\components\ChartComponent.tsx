import React, { useEffect, useRef } from "react";
import ApexCharts, { ApexOptions } from "apexcharts";
import { Card } from "react-bootstrap";
import surveyConfig from "../data/surveyConfig.json";

interface ChartComponentProps {
  chartType: "pie" | "bar" | "stackedBar";
  chartId?: number;
  category?: string;
}

interface UnifiedChartData {
  id: number;
  questionNumber: number;
  title: string;
  totalResponses: number;
  answeredResponses: number;
  data: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
  }>;
  averageScore?: number;
}

const ChartComponent: React.FC<ChartComponentProps> = ({
  chartType,
  chartId,
  category,
}) => {
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstanceRef = useRef<ApexCharts | null>(null);

  // Helper function to create unified data from pie chart
  const createUnifiedDataFromPieChart = (pieChart: any): UnifiedChartData => {
    const totalResponses = pieChart.data.reduce(
      (sum: number, item: any) => sum + item.value,
      0
    );
    const weightedSum = pieChart.data.reduce(
      (sum: number, item: any, index: number) => {
        let weight = 5;
        if (
          item.name.toLowerCase() === "no" ||
          item.name.toLowerCase() === "negative"
        ) {
          weight = 1;
        } else if (
          item.name.toLowerCase() === "neutral" ||
          item.name.toLowerCase() === "average"
        ) {
          weight = 3;
        } else if (pieChart.data.length > 2) {
          weight = pieChart.data.length - index;
        }
        return sum + item.value * weight;
      },
      0
    );

    return {
      id: pieChart.id,
      questionNumber: pieChart.questionNumber,
      title: pieChart.title,
      totalResponses: pieChart.totalResponses,
      answeredResponses: pieChart.answeredResponses,
      data: pieChart.data,
      averageScore: totalResponses > 0 ? weightedSum / totalResponses : 0,
    };
  };

  // Helper function to create unified data from bar chart
  const createUnifiedDataFromBarChart = (barChart: any): UnifiedChartData => {
    // Bar charts in the config don't have detailed response data, so we need to create it
    // For now, we'll create a simple representation based on the average score
    const mockData = [
      {
        name: "Positive",
        value: Math.round(barChart.averageScore * 2),
        percentage: barChart.percentage,
        color: "#4CAF50",
      },
      {
        name: "Negative",
        value: Math.round((5 - barChart.averageScore) * 2),
        percentage: 100 - barChart.percentage,
        color: "#F44336",
      },
    ];

    return {
      id: barChart.id,
      questionNumber: barChart.questionNumber,
      title: barChart.title,
      totalResponses: barChart.totalResponses,
      answeredResponses: barChart.answeredResponses,
      data: mockData,
      averageScore: barChart.averageScore,
    };
  };

  useEffect(() => {
    // Get unified chart data - handle different data sources based on chart type
    const getUnifiedChartData = (): UnifiedChartData | null => {
      // For pie charts, use pie chart data
      if (chartType === "pie") {
        const pieChart = surveyConfig.chartData.pieCharts.find(
          (chart) => chart.id === chartId
        );

        if (pieChart) {
          return createUnifiedDataFromPieChart(pieChart);
        }
      }

      // For bar charts, try bar chart data first, then fallback to pie chart data
      if (chartType === "bar") {
        const barChart = surveyConfig.chartData.barCharts.find(
          (chart) => chart.id === chartId
        );

        if (barChart) {
          return createUnifiedDataFromBarChart(barChart);
        }

        // Fallback to pie chart data if bar chart not found
        const pieChart = surveyConfig.chartData.pieCharts.find(
          (chart) => chart.id === chartId
        );
        if (pieChart) {
          return createUnifiedDataFromPieChart(pieChart);
        }
      }

      // For stacked bar charts, use pie chart data
      if (chartType === "stackedBar") {
        const pieChart = surveyConfig.chartData.pieCharts.find(
          (chart) => chart.id === chartId
        );

        if (pieChart) {
          return createUnifiedDataFromPieChart(pieChart);
        }
      }

      // Fallback: If chartId is not found, try to use the first available pie chart
      const fallbackChart = surveyConfig.chartData.pieCharts[0];
      if (fallbackChart) {
        return createUnifiedDataFromPieChart(fallbackChart);
      }

      return null;
    };

    if (chartRef.current) {
      const unifiedData = getUnifiedChartData();

      if (unifiedData) {
        let chartOptions: ApexOptions;

        switch (chartType) {
          case "pie":
            chartOptions = getPieChartOptions(unifiedData);
            break;
          case "bar":
            chartOptions = getBarChartOptions(unifiedData);
            break;
          case "stackedBar":
            chartOptions = getStackedBarChartOptions(unifiedData);
            break;
          default:
            chartOptions = getPieChartOptions(unifiedData);
        }

        renderChart(chartOptions);
      } else {
        console.error("ERROR: No unified data found for chartId:", chartId);
      }
    }

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
  }, [chartType, chartId, category]);

  const renderChart = (chartOptions: ApexOptions) => {
    try {
      // Destroy existing chart instance if it exists
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }

      // Create new chart instance
      if (chartRef.current) {
        chartRef.current.innerHTML = "";
        chartInstanceRef.current = new ApexCharts(
          chartRef.current,
          chartOptions
        );
        chartInstanceRef.current
          .render()
          .then(() => {})
          .catch((error) => {
            console.error("Chart render failed:", error);
          });
      } else {
        console.error("chartRef.current is null - cannot render chart!");
      }
    } catch (error) {
      console.error("Chart rendering error:", error);
      console.error("Error details:", error);
    }
  };

  return (
    <Card
      className="p-4 rounded-3 mb-4"
      style={{ backgroundColor: "var(--card-bg-body)" }}
    >
      <div
        ref={chartRef}
        style={{
          minHeight: "400px",
          width: "100%",
        }}
      />
    </Card>
  );
};

function getContrastColor(hex: string): string {
  const clean = hex.replace("#", "");
  const r = parseInt(clean.slice(0, 2), 16);
  const g = parseInt(clean.slice(2, 4), 16);
  const b = parseInt(clean.slice(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.7 ? "#000000" : "#ffffff";
}

const getPieChartOptions = (chartData: UnifiedChartData): ApexOptions => {
  const labelColors = chartData.data.map((item) =>
    getContrastColor(item.color)
  );
  return {
    series: chartData.data.map((item) => item.value),
    chart: {
      type: "pie",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    labels: chartData.data.map((item) => item.name),
    colors: chartData.data.map((item) => item.color),
    legend: {
      position: "right",
      horizontalAlign: "left",
      labels: {
        colors: Array(chartData.data.length).fill("var(--chart-text)"),
        useSeriesColors: false,
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "11px",
        fontWeight: 500,
        colors: labelColors,
      },
      dropShadow: { enabled: false },
      formatter: (val: number) => Math.round(val) + "%",
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10,
        },
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    tooltip: {
      enabled: true,
      theme: "dark", // gives you a dark bg + white text
      fillSeriesColor: false, // prevent the tooltip marker from using the slice color
      style: {
        fontSize: "12px",
        fontFamily: "inherit",
      },
      y: {
        formatter: (val: number) => `${Math.round(val)}%`,
        title: {
          formatter: () => "",
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { height: 250 },
          legend: { position: "bottom" },
        },
      },
    ],
  };
};

// const getBarChartOptions = (chartData: UnifiedChartData): ApexOptions => {
//   // Validate input data
//   if (!chartData || !chartData.data || chartData.data.length === 0) {
//     return {
//       series: [],
//       chart: { type: "bar", height: 350 }
//     };
//   }

//   // Extract data arrays
//   const seriesData = chartData.data.map(item => item.value);
//   const categories = chartData.data.map(item => item.name);
//   const colors = chartData.data.map(item => item.color);

//   // Create bar chart configuration using the same structure as working charts
//   const options: ApexOptions = {
//     series: [{
//       name: "Responses",
//       data: seriesData
//     }],
//     chart: {
//       type: "bar",
//       height: 350,
//       toolbar: { show: false },
//       background: "transparent"
//     },
//     plotOptions: {
//       bar: {
//         horizontal: true,
//         barHeight: "55%"
//       }
//     },
//     dataLabels: {
//       enabled: true,
//       style: {
//         fontSize: "12px",
//         fontWeight: "bold"
//       }
//     },
//     xaxis: {
//       categories: categories,
//       title: {
//         text: "Number of Responses",
//         style: {
//           fontSize: "12px"
//         }
//       },
//       labels: {
//         style: {
//           fontSize: "12px"
//         }
//       }
//     },
//     yaxis: {
//       title: {
//         text: "Survey Options",
//         style: {
//           fontSize: "12px"
//         }
//       },
//       labels: {
//         style: {
//           fontSize: "12px"
//         }
//       }
//     },
//     colors: colors,
//     title: {
//       text: `${chartData.questionNumber}. ${chartData.title}`,
//       align: "left",
//       style: {
//         fontSize: "16px",
//         fontWeight: "bold"
//       }
//     },
//     subtitle: {
//       text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
//       align: "left",
//       style: {
//         fontSize: "12px",
//         color: "#666"
//       }
//     },
//     grid: {
//       show: true,
//       borderColor: "#e0e0e0"
//     },
//     tooltip: {
//       y: {
//         formatter: function (val: number) {
//           return val + " responses";
//         }
//       }
//     },
//     legend: {
//       show: false
//     }
//   };

//   return options;
// };

const getBarChartOptions = (chartData: UnifiedChartData): ApexOptions => {
  const totalResponses = chartData.data.reduce(
    (sum, item) => sum + item.value,
    0
  );

  const series = [
    {
      name: chartData.title,
      data: chartData.data.map((item) =>
        totalResponses > 0 ? (item.value / totalResponses) * 100 : 0
      ),
    },
  ];

  const categories = chartData.data.map((item) => item.name);
  const colors = chartData.data.map((item) => item.color);

  return {
    series,
    chart: {
      type: "bar",
      height: 200,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(0)}%`,
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    xaxis: {
      categories,
      max: 100,
      labels: {
        show: true,
        formatter: (val: string) => `${val}%`,
        style: {
          colors: "var(--chart-text)",
        },
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 500,
          colors: "var(--chart-text)",
        },
      },
    },
    grid: {
      show: false,
    },
    tooltip: {
      y: {
        formatter: (val: number) => `${val.toFixed(1)}%`,
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    colors,
    legend: {
      show: false, // no stacking = no multiple series = no need for legend
    },
  };
};

const getStackedBarChartOptions = (
  chartData: UnifiedChartData
): ApexOptions => {
  // Use the actual survey response data for stacked bar chart
  // Convert response data to percentage-based stacked segments
  const totalResponses = chartData.data.reduce(
    (sum, item) => sum + item.value,
    0
  );

  const series = chartData.data.map((item) => ({
    name: item.name,
    data: [totalResponses > 0 ? (item.value / totalResponses) * 100 : 0],
  }));

  const colors = chartData.data.map((item) => item.color);

  return {
    series,
    chart: {
      type: "bar",
      height: 200,
      stacked: true,
      stackType: "100%",
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        columnWidth: "10%",
        borderRadius: 5,
        borderRadiusWhenStacked: "last",
        borderRadiusApplication: "end",
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(0)}%`,
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    xaxis: {
      categories: [chartData.title],
      max: 100,
      labels: { show: false },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: { show: false },
    },
    grid: { show: false },
    tooltip: {
      y: {
        formatter: (val: number) => `${val.toFixed(1)}%`,
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    colors,
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      labels: {
        colors: Array(chartData.data.length).fill("var(--chart-text)"),
        useSeriesColors: false,
      },
    },
  };
};

export default ChartComponent;
