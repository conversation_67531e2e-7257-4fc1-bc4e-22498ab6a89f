import React, { useEffect, useRef } from "react";
import ApexCharts, { ApexOptions } from "apexcharts";
import { Card } from "react-bootstrap";
import { useSurveyChartData } from "../context/SurveyDataContext";
import {
  PieChartData,
  StackedBarChartData,
} from "../utils/responseDataTransformer";
import {
  emojiOptions,
  QUESTION_TYPE_OPTIONS,
  thumbsOptions,
} from "../../components/addQuestions/util/constant";
import { renderToStaticMarkup } from "react-dom/server";

interface ChartComponentProps {
  chartType: "pie" | "stackedBar";
  questionIndex?: number;
}

const ChartComponent: React.FC<ChartComponentProps> = ({
  chartType,
  questionIndex = 0,
}) => {
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstanceRef = useRef<ApexCharts | null>(null);
  const { getChartData, isLoading, error } = useSurveyChartData();

  useEffect(() => {
    if (!chartRef.current) return;

    // Get chart data from context
    const chartData = getChartData(chartType);

    console.log("chartData:>>>", chartData);

    if (!chartData || chartData.length === 0) {
      console.log("❌ No chart data available for type:", chartType);
      return;
    }

    // Get the specific question data based on questionIndex
    const questionData = chartData[questionIndex];

    console.log("questionData:>>", questionData);
    if (!questionData) {
      console.log("❌ No question data at index:", questionIndex);
      return;
    }

    // Destroy existing chart instance
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
      chartInstanceRef.current = null;
    }

    let chartOptions: ApexOptions;

    if (chartType === "pie") {
      chartOptions = getPieChartOptions(questionData as PieChartData);
    } else if (chartType === "stackedBar") {
      chartOptions = getStackedBarChartOptions(
        questionData as StackedBarChartData
      );
    } else {
      console.error("❌ Unsupported chart type:", chartType);
      return;
    }

    // 🔍 CRITICAL DEBUG: Check if chart options are valid
    if (!chartOptions || !chartOptions.series) {
      console.error("❌ Invalid chart options created:", chartOptions);
      return;
    }

    // Create new chart instance
    try {
      if (!chartOptions || !chartOptions.series) {
        console.error("❌ chartOptions:", chartOptions);
        return;
      }

      chartInstanceRef.current = new ApexCharts(chartRef.current, chartOptions);
      chartInstanceRef.current.render();
    } catch (error) {
      console.error("❌ Error rendering chart:", error);

      // 🔍 CRITICAL DEBUG: Special error logging for MULTIPLE_CHOICE
      if (questionData.responseType === "MULTIPLE_CHOICE") {
        console.error("🚨 Error details:", error);
      }

      // Display error in the chart container
      if (chartRef.current) {
        chartRef.current.innerHTML = `
          <div class="alert alert-warning" role="alert">
            <h6 class="alert-heading">Chart Rendering Error</h6>
            <p class="mb-0">Unable to render chart for "${questionData.questionText}". Please try refreshing the page.</p>
            <small>Error: ${error}</small>
          </div>
        `;
      }
    }
  }, [chartType, questionIndex, getChartData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4 custom-card">
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "200px" }}
        >
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading chart...</span>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 rounded-3 mb-4 custom-card">
        <div className="alert alert-danger" role="alert">
          <h6 className="alert-heading">Chart Error</h6>
          <p className="mb-0">{error}</p>
        </div>
      </Card>
    );
  }

  const chartData = getChartData(chartType);
  const questionData = chartData[questionIndex];

  const getQuestionTypeLabel = (responseType: string) => {
    const match = QUESTION_TYPE_OPTIONS.find(
      (option) => option.value === responseType
    );
    return match ? match.label : "";
  };

  if (!questionData) {
    return (
      <Card className="p-4 rounded-3 mb-4 custom-card">
        <div className="alert alert-info" role="alert">
          <h6 className="alert-heading">No Data Available</h6>
          <p className="mb-0">No chart data available for this question.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 rounded-3 custom-card">
      <div className="d-flex justify-content-between align-items-start mb-3">
        <div>
          <h6 className="mb-1 fw-bold">
            {questionIndex + 1}) {questionData.questionText}
          </h6>
          <div className="small">
            Response Type:{" "}
            <span className="badge bg-secondary">
              {getQuestionTypeLabel(questionData.responseType)}
            </span>
            {chartType === "pie" && (
              <span className="ms-2">
                Total Responses:{" "}
                <strong>{(questionData as PieChartData).totalResponses}</strong>
              </span>
            )}
            {chartType === "stackedBar" && (
              <span className="ms-2">
                Total Responses:{" "}
                <strong>
                  {(questionData as StackedBarChartData).totalResponses}
                </strong>
              </span>
            )}
          </div>
        </div>
      </div>
      <div ref={chartRef} style={{ minHeight: "300px" }}></div>
    </Card>
  );
};

// Helper function to get contrast color for text
const getContrastColor = (hexColor: string): string => {
  const clean = hexColor.replace("#", "");
  const r = parseInt(clean.slice(0, 2), 16);
  const g = parseInt(clean.slice(2, 4), 16);
  const b = parseInt(clean.slice(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.7 ? "#000000" : "#ffffff";
};

export const getIconForLabel = (value: string): string => {
  // Match Emoji Option
  const emoji = emojiOptions.find((opt) => opt.value === value);
  if (emoji) {
    return `${emoji.icon}`;
  }

  // Match Thumbs Option
  const thumb = thumbsOptions.find((opt) => opt.value === value);
  if (thumb) {
    const icon = thumb.icon;
    const svgMarkup = renderToStaticMarkup(
      React.createElement(icon, { size: 14 })
    );
    return `${svgMarkup}`;
  }

  // Fallback
  return value;
};

// Pie chart options
const getPieChartOptions = (chartData: PieChartData): ApexOptions => {
  const labelColors = chartData.colors.map((color) => getContrastColor(color));

  const options: ApexOptions = {
    series: chartData.series,
    chart: {
      type: "pie",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    labels: chartData.labels.map(getIconForLabel),
    colors: chartData.colors,
    legend: {
      position: "right",
      horizontalAlign: "left",
      fontSize: "12px",
      fontWeight: 500,
      offsetX: 10,
      labels: {
        colors: "var(--Rx-title)",
      },
      markers: {
        strokeWidth: 0,
        shape: "square",
        offsetX: -4,
      },
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10,
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return val > 5 ? Math.round(val) + "%" : "";
      },
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: labelColors,
      },
      dropShadow: {
        enabled: false,
      },
    },
    tooltip: {
      custom: function ({ series, seriesIndex, dataPointIndex, w }) {
        const rawName = w?.globals?.seriesNames?.[seriesIndex];
        const iconLabel = getIconForLabel(rawName);
        const value = Math.round(series[seriesIndex][dataPointIndex]);
        return `
      <div style="
        padding: 8px 12px;
        background: rgba(30, 30, 30, 0.85);
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #fff;
        font-size: 13px;
      ">
        <div style="margin-bottom: 4px; minWidth: 50px;"></div>
        <div style="display: flex; align-items: center;">
          <span style="
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: ${w.config.colors[seriesIndex]};
            border-radius: 50%;
            margin-right: 8px;
          "></span>
          <span>${iconLabel}</span>
        </div>
      </div>
    `;
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          legend: {
            position: "bottom",
            horizontalAlign: "center",
          },
        },
      },
    ],
  };
  return options;
};

// Stacked bar chart options
const getStackedBarChartOptions = (
  chartData: StackedBarChartData
): ApexOptions => {
  const options: ApexOptions = {
    series: chartData.series,
    chart: {
      type: "bar",
      height: 120,
      stacked: true,
      stackType: "100%",
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
        dataLabels: {
          total: {
            enabled: true,
            style: {
              fontSize: "12px",
              fontWeight: 600,
            },
          },
        },
      },
    },
    xaxis: {
      categories: chartData.categories,
      labels: {
        show: false,
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      show: false,
      labels: {
        show: false,
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    grid: {
      show: false,
    },
    colors: chartData.colors,
    legend: {
      position: "bottom",
      fontSize: "12px",
      fontWeight: 500,
      offsetX: 10,
      labels: {
        colors: "var(--Rx-title)",
      },
      markers: {
        strokeWidth: 0,
        shape: "square",
        offsetX: -4,
      },
      formatter: function (seriesName: string) {
        return getIconForLabel(seriesName);
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return val > 5 ? Math.round(val) + "%" : "";
      },
      style: {
        fontSize: "11px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    tooltip: {
      custom: function ({ series, seriesIndex, dataPointIndex, w }) {
        const rawName = w?.globals?.seriesNames?.[seriesIndex];
        const iconLabel = getIconForLabel(rawName);
        const value = Math.round(series[seriesIndex][dataPointIndex]);
        return `
      <div style="
        padding: 8px 12px;
        background: #1f1f1f;
        border-radius: 8px;
        color: #fff;
        font-size: 13px;
      ">
        <div style="margin-bottom: 4px; minWidth: 90px;"></div>
        <div style="display: flex; align-items: center;">
          <span style="
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: ${w.config.colors[seriesIndex]};
            border-radius: 50%;
            margin-right: 8px;
          "></span>
          <span>${iconLabel}</span>
          <span style="margin-left: auto;">${value}%</span>
        </div>
      </div>
    `;
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          plotOptions: {
            bar: {
              barHeight: "80%",
            },
          },
        },
      },
    ],
  };

  return options;
};

export default ChartComponent;
