import React, { useEffect, useRef } from "react";
import ApexCharts, { ApexOptions } from "apexcharts";
import { Card } from "react-bootstrap";
import surveyConfig from "../data/surveyConfig.json";

interface ChartComponentProps {
  chartType: "pie" | "donut" | "bar" | "stackedBar" | "line" | "area" | "scatter" | "category";
  chartId?: number;
  category?: string;
}

const ChartComponent: React.FC<ChartComponentProps> = ({
  chartType,
  chartId,
  category,
}) => {
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstanceRef = useRef<ApexCharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      // Find the appropriate chart data based on props
      let chartData;
      let chartOptions: ApexOptions;

      if (chartType === "pie") {
        chartData = surveyConfig.chartData.pieCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getPieChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "donut") {
        chartData = surveyConfig.chartData.pieCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getDonutChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "bar") {
        chartData = surveyConfig.chartData.barCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getBarChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "stackedBar") {
        // For stacked bar, create chart data with the chartId for variations
        const stackedBarData = { id: chartId, title: "Survey Topic" };
        chartOptions = getStackedBarChartOptions(stackedBarData);
        renderChart(chartOptions);
      } else if (chartType === "line") {
        chartData = surveyConfig.chartData.barCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getLineChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "area") {
        chartData = surveyConfig.chartData.barCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getAreaChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "scatter") {
        chartData = surveyConfig.chartData.barCharts.find(
          (chart) => chart.id === chartId
        );
        if (chartData) {
          chartOptions = getScatterChartOptions(chartData);
          renderChart(chartOptions);
        }
      } else if (chartType === "category") {
        chartData = surveyConfig.chartData.categoryCharts.find(
          (chart) => chart.category === category
        );
        if (chartData) {
          chartOptions = getCategoryChartOptions(chartData);
          renderChart(chartOptions);
        }
      }
    }

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
  }, [chartType, chartId, category]);

  const renderChart = (chartOptions: ApexOptions) => {
    // If we already have a chart instance, update it instead of recreating
    if (chartInstanceRef.current) {
      chartInstanceRef.current.updateOptions(chartOptions, true, true, true);
    } else {
      // Clear any existing content
      if (chartRef.current) {
        chartRef.current.innerHTML = "";
        chartInstanceRef.current = new ApexCharts(chartRef.current, chartOptions);
        chartInstanceRef.current.render();
      }
    }
  };

  return (
    <Card
      className="p-4 rounded-3 mb-4"
      style={{ backgroundColor: "var(--card-bg-body)" }}
    >
      <div ref={chartRef} />
    </Card>
  );
};

function getContrastColor(hex: string): string {
  const clean = hex.replace("#", "");
  const r = parseInt(clean.slice(0, 2), 16);
  const g = parseInt(clean.slice(2, 4), 16);
  const b = parseInt(clean.slice(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.7 ? "#000000" : "#ffffff";
}

const getPieChartOptions = (chartData: any): ApexOptions => {
  const labelColors = chartData.data.map((item: any) =>
    getContrastColor(item.color)
  );
  return {
    series: chartData.data.map((item: any) => item.value),
    chart: {
      type: "pie",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    labels: chartData.data.map((item: any) => item.name),
    colors: chartData.data.map((item: any) => item.color),
    legend: {
      position: "right",
      horizontalAlign: "left",
      labels: {
        colors: Array(chartData.data.length).fill("var(--chart-text)"),
        useSeriesColors: false,
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "11px",
        fontWeight: 500,
        colors: labelColors,
      },
      dropShadow: { enabled: false },
      formatter: (val: number) => Math.round(val) + "%",
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10,
        },
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    tooltip: {
      enabled: true,
      theme: "dark", // gives you a dark bg + white text
      fillSeriesColor: false, // prevent the tooltip marker from using the slice color
      style: {
        fontSize: "12px",
        fontFamily: "inherit",
      },
      y: {
        formatter: (val: number) => `${Math.round(val)}%`,
        title: {
          formatter: () => "",
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { height: 250 },
          legend: { position: "bottom" },
        },
      },
    ],
  };
};

const getBarChartOptions = (chartData: any): ApexOptions => {
  const max = chartData.maxValue ?? 5;
  const value = chartData.averageScore;
  const remainder = Math.max(0, max - value);
  const pct = ((value / max) * 100).toFixed(0);

  return {
    series: [
      {
        name: "Filled",
        data: [value],
      },
      {
        name: "Remaining",
        data: [remainder],
      },
    ],
    chart: {
      type: "bar",
      height: 120,
      stacked: true, // stack filled + remaining bars
      fontFamily: "inherit",
      toolbar: { show: false },
      offsetY: 10,
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
        // rounded on only left side of 'Filled', right side of 'Remaining'
        borderRadiusApplication: "end",
        borderRadiusWhenStacked: "last",
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (_val: number, opts: any) => {
        // Only show label on first (green) bar
        return opts.seriesIndex === 0 ? value.toFixed(1) + `/${max}` : "";
      },
      style: {
        fontSize: "14px",
        fontWeight: "bold",
      },
    },
    annotations: {
      points: [
        {
          x: value,
          y: 0,
          marker: {
            size: 0,
            fillColor: "transparent",
            strokeColor: "transparent",
            strokeWidth: 0,
            shape: "circle",
          },
          label: {
            text: `${pct}%`,
            // offsetX: 100,
            offsetY: 31,
            // position: "top",
            style: {
              fontSize: "12px",
              fontWeight: "bold",
              color: "var(--chart-text)",
              background: "transparent",
            },
            textAnchor: "start",
          },
        },
      ],
    },
    xaxis: {
      categories: [chartData.title],
      max: max,
      labels: { show: false },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: { show: false },
    },
    grid: { show: false },
    legend: { show: false },
    tooltip: {
      y: {
        formatter: function (val: number) {
          return val.toFixed(1);
        },
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    colors: ["#4CAF50", "#E0E0E0"], // green + light gray
  };
};

export const getStacked100BarChartOptions = (chartData: {
  title: string;
  questionNumber: number;
  totalResponses: number;
  answeredResponses: number;
  segments: Array<{ name: string; value: number; color: string }>;
}): ApexOptions => {
  // Build series & colors from segments[]
  const series = chartData.segments.map((seg) => ({
    name: seg.name,
    data: [seg.value],
  }));
  const colors = chartData.segments.map((seg) => seg.color);

  return {
    series,
    chart: {
      type: "bar",
      height: 120,
      stacked: true,
      stackType: "100%",
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
        borderRadiusWhenStacked: "last",
        borderRadiusApplication: "end",
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(0)}%`,
      style: {
        fontSize: "14px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    xaxis: {
      categories: [chartData.title],
      max: 100,
      labels: { show: false },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: { labels: { show: false } },
    grid: { show: false },
    tooltip: {
      y: {
        formatter: (val: number) => `${val.toFixed(1)}%`,
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.answeredResponses} of ${chartData.totalResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    colors,
    legend: { show: false },
  };
};

const getCategoryChartOptions = (chartData: any): ApexOptions => {
  return {
    series: chartData.data.map((item: any) => item.value),
    chart: {
      type: "donut",
      height: 300,
      fontFamily: "inherit",
      toolbar: {
        show: false,
      },
    },
    labels: chartData.data.map((item: any) => item.name),
    colors: chartData.data.map((item: any) => item.color),
    legend: {
      position: "bottom",
      horizontalAlign: "center",
    },
    plotOptions: {
      pie: {
        donut: {
          labels: {
            show: true,
            total: {
              show: true,
              label: "Total",
              formatter: function () {
                return (
                  chartData.answeredResponses + "/" + chartData.totalResponses
                );
              },
            },
          },
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return Math.round(val) + "%";
      },
    },
    title: {
      text: chartData.category + " Sentiment",
      align: "center",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };
};

const getDonutChartOptions = (chartData: any): ApexOptions => {
  const labelColors = chartData.data.map((item: any) =>
    getContrastColor(item.color)
  );
  return {
    series: chartData.data.map((item: any) => item.value),
    chart: {
      type: "donut",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    labels: chartData.data.map((item: any) => item.name),
    colors: chartData.data.map((item: any) => item.color),
    legend: {
      position: "right",
      horizontalAlign: "left",
      labels: {
        colors: Array(chartData.data.length).fill("var(--chart-text)"),
        useSeriesColors: false,
      },
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "11px",
        fontWeight: 500,
        colors: labelColors,
      },
      dropShadow: { enabled: false },
      formatter: (val: number) => Math.round(val) + "%",
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        donut: {
          size: "65%",
          labels: {
            show: true,
            total: {
              show: true,
              label: "Total",
              formatter: function () {
                return chartData.answeredResponses + "/" + chartData.totalResponses;
              },
            },
          },
        },
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10,
        },
      },
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    tooltip: {
      enabled: true,
      theme: "dark",
      fillSeriesColor: false,
      style: {
        fontSize: "12px",
        fontFamily: "inherit",
      },
      y: {
        formatter: (val: number) => `${Math.round(val)}%`,
        title: {
          formatter: () => "",
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { height: 250 },
          legend: { position: "bottom" },
        },
      },
    ],
  };
};

const getLineChartOptions = (chartData: any): ApexOptions => {
  return {
    series: [{
      name: chartData.title,
      data: chartData.data ? chartData.data.map((item: any) => item.value) : [chartData.averageScore]
    }],
    chart: {
      type: "line",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    stroke: {
      curve: "smooth",
      width: 3,
    },
    xaxis: {
      categories: chartData.data ? chartData.data.map((item: any) => item.name) : [chartData.title],
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    colors: ["#4CAF50"],
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    grid: {
      borderColor: "var(--Gray-3)",
    },
  };
};

const getAreaChartOptions = (chartData: any): ApexOptions => {
  return {
    series: [{
      name: chartData.title,
      data: chartData.data ? chartData.data.map((item: any) => item.value) : [chartData.averageScore]
    }],
    chart: {
      type: "area",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.3,
      },
    },
    xaxis: {
      categories: chartData.data ? chartData.data.map((item: any) => item.name) : [chartData.title],
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    colors: ["#2196F3"],
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    grid: {
      borderColor: "var(--Gray-3)",
    },
  };
};

const getScatterChartOptions = (chartData: any): ApexOptions => {
  return {
    series: [{
      name: chartData.title,
      data: chartData.data ? chartData.data.map((item: any, index: number) => [index + 1, item.value]) : [[1, chartData.averageScore]]
    }],
    chart: {
      type: "scatter",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    xaxis: {
      type: "numeric",
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: "var(--chart-text)",
        },
      },
    },
    colors: ["#FF9800"],
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    subtitle: {
      text: `${chartData.totalResponses} out of responses ${chartData.answeredResponses} answered`,
      align: "left",
      style: {
        fontSize: "12px",
        color: "var(--Gray-1)",
      },
    },
    grid: {
      borderColor: "var(--Gray-3)",
    },
  };
};

const getStackedBarChartOptions = (chartData: any): ApexOptions => {
  // Create different data sets based on chart ID
  const dataVariations = {
    1: [
      { name: "Value", value: 30, color: "#6366F1" },
      { name: "Facilities", value: 5, color: "#F59E0B" },
      { name: "Experience", value: 15, color: "#10B981" },
      { name: "Functionality", value: 10, color: "#6B7280" },
      { name: "Quality", value: 15, color: "#8B5CF6" }
    ],
    2: [
      { name: "Performance", value: 25, color: "#EF4444" },
      { name: "Usability", value: 20, color: "#3B82F6" },
      { name: "Design", value: 18, color: "#10B981" },
      { name: "Features", value: 12, color: "#F59E0B" },
      { name: "Support", value: 10, color: "#8B5CF6" }
    ]
  };

  const chartId = chartData?.id || 1;
  const segments = dataVariations[chartId as keyof typeof dataVariations] || dataVariations[1];

  const series = segments.map((seg) => ({
    name: seg.name,
    data: [seg.value],
  }));
  const colors = segments.map((seg) => seg.color);

  return {
    series,
    chart: {
      type: "bar",
      height: 120,
      stacked: true,
      stackType: "100%",
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
        borderRadiusWhenStacked: "last",
        borderRadiusApplication: "end",
      },
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => `${val.toFixed(0)}%`,
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    xaxis: {
      categories: ["Survey Topic"],
      max: 100,
      labels: { show: false },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: { show: false },
    },
    grid: { show: false },
    tooltip: {
      y: {
        formatter: (val: number) => `${val.toFixed(1)}%`,
      },
    },
    title: {
      text: "Survey Topic",
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
        color: "var(--chart-text)",
      },
    },
    colors,
    legend: {
      show: true,
      position: "bottom",
      horizontalAlign: "center",
      labels: {
        colors: Array(segments.length).fill("var(--chart-text)"),
        useSeriesColors: false,
      },
    },
  };
};

export default ChartComponent;
