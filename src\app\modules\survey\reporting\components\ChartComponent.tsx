import React, { useEffect, useRef } from "react";
import ApexCharts, { ApexOptions } from "apexcharts";
import { Card } from "react-bootstrap";
import { useSurveyChartData } from "../context/SurveyDataContext";
import {
  PieChartData,
  StackedBarChartData,
} from "../utils/responseDataTransformer";

interface ChartComponentProps {
  chartType: "pie" | "stackedBar";
  questionIndex?: number; // Index of the question to display
}

const ChartComponent: React.FC<ChartComponentProps> = ({
  chartType,
  questionIndex = 0,
}) => {
  const chartRef = useRef<HTMLDivElement | null>(null);
  const chartInstanceRef = useRef<ApexCharts | null>(null);
  const { getChartData, isLoading, error } = useSurveyChartData();

  useEffect(() => {
    if (!chartRef.current) return;

    // Get chart data from context
    const chartData = getChartData(chartType);

    console.log("🔍 ChartComponent: Chart type:", chartType);
    console.log("🔍 ChartComponent: Chart data:", chartData);
    console.log(
      "🔍 ChartComponent: Chart data length:",
      chartData?.length || 0
    );
    console.log("🔍 ChartComponent: Question index:", questionIndex);

    if (!chartData || chartData.length === 0) {
      console.log("❌ No chart data available for type:", chartType);
      return;
    }

    // Get the specific question data based on questionIndex
    const questionData = chartData[questionIndex];
    if (!questionData) {
      console.log("❌ No question data at index:", questionIndex);
      return;
    }

    console.log("🔄 Rendering chart for question:", questionData.questionText);
    console.log("🔄 Question response type:", questionData.responseType);
    console.log("🔄 Chart data:", questionData);

    // 🔍 CRITICAL DEBUG: Specific MULTIPLE_CHOICE tracking
    if (questionData.responseType === "MULTIPLE_CHOICE") {
      console.log("🔍 MULTIPLE_CHOICE question detected in ChartComponent!");
      console.log("🔍 MULTIPLE_CHOICE series:", questionData.series);
      console.log("🔍 MULTIPLE_CHOICE labels:", (questionData as any).labels);
      console.log("🔍 MULTIPLE_CHOICE colors:", questionData.colors);
      console.log(
        "🔍 MULTIPLE_CHOICE totalResponses:",
        questionData.totalResponses
      );
      console.log("🔍 MULTIPLE_CHOICE chart type:", chartType);
    }

    // Destroy existing chart instance
    if (chartInstanceRef.current) {
      chartInstanceRef.current.destroy();
      chartInstanceRef.current = null;
    }

    let chartOptions: ApexOptions;

    console.log("🔍 Creating chart options for type:", chartType);
    console.log("🔍 Question data for chart options:", questionData);

    if (chartType === "pie") {
      console.log(
        "🔍 Creating PIE chart options for:",
        questionData.questionText
      );
      chartOptions = getPieChartOptions(questionData as PieChartData);
      console.log("🔍 PIE chart options created:", chartOptions);
    } else if (chartType === "stackedBar") {
      console.log(
        "🔍 Creating STACKED BAR chart options for:",
        questionData.questionText
      );
      chartOptions = getStackedBarChartOptions(
        questionData as StackedBarChartData
      );
      console.log("🔍 STACKED BAR chart options created:", chartOptions);
    } else {
      console.error("❌ Unsupported chart type:", chartType);
      return;
    }

    // 🔍 CRITICAL DEBUG: Check if chart options are valid
    if (!chartOptions || !chartOptions.series) {
      console.error("❌ Invalid chart options created:", chartOptions);
      return;
    }

    console.log(
      "✅ Chart options validation passed for:",
      questionData.questionText
    );

    // Create new chart instance
    try {
      console.log("🔍 Starting chart creation process...");
      console.log("🔍 Chart container ref:", chartRef.current);
      console.log("🔍 Chart options series:", chartOptions.series);

      if (!chartOptions || !chartOptions.series) {
        console.error("❌ Invalid chart options or missing series data");
        console.error("❌ chartOptions:", chartOptions);
        return;
      }

      console.log("🔍 Creating ApexCharts instance...");
      chartInstanceRef.current = new ApexCharts(chartRef.current, chartOptions);

      console.log("🔍 Rendering chart...");
      chartInstanceRef.current.render();

      console.log(
        "✅ Chart rendered successfully for:",
        questionData.questionText
      );
      console.log("✅ Chart type:", chartType);
      console.log("✅ Response type:", questionData.responseType);

      // 🔍 CRITICAL DEBUG: Special logging for MULTIPLE_CHOICE
      if (questionData.responseType === "MULTIPLE_CHOICE") {
        console.log("🎉 MULTIPLE_CHOICE chart rendered successfully!");
        console.log(
          "🎉 Chart container HTML:",
          chartRef.current?.innerHTML?.substring(0, 200)
        );
      }
    } catch (error) {
      console.error("❌ Error rendering chart:", error);
      console.error("❌ Question:", questionData.questionText);
      console.error("❌ Chart type:", chartType);
      console.error("❌ Response type:", questionData.responseType);

      // 🔍 CRITICAL DEBUG: Special error logging for MULTIPLE_CHOICE
      if (questionData.responseType === "MULTIPLE_CHOICE") {
        console.error("🚨 MULTIPLE_CHOICE chart failed to render!");
        console.error("🚨 Error details:", error);
        console.error("🚨 Chart options:", chartOptions);
      }

      // Display error in the chart container
      if (chartRef.current) {
        chartRef.current.innerHTML = `
          <div class="alert alert-warning" role="alert">
            <h6 class="alert-heading">Chart Rendering Error</h6>
            <p class="mb-0">Unable to render chart for "${questionData.questionText}". Please try refreshing the page.</p>
            <small>Error: ${error}</small>
          </div>
        `;
      }
    }
  }, [chartType, questionIndex, getChartData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.destroy();
        chartInstanceRef.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "200px" }}
        >
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading chart...</span>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <div className="alert alert-danger" role="alert">
          <h6 className="alert-heading">Chart Error</h6>
          <p className="mb-0">{error}</p>
        </div>
      </Card>
    );
  }

  const chartData = getChartData(chartType);
  const questionData = chartData[questionIndex];

  if (!questionData) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <div className="alert alert-info" role="alert">
          <h6 className="alert-heading">No Data Available</h6>
          <p className="mb-0">No chart data available for this question.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 rounded-3 mb-4">
      <div className="d-flex justify-content-between align-items-start mb-3">
        <div>
          <h6 className="mb-1 text-dark fw-bold">
            {questionData.questionText}
          </h6>
          <div className="text-muted small">
            Response Type:{" "}
            <span className="badge bg-secondary">
              {questionData.responseType}
            </span>
            {chartType === "pie" && (
              <span className="ms-2">
                Total Responses:{" "}
                <strong>{(questionData as PieChartData).totalResponses}</strong>
              </span>
            )}
            {chartType === "stackedBar" && (
              <span className="ms-2">
                Total Responses:{" "}
                <strong>
                  {(questionData as StackedBarChartData).totalResponses}
                </strong>
              </span>
            )}
          </div>
        </div>
      </div>
      <div ref={chartRef} style={{ minHeight: "300px" }}></div>
    </Card>
  );
};

// Helper function to get contrast color for text
const getContrastColor = (hexColor: string): string => {
  const clean = hexColor.replace("#", "");
  const r = parseInt(clean.slice(0, 2), 16);
  const g = parseInt(clean.slice(2, 4), 16);
  const b = parseInt(clean.slice(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.7 ? "#000000" : "#ffffff";
};

// Pie chart options
const getPieChartOptions = (chartData: PieChartData): ApexOptions => {
  console.log("🔍 getPieChartOptions called with:", chartData);
  console.log("🔍 PIE series data:", chartData.series);
  console.log("🔍 PIE labels data:", chartData.labels);
  console.log("🔍 PIE colors data:", chartData.colors);
  console.log("🔍 PIE response type:", chartData.responseType);

  const labelColors = chartData.colors.map((color) => getContrastColor(color));

  const options: ApexOptions = {
    series: chartData.series,
    chart: {
      type: "pie",
      height: 170,
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    labels: chartData.labels,
    colors: chartData.colors,
    legend: {
      position: "right",
      horizontalAlign: "left",
      fontSize: "12px",
      fontWeight: 500,
      offsetX: 10,
      // itemMargin: {
      //   horizontal: 5,
      //   vertical: 5,
      // },
      // markers: {
      //   size: 12,
      // },
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10,
        },
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return Math.round(val) + "%";
      },
      style: {
        fontSize: "12px",
        fontWeight: "bold",
        colors: labelColors,
      },
      dropShadow: {
        enabled: false,
      },
    },
    tooltip: {
      enabled: true,
      theme: "dark",
      fillSeriesColor: false,
      y: {
        formatter: function (val: number, opts: any) {
          try {
            if (
              opts &&
              opts.w &&
              opts.w.globals &&
              opts.w.globals.seriesPercent &&
              opts.w.globals.seriesPercent[opts.seriesIndex]
            ) {
              const percentage =
                opts.w.globals.seriesPercent[opts.seriesIndex][0];
              return `${val} responses (${Math.round(percentage)}%)`;
            }
            return `${val} responses`;
          } catch (error) {
            console.warn("Tooltip formatter error:", error);
            return `${val} responses`;
          }
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          legend: {
            position: "bottom",
            horizontalAlign: "center",
          },
        },
      },
    ],
  };

  console.log("✅ PIE chart options created:", options);
  console.log("✅ PIE chart series in options:", options.series);
  console.log("✅ PIE chart labels in options:", options.labels);

  // 🔍 CRITICAL DEBUG: Special logging for MULTIPLE_CHOICE
  if (chartData.responseType === "MULTIPLE_CHOICE") {
    console.log("🎉 PIE chart options created for MULTIPLE_CHOICE!");
    console.log("🎉 MULTIPLE_CHOICE series:", options.series);
    console.log("🎉 MULTIPLE_CHOICE labels:", options.labels);
  }

  return options;
};

// Stacked bar chart options
const getStackedBarChartOptions = (
  chartData: StackedBarChartData
): ApexOptions => {
  console.log("🔍 getStackedBarChartOptions called with:", chartData);
  console.log("🔍 STACKED BAR series data:", chartData.series);
  console.log("🔍 STACKED BAR categories data:", chartData.categories);
  console.log("🔍 STACKED BAR colors data:", chartData.colors);
  console.log("🔍 STACKED BAR response type:", chartData.responseType);

  const options: ApexOptions = {
    series: chartData.series,
    chart: {
      type: "bar",
      height: 200,
      stacked: true,
      stackType: "100%",
      fontFamily: "inherit",
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: "34px",
        borderRadius: 5,
        dataLabels: {
          total: {
            enabled: true,
            style: {
              fontSize: "12px",
              fontWeight: 600,
            },
          },
        },
      },
    },
    xaxis: {
      categories: chartData.categories,
      labels: {
        formatter: function (val: string) {
          return val + "%";
        },
      },
      axisBorder: { show: false },
      axisTicks: { show: false },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
          fontWeight: 500,
          colors: "var(--chart-text)",
        },
      },
      title: {
        text: undefined,
      },
    },
    grid: {
      show: false,
    },
    colors: chartData.colors,
    legend: {
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "14px",
      fontWeight: 500,
      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val: number) {
        return val > 5 ? Math.round(val) + "%" : "";
      },
      style: {
        fontSize: "11px",
        fontWeight: "bold",
        colors: ["#fff"],
      },
    },
    tooltip: {
      y: {
        formatter: function (val: number, opts: any) {
          try {
            if (
              opts &&
              opts.w &&
              opts.w.globals &&
              opts.w.globals.seriesNames &&
              opts.w.globals.seriesNames[opts.seriesIndex]
            ) {
              const seriesName = opts.w.globals.seriesNames[opts.seriesIndex];
              return `${seriesName}: ${Math.round(val)}%`;
            }
            return `${Math.round(val)}%`;
          } catch (error) {
            console.warn("Stacked bar tooltip formatter error:", error);
            return `${Math.round(val)}%`;
          }
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          plotOptions: {
            bar: {
              barHeight: "80%",
            },
          },
        },
      },
    ],
  };

  console.log("✅ STACKED BAR chart options created:", options);
  console.log("✅ STACKED BAR chart series in options:", options.series);
  console.log(
    "✅ STACKED BAR chart categories in options:",
    options.xaxis?.categories
  );

  // 🔍 CRITICAL DEBUG: Special logging for MULTIPLE_CHOICE
  if (chartData.responseType === "MULTIPLE_CHOICE") {
    console.log("🎉 STACKED BAR chart options created for MULTIPLE_CHOICE!");
    console.log("🎉 MULTIPLE_CHOICE series:", options.series);
    console.log("🎉 MULTIPLE_CHOICE categories:", options.xaxis?.categories);
  }

  return options;
};

export default ChartComponent;
