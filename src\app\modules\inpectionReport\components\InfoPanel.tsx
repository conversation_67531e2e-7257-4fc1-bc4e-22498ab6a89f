import { Card } from "react-bootstrap";
import InspectorCard from "./InspectorCard";
import ScoreCard from "./ScoreCard";
import { InspectionReportDetails } from "../../../apis/type";
import InspectionSummaryDetails from "./InspectionSummaryDetails";

interface Props {
  report: InspectionReportDetails;
}

const InfoPanel: React.FC<Props> = ({ report }) => {
  return (
    <div
      className="w-100 w-md-auto p-3 p-md-4 overflow-auto"
      style={{ minWidth: 320, maxWidth: 400 }}
    >
      <Card className="border-0 rounded-3 mb-3">
        <Card.Body className="p-3 p-md-4">
          <InspectionSummaryDetails report={report} />
          {report?.inspectionStatus?.toLowerCase() !== "in progress" && (
            <Card className="w-100 border-0 rounded-4 shadow-sm p-4 mt-3">
              <ScoreCard
                scoreData={report.inspectionScoreResponseDto}
                finalScore={report?.finalScore}
              />
            </Card>
          )}
        </Card.Body>
      </Card>

      {report?.inspectionStatus?.toLowerCase() !== "in progress" && (
        <InspectorCard report={report} />
      )}
    </div>
  );
};

export default InfoPanel;
