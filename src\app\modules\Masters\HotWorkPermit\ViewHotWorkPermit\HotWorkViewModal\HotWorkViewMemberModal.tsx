import React from "react";
import { <PERSON>, <PERSON><PERSON>, Row } from "react-bootstrap";
import { RxCross2 } from "react-icons/rx";

export const HotWorkViewMemberModal = ({
  memberSortingModal,
  setMemberSortinfModal,
}: any) => {
  const handleSubmit = () => {
    setMemberSortinfModal(false);
  };

  return (
    <>
      <Modal
        className="modal-right modal-right-small p-0"
        scrollable={true}
        show={memberSortingModal}
        onHide={() => {
          // setselctedMembers([]);
          setMemberSortinfModal(false);
        }}
      >
        <Modal.Header className=" border-0 p-0">
          <Row className="align-items-baseline">
            <Col xs={10} className="mt-auto mb-auto">
              <h2 className="mb-0">Sorting</h2>
            </Col>
            <Col xs={2} className=" text-end mb-3">
              <span
                className="close-btn cursor-pointer"
                onClick={() => {
                  // setselctedMembers([]);
                  setMemberSortinfModal(false);
                }}
              >
                <RxCross2 fontSize={20} />
              </span>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="mt-5 p-0">
          <Row className="align-items-baseline justify-content-start gap-5">
            <Col
              sm={12}
              className="d-flex align-items-center justify-content-start gap-3 border-bottom p-5"
            >
              <input type="radio" name="sorting-ticket" />
              <label className="cursor-pointer">Sort by Role</label>
            </Col>
            <Col
              sm={12}
              className="d-flex align-items-center justify-content-start gap-3 border-bottom p-5"
            >
              <input type="radio" name="sorting-ticket" />
              <label className="cursor-pointer">Sort by Active</label>
            </Col>
            <Col
              sm={12}
              className="d-flex align-items-center justify-content-start gap-3 border-bottom p-5"
            >
              <input type="radio" name="sorting-ticket" />
              <label className="cursor-pointer">Sort by Inctive</label>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer className="border-0 p-0 ">
          <span className="btn rx-btn" onClick={() => handleSubmit()}>
            Save & Continue
          </span>
        </Modal.Footer>
      </Modal>
    </>
  );
};
