import { useEffect, useState } from "react";
import { Card, Col, Row } from "react-bootstrap";
import { MdOutlineEdit } from "react-icons/md";
import { useLocation, useNavigate } from "react-router";
import departmentSVG from "../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/Departments.svg";
import { useBreadcrumbContext } from "../../../../../_metronic/layout/components/header/BreadcrumbsContext";
import { getAccessDetails, getImage } from "../../../../utils/CommonUtils";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import Spinner from "../../../common/Spinner";
import SwalMessage from "../../../common/SwalMessage";
import { departmentService } from "../Department.helpers";
import { LoopVendorTab } from "./MemberDepartmentViewTab/LoopVendorTab";
import { MemberTabDepartment } from "./MemberDepartmentViewTab/MemberTabViewDepartment";
import { TicketTabDepartment } from "./TicketDepartmentViewTab/TicketTabViewDepartment";
import Inspection from "../../../pages/Inspection";

type ActiveTab =
  | "MemberTab"
  | "PropertyTab"
  | "TicketTab"
  | "SafetyFormsTab"
  | "QrCodeTab"
  | "InspectionsTab"
  | "CalendarTab"
  | "LoopVendorsTab";

export const ViewDepartment = () => {
  const { setLabels } = useBreadcrumbContext();

  const [refreshKey, setRefreshKey] = useState(0);
  const [activeTab, setactiveTab] = useState<ActiveTab>("TicketTab");
  const refreshGrid = (Tab: ActiveTab) => {
    setRefreshKey((oldKey) => oldKey + 1);
    setactiveTab(Tab);
  };
  const [DepartmentImage, SetDepartmentImage] = useState<any>();
  const [deaprtmentName, setDepartmentName] = useState<any>();
  const [propertyName, setPropertyName] = useState<any>();
  const [propertyid, setpropertyid] = useState<string>("");
  const [userType, setuserType] = useState<any>();
  const [isexternal, setIsexternal] = useState<boolean>(false)
  const [departmentheadId, setDepartmentheadId] = useState("")
  const { state } = useLocation();
  const navigate = useNavigate();
  const { moduleId, actions, hasAction } = getAccessDetails();

  // Simple boolean flags
  const [isDepartmentLoading, setIsDepartmentLoading] = useState(false);
  const [isTicketLoading, setIsTicketLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Combined loading state
  const isLoading = (isDepartmentLoading || isTicketLoading) && !hasError;
  // department single api function.
  useEffect(() => {
    const user: any = JSON.parse(localStorage.getItem("userinfo") as string);
    setIsDepartmentLoading(true)
    setuserType(user);

    if (state?.id) {
      let keyinfo = JSON.parse(localStorage.keyinfo);
      departmentService
        .getSingleDepartmentData(state?.id)
        .then((response) => {
          if (response.status === 200) {
            if (response.data.success === true) {
              const result = encryptDecryptUtil.decryptData(
                response.data.data,
                keyinfo.syckey
              );
              const encResponse = JSON.parse(result);
              // console.log(encResponse);
              setIsexternal(encResponse?.isExternal);
              setIsDepartmentLoading(false)
              setDepartmentName(encResponse?.departmentname);
              SetDepartmentImage(encResponse?.photo);
              setPropertyName(encResponse?.property_name);
              setpropertyid(encResponse?.propertyid);
              setDepartmentheadId(encResponse?.departmenthead)
              setLabels([{ path: "", state: {}, breadcrumb: "" }]);
              setLabels([
                { path: "/departments", state: {}, breadcrumb: "Department" },
                {
                  path: "",
                  state: {},
                  breadcrumb: encResponse?.departmentname,
                },
              ]);
            } else {
              setIsDepartmentLoading(false)
              SwalMessage(null, response.data.errormsg, "Ok", "error", false);
              setHasError(true)
            }
          }
        })
        .catch((error: any) => {
          if (error.response?.status == 401) {
            setIsDepartmentLoading(false)
            // localStorage.removeItem("islogin");
            navigate("/dashboard");
          } else if (error?.code === "ERR_NETWORK") {
            setIsDepartmentLoading(false)
            SwalMessage(null, error?.message, "Ok", "error", false);
            setHasError(true)
          }
          setIsDepartmentLoading(false)
          SwalMessage(null, error?.message, "Ok", "error", false);
          setHasError(true)
        })
        .finally(() => {
          setIsDepartmentLoading(false)
        });
    }
  }, [state?.id]);

  const handleDepartmentEdit = (id: any) => {
    navigate("/departments/adddepartments", { state: { id: id, isDepartmentView: true } });
  };
  return (
    <>
      {isLoading && <Spinner />}
      <div className="parent">
        <div className="tab-container-section px-4 py-4 custom-card border-radius-xl">
          <Row>
            <Col sm={6}>
              <div className="bank-section d-flex align-items-center gap-5 ">
                <div className="img-section">
                  <img
                    src={
                      DepartmentImage ? getImage(DepartmentImage) : departmentSVG
                    }
                    alt=""
                    width={80}
                    height={80}
                  />
                </div>
                <div className="bank-content">
                  <h4>{deaprtmentName}</h4>
                  <span>{propertyName}</span>
                </div>
              </div>
            </Col>
            {userType?.displayusertype == "COMPANY_ADMIN" && userType?.isfullaccess != 0 && userType?.permission == "D" && !isexternal && hasAction(2) ? (
              <>
                <Col sm={6} className="text-end">
                  <div className="d-flex align-items-center justify-content-end mb-3 mt-5">
                    {/* <Badge
                bg={departmentStatus == "1" ? "success" : "danger"}
                className="text-white"
              >
                {departmentStatus == "1" ? "Active" : "InActive"}
              </Badge> */}
                    <div
                      className="Fry-edit"
                      onClick={() => handleDepartmentEdit(state?.id)}
                    // style={{position:"absolute", top:"8px", right:"8px"}}
                    >
                      <MdOutlineEdit className="Department-edit-icons" />
                    </div>
                  </div>
                </Col>
              </>
            ) : null}
          </Row>
        </div>

        <Row className="mt-5">
          <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3">
            {/* <Row> */}
            {/* <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3"> */}
            <ul className="nav nav-tabs nav-line-tabs fs-6">
              <li className="nav-item mb-3">
                <a
                  className="nav-link active mb-0"
                  data-bs-toggle="tab"
                  href="#kt_tab_pane_1"
                  onClick={() => refreshGrid("TicketTab")}>
                  Tickets
                </a>
              </li>
              {userType?.isfullaccess != 0 && !isexternal && (<li className="nav-item view-nav-item mb-3">
                <a
                  className="nav-link mb-0"
                  data-bs-toggle="tab"
                  href="#kt_tab_pane_2"
                  onClick={() => refreshGrid("MemberTab")}>
                  Members
                </a>
              </li>)}
              {userType?.isfullaccess != 0 && !isexternal && hasAction(4) && (
                <li className="nav-item view-nav-item mb-3">
                  <a
                    className="nav-link mb-0"
                    data-bs-toggle="tab"
                    href="#kt_tab_pane_3"
                    onClick={() => refreshGrid("LoopVendorsTab")}>
                    Loop Vendors
                  </a>
                </li>
              )}

              <li className="nav-item view-nav-item mb-3">
                <a
                  className="nav-link mb-0"
                  data-bs-toggle="tab"
                  href="#kt_tab_pane_4">
                  Calendar
                </a>
              </li>
              <li className="nav-item view-nav-item mb-3">
                <a
                  href="#kt_tab_pane_5"
                  data-bs-toggle="tab"
                  className="nav-link mb-0"
                  onClick={() => refreshGrid("InspectionsTab")}
                  >
                  Inspections
                </a>
              </li>
              <li className="nav-item view-nav-item mb-3">
                <a
                  href="#kt_tab_pane_6"
                  data-bs-toggle="tab"
                  className="nav-link mb-0">
                  QR Code
                </a>
              </li>
              <li className="nav-item view-nav-item">
                <a
                  href="#kt_tab_pane_7"
                  data-bs-toggle="tab"
                  className="nav-link mb-0">
                  Safety Forms
                </a>
              </li>
            </ul>
            {/* </Col> */}
            {/* </Row> */}
          </Col>
        </Row>
        <div className="tab-content" id="myTabContent ">
          <div
            className="tab-pane fade  active show"
            id="kt_tab_pane_1"
            role="tabpanel">
            <TicketTabDepartment
              departmentid={state?.id}
              refreshKey={refreshKey}
              activeTab={activeTab}
              deaprtmentName={deaprtmentName}
              propertyid={propertyid}
              propertyName={propertyName}
              DepartmentImage={DepartmentImage}
              userType={userType}
              setHasError={setHasError}
              hasError={hasError}
              setisGridLoading={setIsTicketLoading}
            />
          </div>
          <div className="tab-pane fad" id="kt_tab_pane_2" role="tabpanel">
            <MemberTabDepartment
              departmentid={state?.id}
              activeTab={activeTab}
              refreshKey={refreshKey}
              userType={userType}
              propertyid={propertyid}
              departmentheadId={departmentheadId}
            />
          </div>
          <div className="tab-pane fade" id="kt_tab_pane_3" role="tabpanel">
            <LoopVendorTab
              departmentid={state?.id}
              refreshKey={refreshKey}
              activeTab={activeTab}
              userType={userType}
              deaprtmentName={deaprtmentName}
            />
          </div>

          <div className="tab-pane fade" id="kt_tab_pane_4" role="tabpanel">
            <Card>
              <Card.Body>
                <div className="text-center">
                  <h2 style={{ fontSize: "40px" }}>Coming Soon....</h2>
                </div>
              </Card.Body>
            </Card>
          </div>
         <div className="tab-pane fade" id="kt_tab_pane_5" role="tabpanel">
            <Row className="mt-5">
              <div className="table_div tab-view-table">
                <Inspection
                  extraPayload={{
                    departments: [state?.id],
                  }}
                  hideManageTemplate={true}
                />
              </div>
            </Row>
          </div>
          <div className="tab-pane fade" id="kt_tab_pane_6" role="tabpanel">
            <Card>
              <Card.Body>
                <div className="text-center">
                  <h2 style={{ fontSize: "40px" }}>Coming Soon....</h2>
                </div>
              </Card.Body>
            </Card>
          </div>
          <div className="tab-pane fade" id="kt_tab_pane_7" role="tabpanel">
            <Card>
              <Card.Body>
                <div className="text-center">
                  <h2 style={{ fontSize: "40px" }}>Coming Soon....</h2>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};
