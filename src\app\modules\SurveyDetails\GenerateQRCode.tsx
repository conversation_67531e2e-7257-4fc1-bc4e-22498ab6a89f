import React, { useEffect } from "react";
import { useGetQrSurveyCodeMutation } from "../../apis/survaysAPI";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import QRScreen from "../common/QRScreen";
import { Loader } from "../../component";

const GenerateQRCode = () => {
  const [getQrSurveyCode, { isLoading: isQrLoading, data: qrData }] =
    useGetQrSurveyCodeMutation();
  console.log("qrData: ", qrData);
  const { surveyId } = useSurveyUtil();

  useEffect(() => {
    getQrSurveyCode({ surveyId });
  }, []);

  return (
    <div>
      {isQrLoading && <Loader />}
      <QRScreen qrBase64={qrData?.data?.qrcodeBase64} title="Survey QR Code" />
    </div>
  );
};

export default GenerateQRCode;
