import { useEffect, useState } from 'react';
import { FaRegQuestionCircle } from 'react-icons/fa';
import { GiCheckMark } from "react-icons/gi";
import { IoClose } from 'react-icons/io5';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import LoginSidePanal from '../../../../authentication/LoginSidePanal';
import Spinner from '../../../../common/Spinner';
import SwalMessage from '../../../../common/SwalMessage';
import { escalationService } from './escalation.helper';

const EscalationAcknowledgement = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [responseData, setResponseData] = useState<any>(null);
  const [showComponent, setShowComponent] = useState<boolean>(false);
  const navigate = useNavigate();

  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const moduleId = searchParams.get("moduleId");
  const module = searchParams.get("module");
  const userId = searchParams.get("userId");

  useEffect(() => {
    const userinfoStr = localStorage.getItem("userinfo");
    const isLogin = localStorage.getItem("islogin");

    if (userinfoStr && isLogin == "true") {
      console.log("hello")
      const userinfo = JSON.parse(userinfoStr);
      const localUserId = userinfo?.userid?.toString();
      const paramUserId = userId ? userId.toString() : "";

      if (localUserId === paramUserId) {
        if (module === "1") {
          navigate("/tickets/ticketdetail", {
            state: {
              ticketid: moduleId
            }
          });
          return;
        } else if (module === "2") {
          navigate(
            `inspections/inspections-details?id=${moduleId}`
          );
          return;
        }
      } else {
        EscalationCompleted(token);
      }
    } else {
      console.log("token", token)
      if (token) {
        EscalationCompleted(token);
      }
    }
  }, [token, userId, module]);


  const EscalationCompleted = async (id: any) => {
    setIsLoading(true);
    try {
      const response = await escalationService.escalationcompleted(id);
      if (response.status === 200) {
        setResponseData(response?.data);
        setShowComponent(true);
      }
    } catch (error: any) {
      SwalMessage("error", error.message, "OK", "error", false);
      setShowComponent(true); // show failure UI
    } finally {
      setIsLoading(false);
    }
  };

  if (!showComponent) return null;

  return (
    <>
      {isLoading && <Spinner />}
      <div className="d-flex flex-column flex-lg-row flex-column-fluid login-bg login-page-height">
        <LoginSidePanal pagelabel="" />
        <div className="d-flex flex-column flex-lg-row-fluid p-10 login-right">
          <div className="d-flex flex-center flex-column flex-lg-row-fluid">
            <Link to={""} className="need-help-text">
              <FaRegQuestionCircle className="need-help-icon" />
              Need Help?
            </Link>
            <div className="form-width p-10 mobile-padding">
              <div className="escalation-card">
                {responseData?.success ? (
                  <>
                    <span><GiCheckMark fill='#50cd89' size={28} /></span>
                    <h5 className="text-success mb-6 mt-3">Success</h5>
                    <p className="mb-4 fs-4">{responseData?.errormsg || 'You may now proceed to login.'}</p>
                    <Link to="/login" className="btn rx-btn mt-3">Go to Login</Link>
                  </>
                ) : (
                  <>
                    <span><IoClose fill='#f1416c' size={28} /></span>
                    <h5 className="text-danger mb-6 mt-3">Error</h5>
                    <p className="mb-4 fs-4">{responseData?.errormsg}</p>
                    <Link to="/login" className="btn rx-btn mt-3">Go to Login</Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EscalationAcknowledgement;
