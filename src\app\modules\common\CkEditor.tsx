import React, { forwardRef, useEffect, useLayoutEffect, useRef, Ref, ReactNode } from 'react';

import Quill from 'quill';

interface EditorProps {
  readOnly: boolean;
  defaultValue?: string; // Assuming the default value is a string (could be an object depending on how it's used)
  onTextChange?: (delta: any, oldDelta: any, source: string) => void; // Quill's event callback signature
  onSelectionChange?: (range: any, source: string) => void; // Quill's selection change callback signature
}

// Editor is an uncontrolled React component
const CkEditor = forwardRef<Quill | null, EditorProps>(
  ({ readOnly, defaultValue, onTextChange, onSelectionChange }, ref) => {
    const containerRef = useRef<HTMLDivElement | null>(null);
    const defaultValueRef = useRef<any>(defaultValue);
    const onTextChangeRef = useRef<typeof onTextChange | undefined>(onTextChange);
    const onSelectionChangeRef = useRef<typeof onSelectionChange | undefined>(onSelectionChange);

    useLayoutEffect(() => {
      onTextChangeRef.current = onTextChange;
      onSelectionChangeRef.current = onSelectionChange;
    });

    useEffect(() => {
      if (ref && typeof ref !== 'function' && ref.current) {
        ref.current.enable(!readOnly);
      }
    }, [ref, readOnly]);

    useEffect(() => {
      const container = containerRef.current;
      if (container) {
        const editorContainer = container.appendChild(
          container.ownerDocument.createElement('div'),
        );
        const quill = new Quill(editorContainer, {
          theme: 'snow',
          modules: {
            toolbar: [
              [{ header: [true,1,2,3,4,5,6] }], 
              ['bold', 'italic', 'underline'], 
              [{ color: [] }, { background: [] }], 
              [{ list: 'ordered' }, { list: 'bullet' },{ align: [],}], 
              ['link'],
            ],
    
          },
          
        });

        if (ref && typeof ref !== 'function') {
          ref.current = quill;
        }

        if (defaultValueRef.current) {
          quill.setContents(defaultValueRef.current);
        }

        quill.on(Quill.events.TEXT_CHANGE, (...args) => {
          onTextChangeRef.current?.(...args);
        });

        // quill.on(Quill.events.SELECTION_CHANGE, (...args) => {
        //   onSelectionChangeRef.current?.(...args);
        // });

        return () => {
          if (ref && typeof ref !== 'function') {
            ref.current = null;
          }
          container.innerHTML = '';
        };
      }
    }, [ref]);

    return <div ref={containerRef}></div>;
  },
);

CkEditor.displayName = 'Editor';

export default CkEditor;