import React, { useState, useEffect, useMemo } from 'react';
import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';

interface ScaleResponseProps {
  startScale: number;
  endScale: number;
  value?: number;
  onChange?: (value: number) => void;
  readOnly?: boolean;
}

const getMidPoint = (start: number, end: number) => {
  return Math.floor((start + end) / 2);
};

const ScaleResponse: React.FC<ScaleResponseProps> = ({
  startScale,
  endScale,
  value,
  onChange,
  readOnly = false,
}) => {
  // const [internalValue, setInternalValue] = useState<number>(getMidPoint(startScale, endScale));
  const [internalValue, setInternalValue] = useState<number>(value || 0);

  useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  const handleChange = (val: number | number[]) => {
    if (readOnly) return;
    const newValue = Array.isArray(val) ? val[0] : val;
    if (onChange) onChange(newValue);
    if (value === undefined) {
      setInternalValue(newValue);
    }
  };

  const marks = useMemo(() => {
    const rangeLength = endScale - startScale + 1;
    return Array(rangeLength)
      .fill(null)
      .reduce((acc, _, i) => {
        const val = startScale + i;
        acc[val] = `${val}`;
        return acc;
      }, {} as Record<number, string>);
  }, [startScale, endScale]);

  // const displayValue = value !== undefined ? value : internalValue;
  // const displayValue = value !== undefined ? value : internalValue;

  return (
    <div className="scale-response d-flex align-items-center ms-2 mb-4" style={{gap: 30}}>
      <div style={{ flex: 1 }}>
        <Slider
          min={startScale}
          max={endScale}
          marks={marks}
          step={1}
          value={internalValue}
          disabled={readOnly}
          included={false}
          onChange={handleChange}
          dotStyle={{
            // borderColor: '#007bff',
            width: 20,
            height: 20,
            top: -10,
          }}
          // activeDotStyle={{
          //   borderColor: '#007bff',
          //   backgroundColor: '#007bff',
          //   width: 15,
          //   height: 15,
          //   borderRadius: 999,
          //   marginTop: -8,
          // }}
          handleStyle={{
            borderColor: '#007bff',
            backgroundColor: '#007bff',
            width: 22,
            height: 22,
            borderRadius: '50%',
            marginTop: -10,
            boxShadow: '0 0 0 4px rgba(0,123,255,0.2)',
            zIndex: 0,
          }}
        />
      </div>
      <span
        className="badge bg-primary"
        style={{ fontSize: 16, minWidth: 40, textAlign: 'center', color: 'white' }}
      >
        {internalValue ?? '-'}/{endScale}
      </span>
    </div>
  );
};

export default ScaleResponse;
