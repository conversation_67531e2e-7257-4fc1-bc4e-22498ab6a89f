import React, { useEffect, useState } from 'react'
import { Col, Modal, Row } from 'react-bootstrap';
import { RxCross2 } from 'react-icons/rx';
import { useNavigate } from 'react-router';
import SingleSelectDropdown from '../../../common/SingleSelectDropdown';

const HotWorkTemplateFilterModal = ({
    showFilterModal,
    setshowFilterModal,
    filterData,
    setFilterData,
    setVendorFilterCount
}: any) => {
    const [status, setStatus] = useState<any>();
    const [active, setActive] = useState<any>();
    const [isTempClient, setIsTempClient] = useState<any>(1);
    const [isTempVendor, setIsTempVendor] = useState<any>(1);
    const ActiveData = [
        { label: "All", value: "" },
        { label: "Active", value: "1" },
        { label: "Inactive", value: "0" },
    ];
    const accessTypeData = [
        { label: "All", value: "" },
        { label: "Publish", value: "1" },
        { label: "Draft", value: "0" },
    ];

    // switch on Change
    const handleClientChange = () => {
        if (isTempClient === 1) {
            setIsTempClient(0);
            setIsTempVendor(1);
        } else {
            setIsTempClient(1);
        }
    };
    const handleVendorChange = () => {
        if (isTempVendor === 1) {
            setIsTempVendor(0);
            setIsTempClient(1);
        } else {
            setIsTempVendor(1);
        }
    };

    useEffect(() => {
        if (showFilterModal) {
            // Initialize temporary states when modal is opened
            console.log("filterData", ActiveData?.find((item) => item?.value === filterData?.status));
            setStatus(accessTypeData?.find((item) => item?.value === filterData?.publish))
            setActive(ActiveData?.find((item) => item?.value === filterData?.status))
            // setTempStatus(status || null);
            // setTempAccessType(accessType || null);
            // setIsTempClient(isClient);
            // setIsTempVendor(isVendor);
        }
    }, [showFilterModal]);

    const handleSetEmptyState = () => {
        setStatus(null);
        setActive(null);
    };

    const countAppliedFilters = () => {
        let count = 0;
        if (status?.value?.length > 0) count++;
        if (active?.value?.length > 0) count++;
        // if (isTempClient === 1) count++;
        // if (isTempVendor === 1) count++;
        return count;
    };

    const handleFilterData = (isReset: boolean) => {
        console.log("isReset", active, status, isReset);
        // Prepare the filter data based on user input
        const filterData = {
            status: isReset ? "" : active?.value,
            publish: isReset ? "" : status?.value,
        };
        setFilterData(filterData);
        setVendorFilterCount(isReset ? 0 : countAppliedFilters());

        if (!isReset) {
            setStatus(null);
            setActive(null);
        }
        setshowFilterModal(false);
    };
    return (
        <>
            <Modal
                className="modal-right modal-right-small p-0 filterModal"
                scrollable={true}
                show={showFilterModal}
            // onHide={() => setshowFilterModal(false)}
            >
                <Modal.Header className=" border-0 p-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Filters</h2>
                        </Col>
                        <Col xs={2} className=" text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => {
                                    setshowFilterModal(false);
                                }}
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="mt-5 p-0">
                    <Row>
                        <Col sm={12} className="mt-5">
                            <label htmlFor="" className="form-label">
                                status
                            </label>
                            <SingleSelectDropdown
                                data={accessTypeData}
                                getter={status}
                                setter={setStatus}
                                placeholder="Select Access Type"
                            />
                        </Col>
                        <Col sm={12} className="mt-5">
                            <label htmlFor="" className="form-label">
                                Active
                            </label>
                            <SingleSelectDropdown
                                data={ActiveData}
                                getter={active}
                                setter={setActive}
                                placeholder="Select Status"
                            />
                        </Col>
                        {/* {userinfo?.displayusertype === "COMPANY_ADMIN" ? (
            <>
              <Col sm={6} className="my-10">
                <div className="d-flex justify-content-start align-items-center gap-1 user-select-none">
                  <span className="fs-14px pe-2">Client</span>
                  <Switch
                    offLabel=""
                    onLabel=""
                    checked={isTempClient === 1}
                    onChange={handleClientChange}
                    className="standard-switch"
                  />
                </div>
              </Col>
              <Col sm={6} className="my-10">
                <div className="d-flex justify-content-start align-items-center gap-1 user-select-none">
                  <span className="fs-14px pe-2">Vendor</span>
                  <Switch
                    offLabel=""
                    onLabel=""
                    checked={isTempVendor === 1}
                    onChange={handleVendorChange}
                    className="standard-switch"
                  />
                </div>
              </Col>
            </>
          ) : null} */}
                    </Row>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 d-flex justify-content-end gap-5">
                    <span
                        className="btn rx-btn"
                        onClick={() => {
                            handleSetEmptyState();
                            handleFilterData(true);
                            setshowFilterModal(false);
                        }}
                    >
                        Reset
                    </span>
                    <span
                        className="btn rx-btn"
                        onClick={() => {
                            handleFilterData(false);
                        }}
                    >
                        Save & Continue
                    </span>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default HotWorkTemplateFilterModal