@import "yet-another-react-lightbox/plugins/thumbnails.css";
@import "yet-another-react-lightbox/styles.css";

.messageBoxWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
}

.messageBoxWrapper.selfBox {
  align-items: flex-end;
}

.messageBox {
  /* color: var(--message-text); */
  color: #4e4e4e;
  /* background-color: var(--bg-message); */
  background-color: #ececec;
  padding: 10px 12px;
  font-size: 12px;
  display: inline-block;
  border-radius: 0 10px 10px 10px;
  position: relative;
  max-width: 60%;
  min-width: 80px;
}

.messageBox:hover .message-actions {
  opacity: 1;
  display: block;
}

.messageBox.selfBox {
  color: var(--message-self-text);
  /* background-color: var(--bg-self-message); */
  background-color: var(--Send-msg);
  border-radius: 10px 10px 0 10px;
}

.messageBox.deletedMessage {
  background-color: #aeaeae47;
  backdrop-filter: blur(13px);
  color: var(--message-text);
}

.user-left-message {
  background-color: #aeaeae47;
  backdrop-filter: blur(13px);
  color: var(--message-text);
  padding: 5px 13px;
  border-radius: 99px;
  text-align: center;
}

.all-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.messageWithAvatar {
  display: flex;
  align-items: flex-start;
  gap: 5px;
  width: 100%;
}

.messageWithAvatar.selfBox {
  flex-direction: row-reverse;
}

.avatar {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--avatar-border);
}

.messageWithAvatar .avatar {
  margin-top: 17px;
}

.time {
  font-size: 11px;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
  text-align: end;
  color: var(--table-header);
  opacity: 0.8;
  font-weight: 600;
}

.time.selfBox {
  color: var(--table-header);
}

.time.selfBox.deletedColor {
  color: var(--message-text);
}

.edited-dot {
  background-color: #5f6368;
  display: inline-block;
  height: 4px;
  width: 4px;
  border-radius: 50%;
  margin-bottom: 3px;
  margin-left: 4px;
  margin-right: 4px;
}

.author {
  color: var(--author-name);
  font-weight: 500;
  margin: 0;
  font-size: 12px;
  line-height: 1rem;
  text-transform: capitalize;
}

.disable {
  color: rgba(0, 0, 0, 0.26) !important;
  background-color: rgba(0, 0, 0, 0.26) !important;
  cursor: default !important;
}

.disablePointer {
  pointer-events: none !important;
}

#scrollable-chat,
#scrollable-thread-chat {
  width: 100%;
  height: 100%;
}

#scrollable-chat .infinite-scroll-component,
#scrollable-thread-chat .infinite-scroll-component {
  display: flex;
  flex-direction: column-reverse;
}

#scrollable-chat .infinite-scroll-component__outerdiv {
  height: 100%;
}

#scrollable-chat .infinite-scroll-component {
  height: 100% !important;
  justify-content: flex-end;
}

.chat-loader {
  background: #ececec;
  box-shadow: 1px 3px 9px 2px #bab7b759;
  padding: 8px;
  border-radius: 99px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  z-index: 99;
}

/* fileRobot image editor css starts */
.SfxModal-Wrapper {
  z-index: 10003 !important;
}

.SfxPopper-wrapper {
  z-index: 10003 !important;
}

.image-grid {
  display: grid;
  gap: 5px;
}

.image-grid.single-image {
  grid-template-columns: 1fr;
  grid-auto-rows: 200px;
}

.image-grid.two-images {
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 200px;
}

.image-grid.three-images {
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 100px;
}

.image-grid.three-images > :nth-child(1) {
  grid-column: span 2;
  height: 100px; /* Fixed height for the first image */
}

.image-grid.two-images img,
.image-grid.three-images img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.image-grid.four-or-more {
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 100px); /* Fixed height for a uniform grid */
}

.image-grid.four-or-more > div {
  width: 100%;
  height: 100%;
}

.attachment-files {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
}

.image-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.75rem;
  z-index: 1;
}

/* fileRobot image editor css end */

.typing-spinner {
  position: relative;
  bottom: 5px;
  left: 10px;
}

/* Highlight effect for the message */
.highlight-message .messageBox {
  animation: highlight-scale 2s ease-in-out;
}

.view-thred-text {
  color: var(--message-text);
}

.FIE_tools-item-wrapper:has(.FIE_text-tool-button) {
  display: none !important;
}

.notification-loader {
  /* background: #ececec;
  box-shadow: 1px 3px 9px 2px #bab7b759; */
  padding: 8px;
  border-radius: 99px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.notification-spinner {
  color: var(--message-text) !important;
}

.text-collaped p {
  margin: 0;
}

.rx-btn.chat-save-btn {
  padding: 0px 10px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  height: 30px;
  display: flex;
  align-items: center;
}

.attachment-modal .modal-content {
  background: var(--Chat-K);
}

.custom-modal {
  color: var(--message-self-text);
  border-radius: 10px;
  text-align: center;
}

.attachment-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  /* padding-left: 1.75rem; */
  /* padding-right: 1.75rem; */
}

.attachment-icon-container {
  padding: 8px;
  width: fit-content;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #fff;
}

.attachment-icon-container + p {
  color: #fff;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.grid-item {
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  cursor: pointer;
}

.clickable-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 20px;
  border-radius: 10px;
  background: var(--attachment-tile-bg);
  transition: background 0.3s ease-in-out;
}

.clickable-box:hover {
  background: rgba(0, 0, 0, 0.3);
}

.remove-content-p .modal-content {
  padding: 0;
}

.drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s;
}

/* Show Backdrop When Drawer is Open */
.drawer-backdrop.open {
  opacity: 1;
  visibility: visible;
}

/* Bottom Drawer - Parent */
.bottom-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 99;
  width: 100%;
  background: var(--Black-2);
  color: var(--table-header);
  border-radius: 20px 20px 0 0;
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.4s ease-in-out, height 0.3s ease-in-out;
  /* transform: translateY(100%); */
  height: 0;
  overflow: hidden;
  display: flex; /* Fixes flex-shrink issue */
  flex-direction: column;
  z-index: 10000;
}

/* When Open */
.bottom-drawer.open {
  transform: translateY(0);
  height: 400px; /*Expands smooth*/
  overflow: hidden;
}

/* Drawer Handle */
.drawer-handle {
  width: 50px;
  height: 5px;
  background: #555;
  border-radius: 10px;
  margin: 10px auto;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

/* Show Handle Only When Open */
.bottom-drawer.open .drawer-handle {
  opacity: 1;
}

/* Drawer Content */
.drawer-content {
  flex-grow: 1; /* Pushes content down */
  padding: 15px;
  display: flex;
  flex-direction: column;
}

/* Dummy Box - Stretches to Bottom */
.dummy-box {
  width: 100%;
  flex-grow: 1; /* This fills available space */
  background: var(--Black-3);
  border-radius: 10px;
}

.recorder-container {
  display: flex;
  align-items: center;
  height: 90%;
  flex-direction: column;
  justify-content: flex-end;
}

/* Animation for scaling */
@keyframes highlight-scale {
  0% {
    filter: brightness(0);
    transform: scale(0.8);
  }

  50% {
    filter: brightness(0.5);
    transform: scale(1.1);
  }

  100% {
    filter: brightness(1);
    transform: scale(1);
  }
}

@keyframes refresh-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.refresh-spin {
  animation: refresh-spin 1s linear;
}

/* Fullscreen modal settings */
.fullscreen-modal .modal-dialog {
  max-width: 100vw;
  max-height: 100vh;
  margin: 0;
}

.fullscreen-modal .modal-content {
  width: 100vw;
  height: 100vh;
  border: none;
  background: rgba(0, 0, 0, 0.9);
}

/* Full height container for images and videos */
.full-height {
  height: 90vh;
}

/* Styling media to fit properly */
.media-content {
  max-width: 90%;
  max-height: 80vh;
  object-fit: contain; /* Fit large media to screen without cropping */
}

/* Audio player width */
.audio-content {
  width: 100%;
}

/* Smooth transition animation */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Thumbnails */
.thumbnail {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.thumbnail.active {
  opacity: 1;
  border: 2px solid white;
}

/* Hide navigation buttons if only one media item */
.arrow-btn {
  display: block;
}

.fullscreen-modal .modal-body:has(.media-content:only-child) .arrow-btn {
  display: none;
}

/* kendo - Tabs */
.chat-tabs .k-tabstrip-content.k-active {
  display: none;
}

.chat-tabs .k-tabstrip-items-wrapper.k-hstack{
  margin: 7px 0;
}

.chat-tabs .k-tabstrip-items {
  gap: 10px;
}

.chat-tabs .k-tabstrip-items.k-reset.k-tabstrip-items-start {
  justify-content: space-around;
}

.chat-tabs .k-item.k-tabstrip-item {
  border: 1px solid var(--table-header) !important;
  border-radius: 99px !important;
  color: var(--table-header);
}

.chat-tabs .k-item.k-tabstrip-item.k-link {
  text-align: center;
}

.chat-tabs .k-item.k-tabstrip-item.k-active {
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  margin-bottom: 0 !important;
  border-color: var(--Rx-bg) !important;
  border-bottom-color: var(--Rx-bg) !important;
}

.chat-tabs .k-item.k-tabstrip-item:hover {
  background-color: var(--Rx-btn-bg) !important;
  color: var(--Rx-bg) !important;
  transition: 0.5s all;
}


.messageBox-message-content p:last-child {
  margin-bottom: 0;
}