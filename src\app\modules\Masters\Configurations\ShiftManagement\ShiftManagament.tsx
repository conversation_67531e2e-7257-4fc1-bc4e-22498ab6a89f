import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useEffect, useState } from 'react';
import { Badge, Dropdown } from "react-bootstrap";
import { FaFileDownload, FaQrcode, FaRegEye, } from 'react-icons/fa';
import { IoMdAdd, IoMdMore } from 'react-icons/io';
import { IoFilter } from "react-icons/io5";
import { MdOutlineDeleteForever, MdOutlineEdit } from "react-icons/md";
import { Link, useNavigate } from 'react-router-dom';
import { useBreadcrumbContext } from "../../../../../_metronic/layout/components/header/BreadcrumbsContext";
import SwalMessage from "../../../common/SwalMessage";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import { shiftManagamentService } from "./shiftManagement.helper";
import Spinner from "../../../common/Spinner";
import AddShiftManagementModal from "./AddShiftManagementModal";
import SingleSelectDropdown from "../../../common/SingleSelectDropdown";


const statusData = [
    // { label: "All", value: "" },
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];

function ShiftManagement() {
    const navigate = useNavigate();
    const userinfo = JSON.parse(localStorage.getItem("userinfo") as string);
    const [search, setSearch] = useState<string>("");
    const [gridLoading, setGridLoading] = useState<boolean>(false)
    const [openAddShiftModal, setOpenAddShiftModal] = useState<boolean>(false)
    const [ShiftId, setShiftId] = useState<string>("")
    const [gridData, setGridData] = useState<any>([])
    const [editData, setEditData] = useState<any>({});
    const [status, setStatus] = useState<any>();
    const initialSort: Array<any> = [
        { field: "shiftName", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);

    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [totalCount, setTotalCount] = useState<any>();
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);
    // const pageNumber = Math.floor(page.skip / page.take) + 1;
    const itemPerPage: any = [
        {
            label: "5",
            value: 5,
        },
        {
            label: "10",
            value: 10,
        },
        {
            label: "15",
            value: 15,
        },
        {
            label: "All",
            value: totalCount,
        },
    ];
    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
    };

    //breadcrumb
    const { setLabels } = useBreadcrumbContext();
    useEffect(() => {
        setLabels([{ path: "", state: {}, breadcrumb: "Shift Management" }]);
    }, []);

    // Grid API 
    const shiftGrid = () => {
        const pageNumber = Math.floor(page.skip / page.take) + 1;
        const payload = {
            page: pageNumber,
            size: page.take,
            search: search ? search : "",
            active: status?.value,
            sortingColumns: [
                {
                    sortOrder: 0,
                    columnName: "",
                },
            ],
        }
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        shiftManagamentService
            .getGridData(payload)
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        // console.log("encResponse", encResponse)
                        setGridData(encResponse?.data);
                        setTotalCount(encResponse?.totalCount);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    };
    useEffect(() => {
        setTimeout(() => {
            shiftGrid();
        }, 500);
    }, [search, page, status]);

    // Delete Shift
    const handleDeleteShift = async (shiftId: any) => {
        const payload = {
            shiftId: shiftId
        }
        const confirm = await SwalMessage(
            "Are You Sure?",
            "Do you really want to delete this Shift? This process cannot be undone.",
            "Delete",
            "info",
            true
        );
        if (confirm) {
            setGridLoading(true);
            shiftManagamentService
                .deleteshift(payload)
                .then((response) => {
                    if (response.status == 200) {
                        if (response.data.success == true) {
                            SwalMessage(null, response.data.errormsg, "Ok", "success", false).then((isConfirm) => {
                                if (isConfirm) {
                                    shiftGrid();
                                    setGridLoading(false);
                                }
                            })
                            setGridLoading(false);
                        } else {
                            SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                            setGridLoading(false);
                        }
                    }
                })
                .catch((error) => {
                    if (error.response?.status == 401) {
                        // localStorage.removeItem("islogin");
                        navigate("/dashboard");
                        // navigate(0);
                    }
                    SwalMessage(null, error.response.data.message, "Ok", "error", false);
                    setGridLoading(false);
                })
                .finally(() => {
                    setGridLoading(false);
                });
        }
    };

    // Edit Shift
    const handleEditShift = (shiftid: any) => {
        const payload = {
            shiftId: shiftid
        }
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        shiftManagamentService
            .getshiftdataforedit(payload)
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        // console.log("encResponse", encResponse)
                        setEditData(encResponse);
                        setOpenAddShiftModal(true)
                        setShiftId(shiftid);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    }

    // render action
    const renderaction = ({ content }: any) => {
        return (
            <td className="k-table-td">

                <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                    <Dropdown.Toggle
                        as="span"
                        className="fs-1 cursor-pointer ms-2"
                    >
                        <IoMdMore className="td-icon cursor-pointer" />
                    </Dropdown.Toggle>
                    <Dropdown.Menu align="end">
                        <Dropdown.Item onClick={() => handleEditShift(content)}>
                            <span className="fs-5">
                                <MdOutlineEdit className="me-4" size={18} />
                                Edit
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => handleDeleteShift(content)}>
                            <span className="fs-5">
                                <MdOutlineDeleteForever className="me-4" size={18} />
                                Delete
                            </span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </td>
        );
    };
    // end action

    // render switch
    const renderswitch = (props: any) => {
        const { dataItem } = props;
        return (
            <td className="text-center">
                <Badge
                    bg={dataItem.active == 1 ? "success" : "danger"}
                    className="text-white"
                >
                    {dataItem.active == 1 ? "Active" : "Inactive"}
                </Badge>
            </td>
        );
    };

    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        return (
            <td className='k-table-td'>
                <span className='ellipsis-cell' title={content}>
                    {dataItem[field]}
                </span>
            </td>
        );
    };
    // end tooltip



    return (
        <>
            {gridLoading && <Spinner />}
            <div className='row pageheader mb-7'>
                <div className="col-xl-3 col-lg-3 col-sm-3 col-12 mobile-margin">
                    <input
                        type="text"
                        className="form-control"
                        placeholder="Search..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                </div>
                <div className="col-xl-2 col-lg-2 col-sm-2 col-12 mobile-margin">
                    <SingleSelectDropdown
                        data={statusData}
                        getter={status}
                        setter={setStatus}
                        placeholder="Select Status"
                    />
                </div>
                <div className=" col-xl-7 col-lg-7 col-sm-7 col-12 text-end mobile-margin ">
                    {userinfo?.isfullaccess == 1 && (
                        <span className="btn rx-btn" onClick={() => setOpenAddShiftModal(true)}>
                            <IoMdAdd className="btn-icon-custom" />
                            Add Shift{" "}
                        </span>
                    )}
                </div>
            </div >
            <div className='card mt-0'>
                <div className='card-body p-0'>
                    <div className='table_div' style={{ width: '100%' }}>
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid
                                data={orderBy(gridData, sort)}
                                skip={page.skip}
                                take={page.take}
                                total={totalCount}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: itemPerPage.map((item: any) => item.label),
                                    pageSizeValue: pageSizeValue,
                                }}

                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column
                                    title='Shift Name'
                                    field='shiftName'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.shiftName })}
                                />
                                {/* <Column
                                    title='Day Of Week'
                                    field='dayOfWeek'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.dayOfWeek })}
                                /> */}
                                <Column
                                    title='Start Time'
                                    field='startTime'
                                    width={200}
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.startTime })}
                                />
                                <Column
                                    title='End Time'
                                    field='endTime'
                                    width={200}
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.endTime })}
                                />
                                <Column
                                    title='Repeat Pattern'
                                    field='repeatPattern'
                                    width={250}
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.repeatPattern })}
                                />
                                <Column
                                    title='Status'
                                    field='active'
                                    width={150}
                                    headerClassName="center-header"
                                    cell={(props) => renderswitch({ ...props, content: props.dataItem.active })}
                                />
                                {userinfo?.isfullaccess == 1 && (
                                    <Column
                                        title='Action'
                                        width={90}
                                        cell={(props) => renderaction({ ...props, content: props.dataItem.shiftId })}
                                    />
                                )}
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>

            <AddShiftManagementModal
                openAddShiftModal={openAddShiftModal}
                setOpenAddShiftModal={setOpenAddShiftModal}
                shiftGrid={shiftGrid}
                setGridLoading={setGridLoading}
                setShiftId={setShiftId}
                shiftId={ShiftId}
                setEditData={setEditData}
                editData={editData}
            />
        </>
    )
}

export default ShiftManagement;