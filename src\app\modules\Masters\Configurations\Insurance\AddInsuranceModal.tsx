import { useEffect, useState } from "react";
import { Modal, Row, Col, Form, Card } from "react-bootstrap";
import { SlClose, SlCloudUpload, SlEye, SlTrash } from "react-icons/sl";
import { useNavigate } from "react-router-dom";
import { RxCross2 } from "react-icons/rx";
import SwalMessage from "../../../common/SwalMessage";
import { insuranceService } from "./insurance.helper";
import { AutoCapitalize, formatContactInputNumber } from "../../../../utils/CommonUtils";

function AddInsuranceModal({
    openAddInsuranceModal,
    setOpenAddInsuranceModal,
    setGridLoading,
    insuranceGrid,
    setInsuranceId,
    insuranceId,
    setEditData,
    editData
}: any) {
    const navigate = useNavigate();
    const isCapitalize = localStorage.getItem("isCapitalize") === "true";
    const [insuraceName, setInsuranceName] = useState<string>("");
    const [companyemail, setcompanyemail] = useState<string>("")
    const [companymobile, setcompanymobile] = useState<any>("");
    const [contactfirstname, setcontactfirstname] = useState<string>("");
    const [contactlastname, setcontactlastname] = useState<string>("");
    const [contactemail, setcontactemail] = useState<string>("");
    const [contactphone, setcontactphone] = useState<any>("");
    const [description, setDescription] = useState<string>("");
    const [isActive, setIsActive] = useState<boolean>(false)
    const [errors, setErrors] = useState<any>({});

    // email validation 
    const emailRegex = /^[a-zA-Z0-9._%+-]{1,64}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    const validEmail = (value: string) =>
        value.replace(/[^a-zA-Z0-9@._+-]/g, ""); // removes invalid characters

    const handleEmailChange = (e: any) => {
        const emailValue = validEmail(e.target.value);
        setcompanyemail(emailValue?.toLowerCase());
    };

    const handleContactmail = (e: any) => {
        const emailValue = validEmail(e.target.value);
        setcontactemail(emailValue?.toLowerCase());
    }

    const validatePayload = () => {
        const newErrors: any = {};

        if (!insuraceName || !insuraceName.trim()) {
            newErrors.insuraceName = "Please Enter Insurace Company Name.";
        }

        if (!companyemail || !companyemail.trim()) {
            newErrors.companyemail = "Please Enter Email.";
        } else if (!emailRegex.test(companyemail)) {
            newErrors.companyemail = "Please Enter Valid Email.";
        }

        if (!companymobile || !companymobile.trim()) {
            newErrors.companymobile = "Please Enter Contect Number."
        }

        if (!contactfirstname || !contactfirstname.trim()) {
            newErrors.contactfirstname = "Please Enter First Name.";
        }

        if (!contactlastname || !contactlastname.trim()) {
            newErrors.contactlastname = "Please Enter Last Name.";
        }

        if (!contactemail || !contactemail.trim()) {
            newErrors.contactemail = "Please Enter Email.";
        } else if (!emailRegex.test(contactemail)) {
            newErrors.contactemail = "Please Enter Valid Email.";
        }

        if (!contactphone || !contactphone.trim()) {
            newErrors.contactphone = "Please Enter Contact number.";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // set Edit data
    useEffect(() => {
        if (editData && openAddInsuranceModal) {
            setInsuranceName(editData?.insuranceName);
            setcompanyemail(editData?.insuranceCompanyEmail);
            setcompanymobile(formatContactInputNumber(editData?.insuranceCompanyPhoneNumber));
            setcontactfirstname(editData?.contactPersonFirstName);
            setcontactlastname(editData?.contactPersonLastName);
            setcontactemail(editData?.contactPersonEmail);
            setcontactphone(formatContactInputNumber(editData?.contactPersonPhoneNumber));
            setDescription(editData?.description);
            setIsActive(editData?.active == 1 ? true : false)
        }
    }, [editData, openAddInsuranceModal])

    // Handle Submit
    const handleSubmit = () => {
        if (!validatePayload()) {
            return
        }
        setGridLoading(true);
        const payload = {
            insuranceId: insuranceId ? insuranceId : "",
            insuranceName: insuraceName,
            insuranceCompanyEmail: companyemail,
            insuranceCompanyPhoneNumber: companymobile ? companymobile.replace(/\D/g, "") : "",
            contactPersonFirstName: contactfirstname,
            contactPersonLastName: contactlastname,
            contactPersonEmail: contactemail,
            contactPersonPhoneNumber: contactphone ? contactphone.replace(/\D/g, "") : "",
            description: description ? description : "",
            companyIds: [],
            isAllSelectCompany: 0,
            active: isActive == true ? 1 : 0,
        }
        // console.log("savePayload", payload)
        insuranceService
            .createInsurance(payload)
            .then(async (response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        setGridLoading(false);
                        const confirmed = await SwalMessage(
                            null,
                            response.data.errormsg,
                            "Ok",
                            "success",
                            false
                        );
                        if (confirmed) {
                            setOpenAddInsuranceModal(false);
                            setGridLoading(false);
                            insuranceGrid()
                        }
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error: any) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error.response.data.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    }

    // empty state
    useEffect(() => {
        if (!openAddInsuranceModal) {
            setInsuranceName("");
            setcompanyemail("");
            setcompanymobile("");
            setcontactfirstname("");
            setcontactlastname("");
            setcontactemail("");
            setcontactphone("");
            setDescription("");
            setErrors({});
            setEditData({});
            setInsuranceId("");
            setIsActive(false)
        }
    }, [openAddInsuranceModal])

    return (
        <>
            <Modal
                scrollable={true}
                show={openAddInsuranceModal}
                onHide={() => setOpenAddInsuranceModal(false)}
                className="modal-right p-0"
            >
                <Modal.Header className="border-0 p-0">
                    <Row>
                        <Col sm={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">{insuranceId ? "Edit" : "Add"} Insurance Company</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setOpenAddInsuranceModal(false)}>
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="mt-5 p-0">
                    <Row>
                        <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
                            <span className="modal-sub-title">Company Information</span>
                        </Col>

                        <Col xxl={12} xl={12} lg={12} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Insurance Company Name <span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                autoFocus
                                className="form-control"
                                placeholder="Enter Insurance Company Name"
                                value={insuraceName}
                                onChange={(e: any) => {
                                    const value = e.target.value;
                                    if (isCapitalize) {
                                        const isValid = /^[^<>"']*$/.test(value);
                                        if (isValid) {
                                            setInsuranceName(AutoCapitalize(value))
                                        }
                                    } else {
                                        setInsuranceName(value);
                                    }
                                }}
                                onBlur={(e) => {
                                    const value = e.target.value;
                                    if (isCapitalize) {
                                        const isValid = /^[^<>"']*$/.test(value);
                                        if (isValid) {
                                            setInsuranceName(AutoCapitalize(value, true));
                                        }
                                    }
                                }}
                            />
                            {errors.insuraceName && (
                                <div className="text-danger">{errors.insuraceName}</div>
                            )}
                        </Col>
                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Company Email <span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                placeholder="Enter Company Email"
                                className="form-control"
                                value={companyemail}
                                onChange={handleEmailChange}
                            />
                            {errors.companyemail && (
                                <div className="text-danger">{errors.companyemail}</div>
                            )}
                        </Col>
                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Company Phone Number <span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                placeholder="Enter Company contact number"
                                className="form-control"
                                value={companymobile}
                                onChange={(e) => setcompanymobile(formatContactInputNumber(e.target.value))}
                            />
                            {errors.companymobile && (
                                <div className="text-danger">{errors.companymobile}</div>
                            )}
                        </Col>

                        <Col xxl={12} xl={12} lg={12} sm={12} className="my-5">
                            <span className="modal-sub-title">Contact Person Information</span>
                        </Col>

                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                First Name<span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter First Name"
                                value={contactfirstname}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (isCapitalize) {
                                        if (/^[^<>"']*$/.test(value)) {
                                            setcontactfirstname(AutoCapitalize(value));
                                        }
                                    } else {
                                        setcontactfirstname(value);
                                    }
                                }}
                                onBlur={(e) => {
                                    const value = e.target.value;
                                    if (isCapitalize && /^[^<>"']*$/.test(value)) {
                                        setcontactfirstname(AutoCapitalize(value, true));
                                    }
                                }}
                                maxLength={24}
                            />
                            {errors.contactfirstname && (
                                <div className="text-danger">{errors.contactfirstname}</div>
                            )}
                        </Col>

                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Last Name<span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter Last Name"
                                value={contactlastname}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (isCapitalize) {
                                        if (/^[^<>"']*$/.test(value)) {
                                            setcontactlastname(AutoCapitalize(value));
                                        }
                                    } else {
                                        setcontactlastname(value);
                                    }
                                }}
                                onBlur={(e) => {
                                    const value = e.target.value;
                                    if (isCapitalize && /^[^<>"']*$/.test(value)) {
                                        setcontactlastname(AutoCapitalize(value, true));
                                    }
                                }}
                                maxLength={24}
                            />
                            {errors.contactlastname && (
                                <div className="text-danger">{errors.contactlastname}</div>
                            )}
                        </Col>


                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Contact Email<span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter Contact Email"
                                value={contactemail}
                                onChange={handleContactmail}
                                maxLength={128}
                            />
                            {errors.contactemail && (
                                <div className="text-danger">{errors.contactemail}</div>
                            )}
                        </Col>

                        <Col xxl={6} xl={6} lg={6} sm={12} className="mb-3 mobile-margin">
                            <span className="form-label ">
                                Contact Phone Number<span className="text-danger">*</span>
                            </span>
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Enter Contact number"
                                value={contactphone}
                                onChange={(e) =>
                                    setcontactphone(formatContactInputNumber(e.target.value))
                                }
                                maxLength={14}
                            />
                            {errors.contactphone && (
                                <div className="text-danger">{errors.contactphone}</div>
                            )}
                        </Col>

                        <Col sm={12} className="mt-5 mobile-margin">
                            <span className="form-label ">Description</span>
                            <textarea
                                className="form-control  "
                                placeholder="Enter Description"
                                maxLength={1024}
                                onChange={(e: any) => {
                                    const value = e.target.value
                                    // setDescription(AutoCapitalize(value))
                                    setDescription(value)
                                }}
                                // onBlur={(e: any) => {
                                //   const value = e.target.value
                                //   setDescription(AutoCapitalize(value, true))
                                // }}
                                value={description}
                                autoComplete="off"
                            />
                        </Col>
                    </Row>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 d-block">
                    <Row className="align-items-center">
                        <Col sm={3}>
                            {insuranceId && (
                                <>
                                    <input
                                        type="checkbox"
                                        className="form-check-input me-3 mt-1"
                                        name="isdefault"
                                        checked={isActive}
                                        onChange={(e) => setIsActive(e.target.checked)}
                                    />
                                    <span className="fs-5" >
                                        Active
                                    </span>
                                </>
                            )}
                        </Col>
                        <Col sm={9} className="text-end">
                            <span className="btn rx-btn" onClick={handleSubmit}>
                                Save
                            </span>
                        </Col>
                    </Row>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default AddInsuranceModal;
