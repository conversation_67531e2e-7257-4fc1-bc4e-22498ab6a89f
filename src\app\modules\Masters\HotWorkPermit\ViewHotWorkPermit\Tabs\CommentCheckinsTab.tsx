import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import {
  MdOutlineDeleteForever,
  MdOutlineInfo,
  MdOutlineRemoveRedEye,
} from "react-icons/md";
import { Card, Col, OverlayTrigger, Row, Tooltip } from "react-bootstrap";
import { extensionPacks, getImage } from "../../../../../utils/CommonUtils";
import { GrEmoji } from "react-icons/gr";
import { AudioRecorder } from "react-audio-voice-recorder";
import { FaImage, FaPlus, FaVideo } from "react-icons/fa";
import Picker from "@emoji-mart/react";
import SwalMessage from "../../../../common/SwalMessage";
import { AiFillAudio } from "react-icons/ai";
import data from "@emoji-mart/data";
import userimage from "../../../../../../efive_assets/images/user.jpg";
import Swal from "sweetalert2";

interface FileDetails {
  selectedFiles: File[];
}

const commentData = [
  {
    id: "1",
    name: "<PERSON>",
    time: "March 06 2024, 02:48 PM",
    description:
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum is simply dummy text of the printing and typesetting industry..",
    image: userimage,
  },
];

export const CommentCheckinsTab = () => {
  const fileTooltip = useRef<HTMLDivElement>(null);
  const [Comments, setComments] = useState<string>("");
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  const [isCheckboxVisible, setIsCheckboxVisible] = useState(false);
  const [isCheckboxChecked, setIsCheckboxChecked] = useState(false);

  // ********** Files Section ***************
  const [previewimagemodal, setpreviewimagemodal] = useState(false);
  const [viewImage, setViewImage] = useState<any>();
  const [videoUrl, setVideoUrl] = useState<any>();
  const [previevideomodal, setprevievideomodal] = useState(false);
  const [audioUrl, setAudioUrl] = useState<any>();
  const [previeaudiomodal, setprevieaudiomodal] = useState(false);
  const [mapmodal, setmapmodal] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<any>();
  const [previewfilemodal, setpreviewfilemodal] = useState(false);

  const [fileDetails, setFileDetails] = useState<FileDetails>({
    selectedFiles: [],
  });

  const pickerRef = useRef(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      pickerRef.current &&
      !(pickerRef.current as Node).contains(event.target as Node)
    ) {
      setIsPickerOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // audio recorder
  const addAudioElement = (blob: Blob) => {
    const file = new File([blob], `recording-${Date.now()}.mp3`, {
      type: "audio/mp3",
    });
    setFileDetails((prevDetails) => ({
      selectedFiles: [...prevDetails.selectedFiles, file],
    }));
  };

  const onFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files);
      const previousFiles = fileDetails.selectedFiles || [];
      const validFiles = newFiles
        ?.map((file: any) => {
          if (extensionPacks.documentExtensions.includes(file.type)) {
            return file;
          } else if (extensionPacks.imageExtensions.includes(file.type)) {
            return file;
          } else if (extensionPacks.videoExtensions.includes(file.type)) {
            return file;
          } else if (file.type === "audio/mp3") {
            return file;
          } else {
            SwalMessage(
              null,
              "Please choose files with the following extensions: .jpeg, .jpg, .png, .pdf, audio/mp3, video/mp4",
              "Ok",
              "error",
              false
            );
          }
        })
        .filter(Boolean);

      const updatedFiles = [...previousFiles, ...validFiles];
      setFileDetails({
        selectedFiles: updatedFiles,
      });
      event.target.value = "";
    }
  };

  // Start Handle Comment Post APi
  const handlePost = () => {};

  const removeFile = (index: number) => {
    setFileDetails((prevDetails) => {
      const updatedFiles = [...prevDetails.selectedFiles];
      updatedFiles.splice(index, 1);
      return { selectedFiles: updatedFiles };
    });
  };
  const getFileTypeIcon = (fileType: string) => {
    if (fileType?.startsWith("image/"))
      return <FaImage className="fs-2 me-2 mb-2" />;
    if (fileType?.startsWith("video/"))
      return <FaVideo className="fs-2 me-2 mb-2" />;
    if (fileType?.startsWith("audio/"))
      return <AiFillAudio className="fs-2 me-2 mb-2" />;
    if (fileType?.startsWith("appication/"))
      return <AiFillAudio className="fs-2 me-2 mb-2" />;
    else return null;
  };

  const truncateFileName = (fileName: string, maxLength: number) => {
    // Find the file extension and its position
    const extensionMatch = fileName.match(/\.[^\.]+$/);
    const extension = extensionMatch ? extensionMatch[0] : "";
    const nameWithoutExtension = fileName.slice(0, -extension.length);

    // Check if the length of the name (without the extension) exceeds the maxLength
    if (nameWithoutExtension.length > maxLength) {
      return `${nameWithoutExtension.slice(0, maxLength)}... ${extension}`;
    }

    return fileName;
  };
  const handleOpenMediaModal = (FileType: any, file: any) => {
    if (FileType?.startsWith("image")) {
      setViewImage(URL.createObjectURL(file));
      setpreviewimagemodal(true);
    } else if (FileType?.startsWith("video")) {
      setVideoUrl(URL.createObjectURL(file));
      setprevievideomodal(true);
    } else if (FileType?.startsWith("audio")) {
      setAudioUrl(URL.createObjectURL(file));
      setprevieaudiomodal(true);
    } else if (FileType?.startsWith("application")) {
      setPdfUrl(URL.createObjectURL(file));
      setpreviewfilemodal(true);
      // window.open(fileURL, "_blank");
    }
  };

  const renderFileName = (props: any, content: any) => (
    <Tooltip id="tooltiptyfy" {...props}>
      <div className="custom-card">
        <span className="w-100 fs-10px">{content}</span>
      </div>
    </Tooltip>
  );
  // End file handle

  // important comment.
  const handleImportantComment = (key: any) => {
    Swal.fire({
      title: "Are you want to mark as an important comment?",
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonText: "Yes",
      reverseButtons: true,
      customClass: {
        popup: "custom-popup custom-card checkins-popup",
        confirmButton: "custom-confirm-button btn rx-btn",
        closeButton: "custom-close-button",
        cancelButton: "custom-cancel-button btn rx-btn btn-black-lightblue",
      },
      showCloseButton: true,
      buttonsStyling: true,
    }).then((result) => {
      if (result.isConfirmed) {
        // Show the checkbox and check it
        setIsCheckboxVisible(true);
        setIsCheckboxChecked(true);

        // Trigger the second Swal after 1 second
        setTimeout(() => {
          Swal.fire({
            title: "Expired Permit!",
            text: "Add final observations or issues related to the work after the permit has expired. This helps document any follow-up actions.",
            showCancelButton: true,
            cancelButtonText: "No",
            confirmButtonText: "Yes",
            reverseButtons: true,
            customClass: {
              popup: "custom-popup custom-card checkins-popup",
              confirmButton: "custom-confirm-button btn rx-btn",
              closeButton: "custom-close-button",
              cancelButton:
                "custom-cancel-button btn rx-btn btn-black-lightblue",
              title: "cutom-title-checkins-popup",
            },
            showCloseButton: true,
            buttonsStyling: true,
          }).then((result) => {
            if (result.isConfirmed) {
              // Uncheck the checkbox and hide it after confirming the second Swal
              setIsCheckboxChecked(false);
              setIsCheckboxVisible(false);
            }
          });
        }, 1000);
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        setIsCheckboxVisible(false);
        setIsCheckboxChecked(false);
      }
    });
  };

  const stopPropagation = (e: any) => {
    e.stopPropagation();
  };

  const handleCheckboxChange = () => {
    setIsCheckboxChecked((prev) => !prev); 
  };

  return (
    <>
      <div className="d-flex flex-end">
        <div className="notification">
          <span className="text-muted cursor-pointer">
            <MdOutlineInfo size={15} />
          </span>
          <span className="tooltip-text">
            Add final observations or issues related to the work after the
            permit has expired. This helps document any follow-up actions.
          </span>
        </div>
      </div>

      <Row className="position-relative ">
{/* <Col
          xl={8}
          lg={12}
          md={12}
          sm={12}
          className="ticket-detail-left mb-lg-0 mb-5"
        >
          <div className="d-flex align-items-center gap-2">
            <h1 className="mb-0">Comments</h1>
            <div>
              {connectionStatus === "failed" ? (
                // {true ? (
                <FiRefreshCcw
                  className="fs-1 cursor-pointer"
                  onClick={(e) => reConnect(e)}
                />
              ) : (
                <FaWifi
                  className="fs-1"
                  color={getStatusColor(connectionStatus)}
                />
              )}
            </div>
          </div>
          <div className="custom-card-container ticketPageChat">
            {activeRoom && (
              <Chats
                activeRoom={activeRoom}
                chatHistory={ticketPageChatHistory}
                threadInitiatMessage={
                  threadInitiatMessage as ChatThreadHistoryItem
                }
                onThreadClose={() => setOpenThread(false)}
                handleThread={(message) => {
                  dispatch(cleanChatThreadHistory());
                  setOpenThread(true);
                }}
                openThread={openChatThred}
                chatThreadHistory={chatThreadHistory}
                hideMainLoader={true}
                showFilesOptionTicket={showFilesOptionTicket}
                showRecorderTicket={showRecorderTicket}
                toggleFileOption={toggleFileOption}
                handleAttachmentClose={handleAttachmentClose}
                handleToggleRecorder={handleToggleRecorder}
                toggleEmojiPicker={toggleEmojiPicker}
                showEmojiPicker={showEmojiPicker}
              />
            )}
            {isTicketLoading && (
              <div className="h-100 d-flex align-items-center">
                <div className="loader-overlay" style={{ borderRadius: 10 }}>
                  <ClipLoader size={60} color="#ffffff" />
                </div>
              </div>
            )}
          </div>
        </Col> */}



        <Col sm={12} className="comment-section-checkins">
          {commentData.map((item, key) => (
            <div
              key={key}
              className="custom-card mt-3 py-4 px-5 rounded-5 m-0 comment-card cursor-pointer"
              onClick={() => handleImportantComment(key)}
            >
              <div className="d-flex align-items-center justify-content-between ">
                <div className="d-flex align-items-center justify-content-between gap-3 ">
                  <img
                    src={item.image}
                    alt=""
                    className="rounded-circle"
                    height={"40px"}
                    width={"40px"}
                  />
                  <div>
                    <div>{item.name}</div>
                    <div className="opacity-25 fs-12px">{item.time}</div>
                  </div>
                </div>
                <div
                  className="d-flex align-items-center gap-1 cursor-pointer"
                  onClick={stopPropagation}
                >
                  <MdOutlineRemoveRedEye />
                  <span>10</span>
                </div>
              </div>
              <div className="description-checkins-section d-flex align-items-center justify-content-between">
                <span className="w-100 fs-12px mt-4">{item.description}</span>
                {isCheckboxVisible && (
                  <span className="mx-3" onClick={stopPropagation}>
                    <input
                      type="checkbox"
                      checked={isCheckboxChecked}
                      onChange={handleCheckboxChange} 
                    />
                  </span>
                )}
              </div>
            </div>
          ))}
        </Col>
        <Row className="ticket-detail-chat  textarea-form-control w-100 custom-card">
          <Col sm={12} className="comment-files">
            {fileDetails?.selectedFiles.length > 0 && (
              <Row className=" mt-3" ref={fileTooltip}>
                {fileDetails.selectedFiles.map((file, index) => (
                  <Col
                    className="d-flex align-items-center justify-content-between "
                    key={index}
                    sm={6}
                  >
                    <div className="">
                      {getFileTypeIcon(file?.type)}
                      <OverlayTrigger
                        placement="top"
                        overlay={(props: any) =>
                          renderFileName(props, file.name)
                        }
                        container={fileTooltip.current}
                      >
                        <span
                          onClick={() => handleOpenMediaModal(file.type, file)}
                          className="cursor-pointer fs-12px"
                        >
                          {file?.name && <>{truncateFileName(file.name, 30)}</>}
                        </span>
                      </OverlayTrigger>
                      {/* <span className="fs-12px">
                          {file?.name && <>{truncateFileName(file.name, 30)}</>}
                        </span> */}
                    </div>
                    <div className="delete-border">
                      <span
                        className="delete-button"
                        onClick={() => removeFile(index)}
                      >
                        <MdOutlineDeleteForever
                          className="cursor-pointer"
                          color="red"
                          fontSize={16}
                        />
                      </span>
                    </div>
                  </Col>
                ))}
              </Row>
            )}
          </Col>
          
          <Col sm={12} className="p-0">
            <textarea
              placeholder="Write a comment"
              className="comment-textarea"
              cols={30}
              value={Comments}
              onChange={(e: any) => {
                setComments(e.target.value);
              }}
            />
          </Col>
          <Col sm={12}>
            <div className="d-flex align-items-center justify-content-end user-select-none">
              <span className="vector-img me-2">
                <input
                  type="file"
                  className="d-none"
                  id="commentFile"
                  accept=".jpeg, .jpg, .png, .pdf, .mp4, .mp3"
                  multiple
                  onChange={onFileChange}
                  style={{ display: "none" }}
                />
                <FaPlus
                  className="vector-img-icon"
                  onClick={() =>
                    document.getElementById("commentFile")?.click()
                  }
                />
              </span>
              <span className="vector-img me-2">
                <GrEmoji
                  className="vector-img-icon"
                  onClick={() => setIsPickerOpen(!isPickerOpen)}
                />
              </span>

              <AudioRecorder
                onRecordingComplete={addAudioElement}
                audioTrackConstraints={{
                  noiseSuppression: true,
                  echoCancellation: true,
                }}
                onNotAllowedOrFound={(err: any) => console.table(err)}
                downloadOnSavePress={false}
                downloadFileExtension="mp3"
                mediaRecorderOptions={{
                  audioBitsPerSecond: 128000,
                }}
                showVisualizer={true}
              />

              <span
                className={`btn rx-btn  post-btn ${
                  (Comments.length === 0 || Comments.length > 1024) &&
                  "disabled"
                } `}
                onClick={() => handlePost()}
              >
                Post
              </span>
            </div>
            {/* </Row> */}
          </Col>
        </Row>
        <div
          ref={pickerRef}
          className={`emoji-picker-container ${
            isPickerOpen ? "open-emoji" : "close-emoji"
          }`}
        >
          <Picker
            data={data}
            onEmojiSelect={(data: any) => {
              setComments((prevComments: string) =>
                prevComments.concat(data?.native)
              );
            }}
            previewPosition={"none"}
          />
        </div>
      </Row>
    </>
  );
};