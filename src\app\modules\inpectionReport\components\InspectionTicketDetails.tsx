import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Modal, Row } from "react-bootstrap";
import { RxCross2 } from "react-icons/rx";
import { BsTicket } from "react-icons/bs";
import { useNavigate } from "react-router-dom";
import { useGetTicketListMutation } from "../../../apis/ticketListAPI";
import { TicketItem } from "../../../apis/type";
import { DataStatusWrapper, RxInfiniteScroll } from "../../../component";
import { getImage } from "../../../utils/CommonUtils";
import usersvg from "../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { ClipLoader } from "react-spinners";

interface Props {
  open: boolean;
  onClose: () => void;
  inspectionId: string;
}

const InspectionTicketDetails: React.FC<Props> = ({
  onClose,
  open,
  inspectionId,
}) => {
  const [tickets, setTickets] = useState<TicketItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1); // Start from page 0 for infinite scroll
  const [hasMore, setHasMore] = useState(true);
  const [initialLoading, setInitialLoading] = useState(true); // Track initial loading state
  const [loadingMore, setLoadingMore] = useState(false); // Track loading more state
  const navigate = useNavigate();

  // RTK Query hook for fetching tickets
  const [getTicketList] = useGetTicketListMutation();

  // Fetch tickets when the modal opens and inspectionId changes
  useEffect(() => {
    if (open && inspectionId) {
      // Reset state when modal opens or inspectionId changes
      setTickets([]);
      setPage(1);
      setHasMore(true);
      setInitialLoading(true); // Set initial loading to true
      fetchTickets(1);
    }
  }, [open, inspectionId]);

  // Function to fetch tickets related to the inspection
  const fetchTickets = async (currentPage: number) => {
    try {
      // Reset error state before making the request
      setError(null);

      // Set appropriate loading state
      if (currentPage === 0) {
        setInitialLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await getTicketList({
        page: currentPage,
        size: 20, // Fetch items per page for better performance
        search: "",
        inspectionId: inspectionId, // Pass the inspectionId to filter tickets
      }).unwrap();

      if (response.success) {
        if (currentPage === 1) {
          // First page - replace existing data
          setTickets(response.data.data);
        } else {
          // Subsequent pages - append to existing data
          setTickets((prevTickets) => [...prevTickets, ...response.data.data]);
        }

        setTotalCount(response.data.totalCount);

        // Check if we have more data to load
        setHasMore(
          response.data.data.length > 0 &&
            tickets.length + response.data.data.length <
              response.data.totalCount
        );
      } else {
        // Set error message from API response
        setError(response.errormsg || "Failed to fetch tickets");
        console.error("Error fetching tickets:", response.errormsg);
      }
    } catch (error: any) {
      // Handle different types of errors
      const errorMessage =
        error?.data?.errormsg ||
        error?.message ||
        "An unexpected error occurred while fetching tickets";
      setError(errorMessage);
      console.error("Error fetching tickets:", error);
    } finally {
      // Reset loading states
      setInitialLoading(false);
      setLoadingMore(false);
    }
  };

  // Function to load more data when scrolling
  const loadMoreTickets = () => {
    if (!loadingMore) { // Prevent multiple simultaneous requests
      const nextPage = page + 1;
      setPage(nextPage);
      fetchTickets(nextPage);
    }
  };

  // Function to get badge color based on ticket priority
  const getPriorityBadgeColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "high":
        return "danger";
      case "medium":
        return "warning";
      case "low":
        return "success";
      default:
        return "secondary";
    }
  };

  // Function to get badge color based on ticket status
  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return "primary";
      case "in progress":
        return "info";
      case "resolved":
        return "success";
      case "closed":
        return "secondary";
      default:
        return "secondary";
    }
  };

  return (
    <Modal
      className="modal-right modal-right-small"
      scrollable={true}
      show={open}
      onHide={() => {
        onClose();
      }}
    >
      <Modal.Header className="border-0">
        <Row className="align-items-baseline">
          <Col xs={10} className="mt-auto mb-auto">
            <h2 className="mb-0">Inspection Tickets</h2>
          </Col>
          <Col xs={2} className="text-end mb-3">
            <span
              className="close-btn cursor-pointer"
              onClick={() => {
                onClose();
              }}
            >
              <RxCross2 fontSize={20} />
            </span>
          </Col>
        </Row>
      </Modal.Header>
      <Modal.Body
        className="p-2"
        id="scrollableDiv"
        style={{ overflow: "auto" }}
      >
        <DataStatusWrapper
          isLoading={initialLoading}
          renderNodata={!initialLoading && !error && tickets.length === 0}
          error={error}
          message="No tickets found for this inspection."
        >
          <div className="mb-3">
            <small className="text-muted">Total tickets: {totalCount}</small>
          </div>
          <RxInfiniteScroll
            dataLength={tickets.length}
            next={loadMoreTickets}
            hasMore={hasMore}
            loader={
              hasMore ? (
                <div className="d-flex justify-content-center w-100 py-3">
                  <div className="notification-loader">
                    <ClipLoader
                      size={30}
                      className="spinner"
                      color="var(--message-text)"
                    />
                  </div>
                </div>
              ) : (
                <></>
              )
            }
            scrollableTarget="scrollableDiv"
            endMessage={
              <div className="text-center py-2">
                <small className="text-muted">No more tickets to load</small>
              </div>
            }
          >
            {tickets.map((ticket) => (
              <Card
                key={ticket.ticketid}
                className="mb-4 custom-card cursor-pointer"
                onClick={() =>
                  navigate("/tickets/ticketdetail", {
                    state: {
                      ticketid: ticket.ticketid,
                    },
                  })
                }
              >
                <Card.Body className="p-3 ">
                  <div
                    className="d-flex align-items-center justify-content-between gap-2 mb-1"
                    style={{ cursor: "pointer" }}
                  >
                    <div className="d-flex align-items-center">
                      <BsTicket className="text-primary me-2" size={18} />
                      <h6 className="mb-0">{ticket.ticketnumber}</h6>
                    </div>

                    <div className="d-flex align-items-center justify-content-end gap-2">
                      <Badge bg={getPriorityBadgeColor(ticket.priority)}>
                        {ticket.priority}
                      </Badge>
                      <Badge bg={getStatusBadgeColor(ticket.currentstatus)}>
                        {ticket.currentstatus}
                      </Badge>
                    </div>
                  </div>
                  <Tooltip position="bottom" anchorElement="target">
                    <p className="text-truncate" style={{ maxWidth: "200px" }}>
                      {ticket.summary}
                    </p>
                  </Tooltip>
                  <div className="d-flex justify-content-between align-items-center mt-2">
                    <div className="d-flex align-items-center">
                      <img
                        src={
                          ticket.createdbyimage
                            ? getImage(ticket.createdbyimage)
                            : usersvg
                        }
                        className="rounded-circle"
                        width="32px"
                        height="32px"
                        alt="User"
                      />
                      <span
                        className="user-name"
                        style={{
                          display: "inlineBlock",
                          paddingLeft: "10px",
                          verticalAlign: "middle",
                          lineHeight: "32px",
                        }}
                        title={ticket.createdbyname}
                      >
                        {ticket.createdbyname}
                      </span>
                    </div>
                    <small className="text-muted">{ticket.propertyname}</small>
                  </div>
                </Card.Body>
              </Card>
            ))}
          </RxInfiniteScroll>
        </DataStatusWrapper>
      </Modal.Body>
    </Modal>
  );
};

export default InspectionTicketDetails;
