import React from "react";
import ChartComponent from "./ChartComponent";
import { useChartContext } from "../context/ChartContext";

type ChartType = "pie" | "donut" | "bar" | "stackedBar" | "line" | "area" | "scatter" | "category";

interface ChartOption {
  type: ChartType;
  id?: number;
  category?: string;
  label: string;
}

interface ChartContainerProps {
  activeTab: string;
  surveyId?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({ activeTab, surveyId }) => {
  const { selectedChartOption, getChartsOfType } = useChartContext();

  // surveyId can be used for future survey-specific chart filtering

  // Get all charts of the selected type for multi-chart display
  const chartsToDisplay = getChartsOfType(selectedChartOption.type);

  // Function to render multiple charts in a 2-column grid
  const renderMultipleCharts = (charts: ChartOption[]) => {
    return charts.map((chart, index) => (
      <div key={`${chart.type}-${chart.id || chart.category}-${index}`} className="col-md-12 mb-4">
        <ChartComponent
          chartType={chart.type}
          chartId={chart.id}
          category={chart.category}
        />
      </div>
    ));
  };

  return (
    <>
      <div className="row">
        {activeTab === "summary" && (
          <>
            {/* Display all charts of the selected type in 2-column grid */}
            {renderMultipleCharts(chartsToDisplay)}
          </>
        )}

        {activeTab === "goose" && (
          <>
            {/* Display all charts of the selected type in 2-column grid */}
            {renderMultipleCharts(chartsToDisplay)}
          </>
        )}

        {activeTab === "responses" && (
          <>
            {/* Display all charts of the selected type in 2-column grid */}
            {renderMultipleCharts(chartsToDisplay)}
          </>
        )}
      </div>
    </>
  );
};

export default ChartContainer;
