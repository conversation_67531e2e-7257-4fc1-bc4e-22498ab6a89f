import React from "react";
import ChartComponent from "./ChartComponent";
import { useChartContext } from "../context/ChartContext";
import { useSurveyChartData } from "../context/SurveyDataContext";

interface ChartContainerProps {
  activeTab: string;
  surveyId?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({ activeTab, surveyId }) => {
  const { selectedChartOption } = useChartContext();
  const { getChartData, hasData, isLoading, error } = useSurveyChartData();

  // Get chart data for the selected chart type
  const chartData = getChartData(selectedChartOption.type as "pie" | "stackedBar");


  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "200px" }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading charts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h6 className="alert-heading">Chart Error</h6>
        <p className="mb-0">{error}</p>
      </div>
    );
  }

  if (!hasData || chartData.length === 0) {
    return (
      <div className="alert alert-info" role="alert">
        <h6 className="alert-heading">No Chart Data Available</h6>
        <p className="mb-0">No survey response data available for charts. Please ensure the survey has responses.</p>
      </div>
    );
  }

  // Function to render multiple charts based on available questions
  const renderMultipleCharts = () => {
    // Render all charts for all questions in chartData
    return chartData.map((_, index) => (
      <div key={`${selectedChartOption.type}-question-${index}`} className="col-md-12 mb-4">
        <ChartComponent
          chartType={selectedChartOption.type as "pie" | "stackedBar"}
          questionIndex={index}
        />
      </div>
    ));
  };

  return (
    <>
      <div className="row">
        {/* {activeTab === "goose" && (
          <>
             // Display charts for goose tab
            {renderMultipleCharts()}
          </>
        )} */}
        {activeTab === "summary" && (
          <>
            {/* Display charts for summary tab */}
            {renderMultipleCharts()}
          </>
        )}
        {activeTab === "responses" && (
          <>
            {/* Display charts for responses tab */}
            {renderMultipleCharts()}
          </>
        )}
      </div>
    </>
  );
};

export default ChartContainer;
