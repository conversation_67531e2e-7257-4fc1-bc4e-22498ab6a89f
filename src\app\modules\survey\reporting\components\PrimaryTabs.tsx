import React from "react";

interface PrimaryTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const PrimaryTabs: React.FC<PrimaryTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  const tabs = [
    { id: "template", label: "View Template" },
    { id: "reporting", label: "Reporting" },
    { id: "qrcode", label: "Generate QR Code" },
    { id: "ticket", label: "Survey Ticket" },
  ];

  return (
    <div className="d-flex">
      {tabs.map((tab) => (
        <div
          key={tab.id}
          className={`px-3 py-2 mx-1 cursor-pointer`}
          onClick={() => onTabChange(tab.id)}
          style={{
            color: activeTab === tab.id ? "var(--Rx-bg)" : "#aaa",
            borderRadius: "100px",
            cursor: "pointer",
            position: "relative",
            backgroundColor:
              activeTab === tab.id ? "var(--Rx-btn-bg)" : "transparent",
          }}
        >
          {tab.label}
        </div>
      ))}
    </div>
  );
};

export default PrimaryTabs;
