import { useEffect, useState } from "react";
import { Card, Col, Modal, Row } from "react-bootstrap";
import { RxCross2 } from "react-icons/rx";
import { useNavigate } from "react-router";
import { escalationService } from "../escalation.helper";
import encryptDecryptUtil from "../../../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../../../common/SwalMessage";
import { getImage } from "../../../../../../utils/CommonUtils";

const ViewEscalationUserModal = ({ viewMemberModal, setViewMemberModal, escalationId, setEscalationId, viewUserData, setViewUserData, setLoading, propertyId, departmentid }: any) => {
    const [displayUserData, setDisplayUserData] = useState([]);
    const navigate = useNavigate();
    let keyinfo = JSON.parse(localStorage.keyinfo);

    const getUserMemberDataInternal = async () => {
        setLoading(true);
        await escalationService
            .getEscalationUserData("", "", escalationId, "")
            .then((response: any) => {
                setLoading(true);
                const responseData = response.data;
                if (responseData.success == true) {
                    const decryptedData = encryptDecryptUtil.decryptData(
                        responseData?.data,
                        keyinfo.syckey
                    );
                    const resultData = JSON.parse(decryptedData);
                    // console.log(resultData);
                    setDisplayUserData(resultData);
                    setLoading(false);
                } else {
                    setLoading(false);
                    SwalMessage(null, responseData?.errormsg, "Ok", "error", false);
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    setLoading(false);
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error.response.data.message, "Ok", "error", false);
            })
            .finally(() => {
                setLoading(false);
            });
        // handle the response as required
    };

    useEffect(() => {
        if (viewMemberModal) {
            setDisplayUserData(viewUserData);
            // setSelectMemberData(selectedmemberData); // pre-fill selection from parent
        } else {
            setDisplayUserData([]);
            setEscalationId("");
            setViewUserData([]);
        }
    }, [viewMemberModal]);

    // Handle search with debounce using internal function
    useEffect(() => {
        if (viewMemberModal) {
            const handler = setTimeout(() => {
                getUserMemberDataInternal();
            }, 400);
            return () => {
                clearTimeout(handler);
            };
        }
    }, [viewMemberModal]);
    return (
        <>
            <Modal
                className="modal-right modal-right-small"
                scrollable={true}
                show={viewMemberModal}
            >
                <Modal.Header className=" border-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Member List</h2>
                        </Col>
                        <Col xs={2} className=" text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => {
                                    setViewMemberModal(false);
                                }}
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="p-0 mt-5">
                    {displayUserData && displayUserData.length > 0 ? (
                        displayUserData.map((item: any, index: any) => (
                            <Card className="mb-2 custom-card" key={index}>
                                <Card.Body className="p-3">
                                    <div className="d-flex align-items-center justify-content-between gap-2">
                                        <div className="d-flex align-items-center gap-2">
                                            <img
                                                src={getImage(item.image)}
                                                alt="propertylogo"
                                                height={"48px"}
                                                width={"48px"}
                                                className="user-image"
                                            />
                                            <div>{item.label}</div>
                                        </div>
                                    </div>
                                </Card.Body>
                            </Card>
                        ))
                    ) : (
                        <div className="text-center">No Member exist</div>
                    )}
                </Modal.Body>
                {/* <Modal.Footer className="border-0 p-0 ">
      <span className="btn rx-btn" onClick={() => savedata()}>
        Save & Continue
      </span>
    </Modal.Footer> */}
            </Modal>
        </>
    )
}

export default ViewEscalationUserModal