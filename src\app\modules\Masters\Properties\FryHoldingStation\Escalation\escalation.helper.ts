import { APIs } from "../../../../../serverconfig/apiURLs";
import axiosInstance from "../../../../../serverconfig/axiosInstance";
import encryptDecryptUtil from "../../../../../utils/encrypt-decrypt-util";



class EscalationService {
    getGridData(
        page: any,
        size: any,
        search: any,
        propertyId: any,
        departmentId: any
    ) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const bodyparam = {
            page: page,
            size: size,
            search: search ? search : "",
            sortingColumns: [
                {
                    sortOrder: 0,
                    columnName: "",
                },
            ],
            propertyId: propertyId ? propertyId : "",
            departmentId: departmentId ? departmentId : ""
        };
        // console.log("bodyparam", bodyparam)
        const payload = encryptDecryptUtil.encryptData(
            JSON.stringify(bodyparam),
            keyinfo.syckey
        );

        return axiosInstance.post(APIs.GRIDCALLS.escalationGrid, {
            encryptedData: payload,
        });
    }

    getEditData(escalationId: any, propertyId: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const obj = {
            escalationId: escalationId,
            propertyId: propertyId,
            departmentId: ""
        };
        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(obj),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.getEscalationData, {
            encryptedData: payload,
        });
    }

    getEscalationUserData(propertyId: any, escalationId: any, departmentId: any, search: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const obj = {
            escalationId: escalationId ? escalationId : "",
            propertyId: propertyId ? propertyId : "",
            departmentId: departmentId ? departmentId : "",
            search: search ? search : "",
        };
        console.log("obj", obj);
        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(obj),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.escalationuserdropdown, {
            encryptedData: payload,
        });
    }

    getuserTicketdropdownforticket(
        search: any,
        propertyId: any,
    ) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payload = {
            search: search ? search : "",
            propertyid: propertyId ? propertyId : "",
            departmentid: "",
            ticketid: "",
            isinternalticket: 1
        };
        // console.log("payload", payload);
        let encPayload = encryptDecryptUtil
            .encryptData(JSON.stringify(payload), keyinfo.syckey)
            .toString();
        return axiosInstance.post(APIs.ALL_HEADERS.getuserticketdropdownforticket, {
            encryptedData: encPayload,
        });
    }

    createEscalation(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.createEscalation, {
            encryptedData: encPayload,
        });
    }

    getDeleteData(id: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const obj = { companyid: id };
        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(obj),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.deleteCompany, {
            encryptedData: payload,
        });
    }

    changeStatus(id: any, status: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const obj = {
            escalationId: id,
            isActive: status,
        }

        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(obj),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.escalationactive, {
            encryptedData: payload,
        });
    }

    excelExport(search?: any) {
        return axiosInstance.get(APIs.ALL_HEADERS.companyExport, {
            params: { search: search ? search.trim() : "" },
            responseType: "blob",
        });
    }

    uploadDocument(payload: any, imagePath: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        const formData = new FormData();
        formData.append("encryptedData", encPayload);
        formData.append("files", imagePath);
        return axiosInstance.post(APIs.MULTIPART.companyUploadDocument, formData);
    }

    // getCompanyPermission = () =>{
    //   return axiosInstance.post(APIs.ALL_HEADERS.getCompanyPermission)
    //  }

    validateCompanyCode(companyCode: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const bodyparms = {
            companyCode: companyCode,
        };
        // console.log(bodyparms)
        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(bodyparms),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.validatecompanycode, {
            encryptedData: payload,
        });
    }

    deleteCompanyDocument(id: any, companyId: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const obj = {
            documentId: id ? id : "",
            companyId: companyId ? companyId : ""
        }
        let payload = encryptDecryptUtil.encryptData(
            JSON.stringify(obj),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.ALL_HEADERS.deleteCompanyDocument, {
            encryptedData: payload,
        });
    }

    escalationloggrid(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        let encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.GRIDCALLS.escalationloggrid, {
            encryptedData: encPayload,
        });
    }

    escalationmodulelogs(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        let encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.GRIDCALLS.escalationmodulelogs, {
            encryptedData: encPayload,
        });
    }

    getescalationlogs(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        let encPayload = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return axiosInstance.post(APIs.GRIDCALLS.getescalationlogs, {
            encryptedData: encPayload,
        });
    }

    escalationcompleted(escalationId: any) {
        return axiosInstance.post(APIs.NO_HEADERS.escalationcompleted + `?escalationId=${escalationId}`);
    }
}
export const escalationService = new EscalationService();
