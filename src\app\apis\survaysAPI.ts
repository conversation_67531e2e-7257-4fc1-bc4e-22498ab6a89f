import { createApi } from "@reduxjs/toolkit/query/react";
import { fetchRxSurveyAPI } from "../redux/fetchBase";
import { APIs } from "../serverconfig/apiURLs";
import {
  IAddQuestionToSurveyPayload,
  IGetSurveyPayload,
  IPaginateResponse,
  IResponse,
  ISubmitSurveyResponsePayload,
  ISurveyById,
  ISurveyFullDetails,
  ISurveyGeneralSetupPayload,
  ISurveyQuestionListItem,
  ISurveyTableItem,
  ISurveyReportingSummaryPayload,
  ISurveyReportingSummaryResponse,
  SurveyConfigurationPayload,
} from "./type";
import { defaultSortingColumn } from "../utils/helper";

export type GetAllSurveysTemplateRes = IResponse<
  IPaginateResponse<ISurveyTableItem[]>
>;

const surveysAPI = createApi({
  reducerPath: "surveysAPI",
  baseQuery: fetchRxSurveyAPI(),
  endpoints: (builder) => ({
    generalSetup: builder.mutation<IResponse<any>, ISurveyGeneralSetupPayload>({
      query: (payload) => {
        return {
          url: APIs.CREATE_SURVEY_GENERAL_SETUP,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    getAllSurvey: builder.mutation<
      GetAllSurveysTemplateRes,
      Partial<IGetSurveyPayload>
    >({
      query: (payload) => {
        const mergePayload = Object.assign(
          {
            page: 1,
            size: 10,
            search: "",
            sortingColumns: defaultSortingColumn,
            status: true,
          },
          payload
        );
        return {
          url: APIs.GET_ALL_SURVEY,
          method: "POST",
          body: mergePayload || {},
        };
      },
    }),

    addQUestionToSurvey: builder.mutation<
      IResponse<any>,
      Partial<IAddQuestionToSurveyPayload>
    >({
      query: (payload) => {
        const mergePayload = Object.assign(
          {
            questionText: "",
            responseType: "",
            options: [],
            rattingIcon: "",
            isRequired: true,
            allowAttachment: true,
            attachmentType: [],
            allowComment: true,
            comment: "",
            autoTicketGeneration: true,
            allowBranching: true,
            questionOrder: 0,
            branchingQuestion: {
              questionText: "",
              responseType: "",
              allowAttachment: true,
              attachmentType: [],
            },
          },
          payload
        );
        return {
          url: APIs.ADD_QUESTION_TO_SURVEY,
          method: "POST",
          body: mergePayload || {},
        };
      },
    }),

    getSurveyQuestion: builder.mutation<
      IResponse<ISurveyQuestionListItem[]>,
      Partial<{
        surveyId: string;
      }>
    >({
      query: (payload) => {
        return {
          url: APIs.GET_SURVEY_QUESTION,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    getSurveyFullDetails: builder.mutation<
      IResponse<ISurveyFullDetails>,
      ISurveyById
    >({
      query: (payload) => {
        return {
          url: APIs.GET_FULL_SURVEY_DETAILS,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    getSurveyReportingSummary: builder.mutation<
      ISurveyReportingSummaryResponse,
      ISurveyReportingSummaryPayload
    >({
      query: (payload) => {
        return {
          url: APIs.GET_SURVEY_REPORTING_SUMMARY,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    setSurveyConfiguration: builder.mutation<
      IResponse<any>,
      Partial<SurveyConfigurationPayload>
    >({
      query: (payload) => {
        return {
          url: APIs.SET_SURVEY_CONFIGURATION,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    getQrSurveyCode: builder.mutation<
      IResponse<{ qrcodeBase64: string }>,
      { surveyId: string }
    >({
      query: (payload) => {
        return {
          url: APIs.GET_QR_SURVEY_CODE,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    duplicateSurvey: builder.mutation<IResponse<any>, { surveyId: string }>({
      query: (payload) => {
        return {
          url: APIs.DUPLICATE_SURVEY,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    submitSurveyResponse: builder.mutation<
      IResponse<{
        surveyId: string;
        surveyResponseId: string;
      }>,
      ISubmitSurveyResponsePayload
    >({
      query: (payload) => {
        return {
          url: APIs.SUBMIT_SURVEY_RESPONSE,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    inactiveSurveyResponse: builder.mutation<
      IResponse<any>,
      {
        surveyId: string;
      }
    >({
      query: (payload) => {
        return {
          url: APIs.INACTIVE_SURVEY,
          method: "POST",
          body: payload || {},
        };
      },
    }),

    // TODO: Future API endpoint for individual survey responses
    // Uncomment when backend API is available
    /*
    getSurveyIndividualResponses: builder.mutation<
      IResponse<{
        surveyId: string;
        totalResponses: number;
        responses: Array<{
          responseId: string;
          submittedAt: string;
          respondentInfo?: {
            name?: string;
            email?: string;
            phone?: string;
          };
          property?: string;
          department?: string;
          status: string;
          score?: number;
          answers: Array<{
            questionId: string;
            questionText: string;
            answerText: string;
            answerValue: any;
            questionType: string;
          }>;
        }>;
      }>,
      {
        surveyId: string;
        page?: number;
        size?: number;
      }
    >({
      query: (payload) => {
        return {
          url: APIs.GET_SURVEY_INDIVIDUAL_RESPONSES,
          method: "POST",
          body: payload || {},
        };
      },
    }),
    */
  }),
});

export default surveysAPI;
export const {
  useGeneralSetupMutation,
  useGetAllSurveyMutation,
  useAddQUestionToSurveyMutation,
  useGetSurveyQuestionMutation,
  useGetSurveyFullDetailsMutation,
  useGetSurveyReportingSummaryMutation,
  useSetSurveyConfigurationMutation,
  useGetQrSurveyCodeMutation,
  useDuplicateSurveyMutation,
  useSubmitSurveyResponseMutation,
  useInactiveSurveyResponseMutation
} = surveysAPI;
