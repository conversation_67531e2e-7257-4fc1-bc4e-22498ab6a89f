import React, { useState } from "react";
import { Dropdown, ButtonGroup } from "react-bootstrap";
import { FilterSvg } from "../../../../../utils/SvgUtils";
import GlobalFilterModal from "./GlobalFilterModal";
import LocalFilterModal from "./LocalFilterModal";
import PropertyAssignmentModal from "./PropertyAssignmentModal";
import CustomFilterModal from "./CustomFilterModal";
import { FILTER_TYPES } from "../../config/chartConfig";

interface FilterManagerProps {
  onFiltersChange?: (filters: any) => void;
  currentFilters?: any;
}

const FilterManager: React.FC<FilterManagerProps> = ({
  onFiltersChange,
  currentFilters = {}
}) => {
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [appliedFilters, setAppliedFilters] = useState<any>(currentFilters);

  // Get enabled filter types
  const enabledFilters = Object.values(FILTER_TYPES).filter(filter => filter.isEnabled);

  const handleFilterApply = (filterType: string, filterData: any) => {
    const newFilters = {
      ...appliedFilters,
      [filterType]: filterData
    };
    
    setAppliedFilters(newFilters);
    
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
    
    setActiveModal(null);
  };

  const openFilterModal = (filterType: string) => {
    setActiveModal(filterType);
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    Object.values(appliedFilters).forEach((filterData: any) => {
      if (filterData && typeof filterData === 'object') {
        // Count non-empty filter values
        Object.values(filterData).forEach((value: any) => {
          if (Array.isArray(value) && value.length > 0) count++;
          else if (value && typeof value === 'string' && value.trim() !== '') count++;
          else if (value && typeof value === 'object' && Object.keys(value).length > 0) count++;
        });
      }
    });
    return count;
  };

  const clearAllFilters = () => {
    setAppliedFilters({});
    if (onFiltersChange) {
      onFiltersChange({});
    }
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <>
      <ButtonGroup>
        <Dropdown>
          <Dropdown.Toggle
            variant="link"
            className="user-image d-flex align-items-center justify-content-center text-center cursor-pointer position-relative"
            style={{ border: 'none', background: 'none' }}
          >
            <FilterSvg width="17" height="17" className="svgicon" />
            {activeFilterCount > 0 && (
              <span 
                className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                style={{ fontSize: '0.6rem' }}
              >
                {activeFilterCount}
              </span>
            )}
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Header>Filter Options</Dropdown.Header>
            
            {enabledFilters.map((filter) => (
              <Dropdown.Item
                key={filter.id}
                onClick={() => openFilterModal(filter.id)}
              >
                {filter.name}
              </Dropdown.Item>
            ))}
            
            {activeFilterCount > 0 && (
              <>
                <Dropdown.Divider />
                <Dropdown.Item
                  onClick={clearAllFilters}
                  className="text-danger"
                >
                  Clear All Filters
                </Dropdown.Item>
              </>
            )}
          </Dropdown.Menu>
        </Dropdown>
      </ButtonGroup>

      {/* Filter Modals */}
      <GlobalFilterModal
        isOpen={activeModal === 'global'}
        onClose={closeModal}
        onApply={(data) => handleFilterApply('global', data)}
        currentFilters={appliedFilters.global}
        filterType="global"
      />

      <LocalFilterModal
        isOpen={activeModal === 'local'}
        onClose={closeModal}
        onApply={(data) => handleFilterApply('local', data)}
        currentFilters={appliedFilters.local}
        filterType="local"
      />

      <PropertyAssignmentModal
        isOpen={activeModal === 'propertyAssignment'}
        onClose={closeModal}
        onApply={(data) => handleFilterApply('propertyAssignment', data)}
        currentFilters={appliedFilters.propertyAssignment}
        filterType="property"
      />

      <CustomFilterModal
        isOpen={activeModal === 'custom'}
        onClose={closeModal}
        onApply={(data) => handleFilterApply('custom', data)}
        currentFilters={appliedFilters.custom}
        filterType="custom"
      />
    </>
  );
};

export default FilterManager;
