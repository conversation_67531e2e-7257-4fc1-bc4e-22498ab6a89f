import Picker from "@emoji-mart/react";
import Quill from "quill";
import { Mention, MentionBlot } from "quill-mention";
import "quill-mention/dist/quill.mention.css";
import "quill/dist/quill.snow.css";
import React, { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { BsEmojiGrin } from "react-icons/bs";
import sendIcon from "../../../efive_assets/images/send.svg";
import "./rxMentionInput.css";
import clsx from "clsx";
import { CgFormatColor } from "react-icons/cg";
import { HiOutlinePlusCircle } from "react-icons/hi";
import { BiMicrophone } from "react-icons/bi";

Quill.register({
  "blots/mention": MentionBlot,
  "modules/mention": Mention,
});

export interface IMentionList {
  id: string;
  value: string;
}

interface Props {
  mentionList?: IMentionList[];
  onChange?: (content: string) => void;
  onSend?: (content: string, mentionedUserList?: IMentionList[]) => void;
  onAttachment?: () => void;
  handleToggleRecorder: () => void;
  toggleEmojiPicker?: () => void;
  showEmojiPicker: boolean;
  isSending?: boolean;
  value?: string;
  handleTyping: () => void;
  maxChar?: number; // Optional prop for maximum character limit
}

const RxMentionInput: React.FC<Props> = ({
  mentionList,
  onChange,
  onAttachment,
  handleToggleRecorder,
  toggleEmojiPicker,
  showEmojiPicker,
  onSend,
  isSending,
  value,
  handleTyping,
  maxChar = 5000, // Default maxChar to 5000
}) => {
  const editorRef = useRef<HTMLDivElement | null>(null);
  const quillRef = useRef<Quill | null>(null);
  const [charCount, setCharCount] = useState(0);

  const mentionedUserRef = useRef<IMentionList[]>([]);
  const isMentionOpenRef = useRef<boolean>(false);

  // Initialize Quill only once
  useEffect(() => {
    if (editorRef.current && !quillRef.current) {
      const quill = new Quill(editorRef.current, {
        theme: "snow",
        modules: {
          toolbar: [["bold", "italic", "underline"]],
          mention: {
            source: function (searchTerm: string, renderList: Function) {
              // This will be replaced in the next effect
              renderList([]);
            },
            onSelect: (item: any, insertItem: any) => {
              insertItem(item);
              const isAlreadyMentioned = mentionedUserRef.current.some(
                (mention: any) => mention.id.toString() === item.id.toString()
              );
              if (isAlreadyMentioned) {
                return;
              } else {
                const mentionedUser: IMentionList = {
                  id: item.id,
                  value: item.value,
                };
                mentionedUserRef.current.push(mentionedUser);
              }
            },
          },
        },
      });
      quillRef.current = quill;
      quill.on("text-change", () => {
        const mentionOpen = (quill as any)?.getModule("mention")?.isOpen;
        isMentionOpenRef.current = mentionOpen;
        if (quill && quill.root) {
          const currentContent = quill.root.innerHTML;
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = currentContent;
          const plainTextWithMentions = tempDiv.innerText;
          const charCount = plainTextWithMentions.trim().length;
          if (charCount > maxChar) {
            quill.deleteText(maxChar, plainTextWithMentions.length);
          } else {
            setCharCount(charCount);
            onChange?.(currentContent);
            handleTyping();
          }
        }
      });
    }
    // Clean up on unmount
    return () => {
      if (quillRef.current) {
        quillRef.current.off("text-change");
        quillRef.current = null;
      }
      if (editorRef.current) {
        editorRef.current.innerHTML = "";
      }
    };
  }, []);

  // Update mention source when mentionList changes
  useEffect(() => {
    if (quillRef.current) {
      const mentionModule = (quillRef.current as any).getModule("mention");
      if (mentionModule) {
        mentionModule.options.source = function (searchTerm: string, renderList: Function) {
          const mentions = mentionList || [];
          const matches = mentions.filter((item: IMentionList) =>
            item.value.toLowerCase().includes(searchTerm.toLowerCase())
          );
          renderList(matches);
        };
      }
    }
  }, [mentionList]);

  useEffect(() => {
    if (quillRef.current) {
      quillRef.current.root.innerHTML = value || "";
      setCharCount((quillRef.current.getText() || "").trim().length);
    }
  }, [value]);

  const formatContentWithLink = (content: string): string => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, "text/html");

    // Remove leading newlines (<p><br></p>) at the beginning of the document
    while (
      doc.body.firstElementChild?.tagName === "P" &&
      doc.body.firstElementChild.innerHTML === "<br>"
    ) {
      doc.body.removeChild(doc.body.firstElementChild);
    }

    // Remove trailing newlines (<p><br></p>) at the end of the document
    while (
      doc.body.lastElementChild?.tagName === "P" &&
      doc.body.lastElementChild.innerHTML === "<br>"
    ) {
      doc.body.removeChild(doc.body.lastElementChild);
    }

    const walker = document.createTreeWalker(
      doc.body,
      NodeFilter.SHOW_TEXT,
      null
    );
    while (walker.nextNode()) {
      const node = walker.currentNode;
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      if (node.nodeValue?.match(urlRegex)) {
        const fragment = document.createDocumentFragment();
        const parts = node.nodeValue.split(urlRegex);

        parts.forEach((part) => {
          if (urlRegex.test(part)) {
            const anchor = document.createElement("a");
            anchor.href = part;
            anchor.textContent = part;
            anchor.target = "_blank";
            anchor.style.color = "white";
            anchor.style.textDecoration = "underline";
            fragment.appendChild(anchor);
          } else {
            fragment.appendChild(document.createTextNode(part));
          }
        });

        node.parentNode?.replaceChild(fragment, node);
      }
    }

    return doc.body.innerHTML;
  };

  const handleSend = () => {
    if (quillRef.current) {
      let content = quillRef.current.root.innerHTML;
      // if (charCount > 0) {
      content = formatContentWithLink(content);
      onSend?.(content, mentionedUserRef.current);
      quillRef.current.root.innerHTML = "";
      mentionedUserRef.current = [];
      quillRef.current.root.focus();
      setCharCount(0);
      // }
    }
  };

  // console.log('quillRef.current.getModule("mention")',quillRef.current?.getModule("mention").isOpen);

  // useEffect(() => {
  //   if(quillRef.current){
  //     const mentionOpen = quillRef.current.getModule("mention").isOpen;
  //     console.log('mentionOpen',mentionOpen);
  //   }

  // }, []);

  const handleEmojiSelect = (emoji: any) => {
    if (quillRef.current) {
      const cursorPosition = quillRef.current.getSelection()?.index || 0;
      quillRef.current.insertText(cursorPosition, emoji.native);
      quillRef.current.setSelection(cursorPosition + emoji.native.length);
      setCharCount((quillRef.current.getText() || "").trim().length);
    }
  };

  const handleKeyDown = (event: any) => {
    if (event.key === "Enter" && !event.shiftKey && !isMentionOpenRef.current) {
      event.preventDefault();
      handleSend();
    }
  };

  const [showToolbar, setShowToolbar] = useState(false);
  console.log("mentionList2", mentionList);

  return (
    <div
      className={clsx("position-relative rx-editor", {
        hideToolBar: !showToolbar,
      })}
    >
      <div className="char-counter">
        {charCount}/{maxChar}
      </div>
      <div className="rx-editor-adeptor">
        <button
          className="rx-editor-btn"
          style={{
            margin: "0 0px 16px 10px",
          }}
          onClick={onAttachment}
        >
          <HiOutlinePlusCircle
            style={{ color: "#636674", width: 20, height: 20 }}
          />
        </button>
        <div
          className="d-flex align-items-center flex-column"
          style={{
            width: "calc(100% - 60px)",
          }}
        >
          <div
            ref={editorRef}
            onKeyDown={handleKeyDown}
            className="rx-editor-input"
          />
        </div>
        <button
          aria-label="Send"
          className="rx-editor-btn"
          onClick={handleSend}
          disabled={isSending}
          style={{
            margin: "0 10px 16px 0px",
          }}
        >
          <img src={sendIcon} width={14} height={14} />
        </button>
      </div>
      <div
        className="position-absolute"
        style={{
          right: 45,
          bottom: 11,
          marginRight: 10,
        }}
      >
        <div className="rx-mention-btn-wrapper ms-2">
          <CgFormatColor
            onClick={() => setShowToolbar((prev) => !prev)}
            style={{
              color: showToolbar ? "#06c" : "#636674",
            }}
          />
          <button
            id="emoji-picker"
            className="k-button k-button-md k-button-flat k-button-flat-base k-rounded-md k-icon-button rx-atc-btn position-relative"
            onClick={toggleEmojiPicker}
          >
            <BsEmojiGrin
              style={{
                width: 16,
                height: 16,
              }}
            />
          </button>
          <BiMicrophone
            style={{ cursor: "pointer" }}
            onClick={() => {
              // if (onAttachment) {
              //   onAttachment();
              // }
              handleToggleRecorder();
            }}
          />
        </div>
      </div>

      {showEmojiPicker && (
        <div
          style={{
            position: "absolute",
            bottom: "45px",
            right: "80px",
            zIndex: 1055,
          }}
        >
          <Picker onEmojiSelect={handleEmojiSelect} lazyLoadEmojis />
        </div>
      )}
    </div>
  );
};

// export default RxMentionInput;

// function areEqual(prevProps: Props, nextProps: Props) {
//   if (JSON.stringify(prevProps.mentionList) !== JSON.stringify(nextProps.mentionList)) {
//     return false;
//   }
//   // // Shallow compare other props
//   // for (const key of Object.keys(prevProps)) {
//   //   if (
//   //     key !== "mentionList" &&
//   //     prevProps[key as keyof Props] !== nextProps[key as keyof Props]
//   //   ) {
//   //     return false;
//   //   }
//   // }
//   return true;
// }

// export default React.memo(RxMentionInput, areEqual);
export default RxMentionInput;
