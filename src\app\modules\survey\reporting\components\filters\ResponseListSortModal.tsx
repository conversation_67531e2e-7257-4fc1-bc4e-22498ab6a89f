import React, { useState, useEffect } from "react";
import { Col, Form } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import { SortState, ResponseListConfig } from "../../types/chartTypes";
import surveyConfig from "../../data/surveyConfig.json";

interface ResponseListSortModalProps extends FilterModalProps {
  currentSort?: SortState;
  onSortChange?: (sort: SortState) => void;
}

const ResponseListSortModal: React.FC<ResponseListSortModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentSort = { field: 'submittedAt', direction: 'desc' },
  onSortChange
}) => {
  const config = surveyConfig.responseListConfig as ResponseListConfig;
  const [sortData, setSortData] = useState<SortState>(currentSort);
  const [tempSortData, setTempSortData] = useState<SortState>(sortData);

  useEffect(() => {
    if (currentSort) {
      setSortData(currentSort);
      setTempSortData(currentSort);
    }
  }, [currentSort, isOpen]);

  const handleApply = () => {
    setSortData(tempSortData);

    if (onSortChange) {
      onSortChange(tempSortData);
    }

    if (onApply) {
      onApply(tempSortData);
    }

    onClose();
  };

  const handleReset = () => {
    const resetSort = { field: 'submittedAt', direction: 'desc' as 'desc' };
    setTempSortData(resetSort);
    setSortData(resetSort);
  };

  const handleFieldChange = (field: string) => {
    setTempSortData(prev => ({ ...prev, field }));
  };

  const handleDirectionChange = (direction: 'asc' | 'desc') => {
    setTempSortData(prev => ({ ...prev, direction }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Sorting"
      filterType="responseList"
      showApplyButton={true}
      showResetButton={true}
    >
      {/* <Col sm={12} className="mb-3">
        <Form.Label>Sort by</Form.Label>
        <Form.Select
          value={tempSortData.field}
          onChange={(e) => handleFieldChange(e.target.value)}
        >
          {config.columns.filter(col => col.sortable).map((col) => (
            <option key={col.key} value={col.key}>
              {col.label}
            </option>
          ))}
        </Form.Select>
      </Col> */}

      <Col sm={12} className="mb-3">
        {/* <Form.Label>Direction</Form.Label> */}
        <div>
          <Form.Check
            type="radio"
            id="asc"
            name="sortDirection"
            label="Sort by Ascending"
            checked={tempSortData.direction === 'asc'}
            onChange={() => handleDirectionChange('asc')}
            className="mb-5"
          />
          <Form.Check
            type="radio"
            id="desc"
            name="sortDirection"
            label="Sort by Descending"
            checked={tempSortData.direction === 'desc'}
            onChange={() => handleDirectionChange('desc')}
          />
        </div>
      </Col>
    </BaseFilterModal>
  );
};

export default ResponseListSortModal;
