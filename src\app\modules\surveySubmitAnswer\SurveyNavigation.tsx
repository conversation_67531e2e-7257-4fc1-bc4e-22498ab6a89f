import React from 'react';
import { Button } from 'react-bootstrap';

interface Props {
  currentQuestion: number;
  totalQuestions: number;
  isFirstQuestion: boolean;
  isLastQuestion: boolean;
  isSubmitting: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

const SurveyNavigation: React.FC<Props> = ({
  currentQuestion,
  totalQuestions,
  isFirstQuestion,
  isLastQuestion,
  isSubmitting,
  onPrevious,
  onNext,
  onSubmit,
}) => {
  return (
    <div className="survey-navigation mt-4">
      <div className="d-flex justify-content-between align-items-center">
        {/* Back Button */}
        <Button
          variant="outline-secondary"
          onClick={onPrevious}
          disabled={isFirstQuestion || isSubmitting}
          className="px-4"
          type='button'
        >
          &lt; Back
        </Button>

        {/* Question Counter */}
        <div className="question-counter">
          <span className="fw-bold">
            ({currentQuestion}/{totalQuestions})
          </span>
        </div>

        {/* Next/Submit Button */}
        {isLastQuestion ? (
          <Button
            variant="primary"
            onClick={onSubmit}
            disabled={isSubmitting}
            className="px-4"
            type='button'
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </Button>
        ) : (
          <Button
            variant="primary"
            onClick={onNext}
            disabled={isSubmitting}
            className="px-4"
          >
            Next &gt;
          </Button>
        )}
      </div>
    </div>
  );
};

export default SurveyNavigation;
