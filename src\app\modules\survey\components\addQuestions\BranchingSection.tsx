import React from "react";
import { Formik, Form } from "formik";
import FormInput from "../../../../component/form/FormInput";
import FormLabel from "../../../../component/form/FormLabel";
import FormSwitch from "../../../../component/form/FormSwitch";
import {
  useBranchingForm,
  BranchingFormValues,
} from "./hooks/useBranchingForm";
import { Modal } from "react-bootstrap";

interface Props {
  onSubmit: (values: BranchingFormValues) => void;
  onSaveAndClose?: (values: BranchingFormValues) => void;
  onBack?: (values: BranchingFormValues) => void;
  onSaveAndNew?: (values: BranchingFormValues) => void;
  validate?: any;
  initialValues?: Partial<BranchingFormValues> | null;
  isEditQuestion?: boolean;
}

const BranchingSection: React.FC<Props> = ({
  onSubmit,
  onSaveAndClose,
  onBack,
  onSaveAndNew,
  validate,
  initialValues,
  isEditQuestion,
}) => {
  const { initialValues: defaultValues, validationSchema } = useBranchingForm();

  return (
    <Formik
      initialValues={{ ...defaultValues, ...initialValues }}
      validationSchema={validationSchema}
      onSubmit={() => {}}
    >
      {({
        values,
        setFieldValue,
        validateForm,
        setTouched,
        resetForm,
        handleSubmit,
      }) => (
        <Form id="branching-form">
          <div className="mb-4">
            <FormLabel required>Branching Question Text</FormLabel>
            <FormInput
              name="questionText"
              placeholder="Enter branching question text"
            />
          </div>

          <div className="">
            <div className="d-flex align-items-center justify-content-between mb-1 mt-5 pt-3">
              <FormLabel required={false}>Allow Attachment</FormLabel>
              <FormSwitch name="allowAttachment" />
            </div>

            {values.allowAttachment && (
              <div>
                <div className="d-flex align-items-center mb-1 gap-4">
                  {["IMAGE", "VIDEO", "AUDIO"].map((type) => (
                    <div key={type} className="d-flex align-items-center gap-2">
                      <input
                        type="checkbox"
                        id={`attachment-${type}`}
                        checked={values.attachmentType.includes(type)}
                        onChange={(e) => {
                          const newTypes = e.target.checked
                            ? [...values.attachmentType, type]
                            : values.attachmentType.filter(
                                (t: string) => t !== type
                              );
                          setFieldValue("attachmentType", newTypes);
                        }}
                      />
                      <label htmlFor={`attachment-${type}`}>{type}</label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <Modal.Footer
            className="border-0 p-0 w-100"
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              backgroundColor: "var(--Rx-bg)",
            }}
          >
            <div className="d-flex justify-content-between w-100">
              <div className="">
                <button
                  type="button"
                  className="btn btn-outline "
                  onClick={() => onBack?.(values)}
                >
                  Back
                </button>
                {!isEditQuestion && (
                  <>
                    <button
                      type="button"
                      className="btn btn-outline ms-3"
                      onClick={async () => {
                        // Validate before preview
                        const errors = validate
                          ? await validate(values)
                          : await validateForm();
                        if (Object.keys(errors).length === 0) {
                          onSubmit(values);
                        } else {
                          setTouched(
                            Object.keys(errors).reduce((acc, key) => {
                              acc[key] = true;
                              return acc;
                            }, {} as Record<string, boolean>)
                          );
                        }
                      }}
                    >
                      Preview
                    </button>
                    <button
                      type="submit"
                      className="btn rx-btn ms-3"
                      // onClick={async () => {
                      //   if (onSaveAndClose) {
                      //     const errors = validate ? await validate(values) : await validateForm();
                      //     if (Object.keys(errors).length === 0) {
                      //       onSaveAndClose(values);
                      //     } else {
                      //       setTouched(
                      //         Object.keys(errors).reduce((acc, key) => {
                      //           acc[key] = true;
                      //           return acc;
                      //         }, {} as Record<string, boolean>)
                      //       );
                      //     }
                      //   }
                      // }}
                      onClick={async () => {
                        await handleSubmit();
                        await onSaveAndClose?.(values);
                        resetForm({ values: defaultValues });
                      }}
                    >
                      Save & Close
                    </button>
                  </>
                )}
              </div>
              {isEditQuestion ? (
                <button
                  type="submit"
                  className="btn rx-btn ms-3"
                  onClick={async () => {
                    await handleSubmit();
                    await onSaveAndClose?.(values);
                    resetForm({ values: defaultValues });
                  }}
                >
                  Save & Close
                </button>
              ) : (
                <div className="">
                  <button
                    type="button"
                    className="btn rx-btn"
                    onClick={async () => {
                      // Validate before saving
                      const errors = validate ? await validate(values) : await validateForm();
                      if (Object.keys(errors).length === 0) {
                        await handleSubmit();
                        await onSaveAndNew?.(values);
                        resetForm({ values: defaultValues });
                      } else {
                        setTouched(
                          Object.keys(errors).reduce((acc, key) => {
                            acc[key] = true;
                            return acc;
                          }, {} as Record<string, boolean>)
                        );
                      }
                    }}
                  >
                    Save & Add New
                  </button>
                </div>
              )}
            </div>
          </Modal.Footer>
        </Form>
      )}
    </Formik>
  );
};

export default BranchingSection;
