import React, { useState, useEffect } from "react";
import { Col, Form } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import KendoMultiSelect from "../../../../common/KendoMultiSelect";

interface LocalFilterData {
  categories: string[];
  sentiments: string[];
  responseTypes: string[];
  questionTypes: string[];
}

const LocalFilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentFilters = {}
}) => {
  const [filterData, setFilterData] = useState<LocalFilterData>({
    categories: [],
    sentiments: [],
    responseTypes: [],
    questionTypes: []
  });

  const [tempFilterData, setTempFilterData] = useState<LocalFilterData>(filterData);

  // Mock data - in real implementation, this would come from API or context
  const categoriesData = [
    { label: "Health", value: "health" },
    { label: "Safety", value: "safety" },
    { label: "Procedures", value: "procedures" },
    { label: "Operations", value: "operations" },
    { label: "Maintenance", value: "maintenance" }
  ];

  const sentimentsData = [
    { label: "Positive 🙂", value: "positive" },
    { label: "Neutral 😐", value: "neutral" },
    { label: "Negative ☹️", value: "negative" }
  ];

  const responseTypesData = [
    { label: "Multiple Choice", value: "multiple_choice" },
    { label: "Single Choice", value: "single_choice" },
    { label: "Rating Scale", value: "rating" },
    { label: "Yes/No", value: "yes_no" },
    { label: "Text Input", value: "text" },
    { label: "NPS", value: "nps" }
  ];

  const questionTypesData = [
    { label: "Required", value: "required" },
    { label: "Optional", value: "optional" },
    { label: "Branching", value: "branching" },
    { label: "Standard", value: "standard" }
  ];

  useEffect(() => {
    if (currentFilters && Object.keys(currentFilters).length > 0) {
      const newFilterData = {
        categories: currentFilters.categories || [],
        sentiments: currentFilters.sentiments || [],
        responseTypes: currentFilters.responseTypes || [],
        questionTypes: currentFilters.questionTypes || []
      };

      // Only update if the data has actually changed
      setFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.categories) !== JSON.stringify(newFilterData.categories) ||
          JSON.stringify(prev.sentiments) !== JSON.stringify(newFilterData.sentiments) ||
          JSON.stringify(prev.responseTypes) !== JSON.stringify(newFilterData.responseTypes) ||
          JSON.stringify(prev.questionTypes) !== JSON.stringify(newFilterData.questionTypes);

        return hasChanged ? newFilterData : prev;
      });

      setTempFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.categories) !== JSON.stringify(newFilterData.categories) ||
          JSON.stringify(prev.sentiments) !== JSON.stringify(newFilterData.sentiments) ||
          JSON.stringify(prev.responseTypes) !== JSON.stringify(newFilterData.responseTypes) ||
          JSON.stringify(prev.questionTypes) !== JSON.stringify(newFilterData.questionTypes);

        return hasChanged ? newFilterData : prev;
      });
    }
  }, [currentFilters?.categories, currentFilters?.sentiments, currentFilters?.responseTypes, currentFilters?.questionTypes]);

  const handleApply = () => {
    setFilterData(tempFilterData);
    onApply(tempFilterData);
    onClose();
  };

  const handleReset = () => {
    const resetData = {
      categories: [],
      sentiments: [],
      responseTypes: [],
      questionTypes: []
    };
    setTempFilterData(resetData);
    setFilterData(resetData);
  };

  const handleCategoriesChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      categories: selected || []
    }));
  };

  const handleSentimentsChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      sentiments: selected || []
    }));
  };

  const handleResponseTypesChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      responseTypes: selected || []
    }));
  };

  const handleQuestionTypesChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      questionTypes: selected || []
    }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Local Filter"
      filterType="local"
    >
      <Col sm={12} className="mb-3">
        <Form.Label>Survey Categories</Form.Label>
        <KendoMultiSelect
          dropdownData={categoriesData}
          getData={tempFilterData.categories}
          setData={handleCategoriesChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Sentiments</Form.Label>
        <KendoMultiSelect
          dropdownData={sentimentsData}
          getData={tempFilterData.sentiments}
          setData={handleSentimentsChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Response Types</Form.Label>
        <KendoMultiSelect
          dropdownData={responseTypesData}
          getData={tempFilterData.responseTypes}
          setData={handleResponseTypesChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Question Types</Form.Label>
        <KendoMultiSelect
          dropdownData={questionTypesData}
          getData={tempFilterData.questionTypes}
          setData={handleQuestionTypesChange}
        />
      </Col>
    </BaseFilterModal>
  );
};

export default LocalFilterModal;
