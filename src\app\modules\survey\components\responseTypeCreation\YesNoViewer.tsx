import React from "react";
import { useFormikContext } from "formik";
import { QuestionFormValues, generateYesNoOptions } from "../addQuestions/util/constant";

const YesNoViewer: React.FC = () => {
  const { setFieldValue, values } = useFormikContext<QuestionFormValues>();

  // Set default options when component mounts
  React.useEffect(() => {
    const yesNoOptions = generateYesNoOptions();
    setFieldValue("options", yesNoOptions);
  }, [setFieldValue]);

  return (
    <div className="yes-no-viewer mt-5">
      {/* <div className="d-flex gap-3">
        <div className="form-check">
          <input
            className="form-check-input"
            type="radio"
            name="yesNoOption"
            id="yesOption"
            value="Yes"
            checked
            readOnly
          />
          <label className="form-check-label" htmlFor="yesOption">
            Yes
          </label>
        </div>
        <div className="form-check">
          <input
            className="form-check-input"
            type="radio"
            name="yesNoOption"
            id="noOption"
            value="No"
            readOnly
          />
          <label className="form-check-label" htmlFor="noOption">
            No
          </label>
        </div>
      </div> */}
      <div className="response-viewer-box-wrapper mb-3">
        <span>Yes</span>
      </div>
      <div className="response-viewer-box-wrapper">
        <span>No</span>
      </div>
    </div>
  );
};

export default YesNoViewer;
