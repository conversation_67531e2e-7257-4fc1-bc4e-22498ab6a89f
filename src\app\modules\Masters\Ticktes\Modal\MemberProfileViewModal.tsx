import React, { useEffect, useState } from "react";
import { Col, Modal, Row, Form, Card } from "react-bootstrap";
import { FaArrowLeft } from "react-icons/fa";
import { useNavigate } from "react-router";
import { userService } from "../../User/user.helper";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../common/SwalMessage";
import { ClipLoader } from "react-spinners";
import { SlCloudUpload } from "react-icons/sl";
import {
  formatContactInputNumber,
  getImage,
} from "../../../../utils/CommonUtils";

const MemberProfileViewModal = ({
  openMemberProfileViewModal,
  setOpenMemberProfileViewModal,
  ViewMemberId,
}: any) => {
  let keyinfo = JSON.parse(localStorage.keyinfo);
  const [userData, setUserData] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );

  console.devAPILog("ViewMemberId", ViewMemberId);
  console.devAPILog("userData", userData);
  const getSingleUser = (id: any) => {
    setIsLoading(true);
    userService
      .getSingleUser(id)
      .then((response: any) => {
        if (response.status == 200) {
          const resultData = response.data;
          if (resultData.success == true) {
            const decryptData = encryptDecryptUtil.decryptData(
              resultData.data,
              keyinfo.syckey
            );
            const decryptUserData = JSON.parse(decryptData);
            setUserData(decryptUserData?.userdata);
            setIsLoading(false);
          } else {
            SwalMessage(null, resultData?.errormsg, "Ok", "error", false);
          }
        }
      })
      .catch((error: any) => {
        if (error.response.status == 401) {
          localStorage.removeItem("islogin");
          navigate("/login");
          navigate(0);
        }
      });
  };
  useEffect(() => {
    if (openMemberProfileViewModal && ViewMemberId) {
      getSingleUser(ViewMemberId);
    }
  }, [openMemberProfileViewModal]);
  return (
    <>
      {isLoading && spinner}
      <Modal
        className="modal-right modal-right-small"
        scrollable={true}
        show={openMemberProfileViewModal}
        onHide={() => setOpenMemberProfileViewModal(false)}
      >
        <Modal.Header className="p-0 border-0">
          <Row className="align-items-baseline">
            <Col xs={10} className="mt-auto mb-auto">
              <h2 className="mb-0 ">
                <span className="me-5">
                  <FaArrowLeft
                    className="cursor-pointer"
                    onClick={() => {
                      setOpenMemberProfileViewModal(false);
                    }}
                  />
                </span>
                Members
              </h2>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="p-0">
          <Row className="justify-content-start mt-5">
            <Col xxl={6} xl={6} lg={6} sm={6} className="mb-5 text-start">
              <img
                className="TicketMemberView_profile_img"
                src={getImage(userData?.userimage)}
              ></img>
            </Col>
          </Row>
          <Row>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5 mt-5">
              <Form.Label>User Name</Form.Label>
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">
                    {userData ? userData?.username : ""}
                  </span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>Email ID</Form.Label>
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">{userData ? userData.email : ""}</span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>Phone</Form.Label>
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">
                    {formatContactInputNumber(
                      userData ? userData?.phonenumber : ""
                    )}
                  </span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>company Name</Form.Label>
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">{userData ? userData.company : ""}</span>
                </Card.Body>
              </Card>
            </Col>
            <Col xxl={12} xl={12} lg={12} sm={12} className="mb-5">
              <Form.Label>Status</Form.Label>
              <Card className="custom-card">
                <Card.Body className="px-3 py-4">
                  <span className="fs-5">{userData ? userData.active : ""}</span>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default MemberProfileViewModal;
