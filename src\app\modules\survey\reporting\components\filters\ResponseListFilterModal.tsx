import React, { useState, useEffect } from "react";
import { Col, Form } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import { ResponseListConfig, FilterState } from "../../types/chartTypes";
import KendoMultiSelect from "../../../../common/KendoMultiSelect";
import { DateRangePicker } from "@progress/kendo-react-dateinputs";
import surveyConfig from "../../data/surveyConfig.json";

interface ResponseListFilterData {
  property: any[];
  department: any[];
  status: any[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  score: {
    min: number | null;
    max: number | null;
  };
}

interface ResponseListFilterModalProps extends FilterModalProps {
  onFiltersChange?: (filters: FilterState) => void;
  currentFilters?: FilterState;
}

const ResponseListFilterModal: React.FC<ResponseListFilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentFilters = {},
  onFiltersChange
}) => {
  const config = surveyConfig.responseListConfig as ResponseListConfig;

  const [filterData, setFilterData] = useState<ResponseListFilterData>({
    property: [],
    department: [],
    status: [],
    dateRange: {
      start: null,
      end: null
    },
    score: {
      min: null,
      max: null
    }
  });

  const [tempFilterData, setTempFilterData] = useState<ResponseListFilterData>(filterData);

  // Get filter options from config
  const propertyOptions = config.filters.find(f => f.id === 'property')?.options || [];
  const departmentOptions = config.filters.find(f => f.id === 'department')?.options || [];
  const statusOptions = config.filters.find(f => f.id === 'status')?.options || [];

  useEffect(() => {
    if (currentFilters && Object.keys(currentFilters).length > 0) {
      const newFilterData = {
        property: Array.isArray(currentFilters.property) ? currentFilters.property :
                 currentFilters.property ? [currentFilters.property] : [],
        department: Array.isArray(currentFilters.department) ? currentFilters.department :
                   currentFilters.department ? [currentFilters.department] : [],
        status: Array.isArray(currentFilters.status) ? currentFilters.status :
               currentFilters.status ? [currentFilters.status] : [],
        dateRange: currentFilters.dateRange || { start: null, end: null },
        score: currentFilters.score || { min: null, max: null }
      };

      setFilterData(newFilterData);
      setTempFilterData(newFilterData);
    }
  }, [currentFilters, isOpen]);

  const handleApply = () => {
    setFilterData(tempFilterData);

    // Convert to the format expected by FullResponseList
    const convertedFilters: FilterState = {};

    if (tempFilterData.property.length > 0) {
      convertedFilters.property = tempFilterData.property.length === 1 ?
        tempFilterData.property[0].value : tempFilterData.property.map(p => p.value);
    }

    if (tempFilterData.department.length > 0) {
      convertedFilters.department = tempFilterData.department.length === 1 ?
        tempFilterData.department[0].value : tempFilterData.department.map(d => d.value);
    }

    if (tempFilterData.status.length > 0) {
      convertedFilters.status = tempFilterData.status.length === 1 ?
        tempFilterData.status[0].value : tempFilterData.status.map(s => s.value);
    }

    if (tempFilterData.dateRange.start || tempFilterData.dateRange.end) {
      convertedFilters.dateRange = tempFilterData.dateRange;
    }

    if (tempFilterData.score.min !== null || tempFilterData.score.max !== null) {
      convertedFilters.score = tempFilterData.score;
    }

    if (onFiltersChange) {
      onFiltersChange(convertedFilters);
    }

    if (onApply) {
      onApply(convertedFilters);
    }

    onClose();
  };

  const handleReset = () => {
    const resetData = {
      property: [],
      department: [],
      status: [],
      dateRange: { start: null, end: null },
      score: { min: null, max: null }
    };
    setTempFilterData(resetData);
    setFilterData(resetData);
  };

  const handlePropertyChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      property: selected || []
    }));
  };

  const handleDepartmentChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      department: selected || []
    }));
  };

  const handleStatusChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      status: selected || []
    }));
  };

  const handleDateRangeChange = (event: any) => {
    setTempFilterData(prev => ({
      ...prev,
      dateRange: {
        start: event.value.start,
        end: event.value.end
      }
    }));
  };

  const handleScoreChange = (field: 'min' | 'max', value: string) => {
    setTempFilterData(prev => ({
      ...prev,
      score: {
        ...prev.score,
        [field]: value ? parseFloat(value) : null
      }
    }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Filter"
      filterType="responseList"
      showApplyButton={true}
      showResetButton={true}
    >
      <Col sm={12} className="mb-3">
        <Form.Label>Property</Form.Label>
        <KendoMultiSelect
          dropdownData={propertyOptions}
          getData={tempFilterData.property}
          setData={handlePropertyChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Department</Form.Label>
        <KendoMultiSelect
          dropdownData={departmentOptions}
          getData={tempFilterData.department}
          setData={handleDepartmentChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Status</Form.Label>
        <KendoMultiSelect
          dropdownData={statusOptions}
          getData={tempFilterData.status}
          setData={handleStatusChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Date Range</Form.Label>
        <DateRangePicker
          value={{
            start: tempFilterData.dateRange.start,
            end: tempFilterData.dateRange.end
          }}
          onChange={handleDateRangeChange}
          className="form-control"
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Score Range</Form.Label>
        <div className="d-flex gap-2">
          <Form.Control
            type="number"
            placeholder="Min Score"
            value={tempFilterData.score.min || ''}
            onChange={(e) => handleScoreChange('min', e.target.value)}
            step="0.1"
            min="0"
            max="5"
          />
          <Form.Control
            type="number"
            placeholder="Max Score"
            value={tempFilterData.score.max || ''}
            onChange={(e) => handleScoreChange('max', e.target.value)}
            step="0.1"
            min="0"
            max="5"
          />
        </div>
      </Col>
    </BaseFilterModal>
  );
};

export default ResponseListFilterModal;