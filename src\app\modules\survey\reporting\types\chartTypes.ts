// TypeScript interfaces for the survey reporting chart system

export type ChartType = 'pie' | 'bar' | 'donut' | 'stackedBar' | 'line' | 'area';

// API Response Types for getSurveyReportingSummary (using actual API structure)
// Import the actual types from the API type definitions
import {
  SurveyReportingSummaryData,
  SurveyResponseQuestion,
  SurveyResponseAnswer
} from '../../../../apis/type';

// Actual API response structure (different from the types above)
export interface ActualApiAnswerResponseDto {
  answer: string;
  comment: string;
  count: number;
  percentage: number;
}

export interface ActualApiResponseDto {
  questionId: string;
  questinText: string; // Note: API has typo "questinText" instead of "questionText"
  responseType: string;
  totalAnswer: number;
  answerResponseDtos: ActualApiAnswerResponseDto[];
  branchingQuestion: ActualApiResponseDto | null;
}

export interface ActualApiSurveyReportingSummaryData {
  surveyId: string;
  responseCount: number;
  responseDtos: ActualApiResponseDto[];
}

// Transformed data types for table display
export interface SurveyResponseTableRow {
  questionId: string;
  questionText: string;
  responseType: string;
  totalAnswers: number;
  answers: {
    answer: string;
    comment: string;
    count: number;
    percentage: number;
  }[];
  branchingQuestion?: {
    questionId: string;
    questionText: string;
    responseType: string;
    totalAnswers: number;
    answers: {
      answer: string;
      comment: string;
      count: number;
      percentage: number;
    }[];
  };
}

export interface ChartData {
  id: string;
  questionId: string;
  questionNumber: number;
  title: string;
  type: ChartType;
  totalResponses: number;
  answeredResponses: number;
  data: ChartDataPoint[];
  averageScore?: number;
  category?: string;
  responseType: string;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  percentage: number;
  color: string;
  count?: number;
}

export interface ChartOption {
  type: ChartType;
  id?: string;
  category?: string;
  label: string;
  questionId?: string;
  questionText?: string;
}

export interface ChartFilter {
  id: string;
  name: string;
  type: 'chartType' | 'category' | 'responseType' | 'question';
  value: any;
  isActive: boolean;
}

export interface ChartContainerProps {
  activeTab: string;
  surveyId?: string;
  filters?: ChartFilter[];
}

export interface ChartComponentProps {
  chartData: ChartData;
  theme?: 'light' | 'dark';
  responsive?: boolean;
  showTitle?: boolean;
  showSubtitle?: boolean;
  height?: number;
  customOptions?: any;
}

export interface ChartContextType {
  // Chart selection
  selectedChartType: ChartType | null;
  setSelectedChartType: (type: ChartType | null) => void;

  // Chart options and data
  chartOptions: ChartOption[];
  selectedChartOption: ChartOption | null;
  setSelectedChartOption: (option: ChartOption | null) => void;

  // Chart data
  chartData: ChartData[];
  setChartData: (data: ChartData[]) => void;

  // Filters
  activeFilters: ChartFilter[];
  setActiveFilters: (filters: ChartFilter[]) => void;

  // Loading and error states
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;

  // Survey data
  surveyId: string | null;
  setSurveyId: (id: string | null) => void;
  loadSurveyData: (id: string) => Promise<void>;

  // Theme
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
}

export interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: any) => void;
  currentFilters?: any;
  filterType: 'global' | 'local' | 'property' | 'custom' | 'responseList';
}



// Response List Types
export interface SurveyResponse {
  responseId: string;
  submittedAt: string;
  respondentInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  answers: ResponseAnswer[];
  property?: string;
  department?: string;
  status?: string;
  score?: number;
}

export interface ResponseAnswer {
  questionId: string;
  questionText: string;
  answerText: string;
  answerValue: any;
  questionType: string;
}

export interface ResponseListProps {
  responses?: SurveyResponse[]; // Made optional since we'll get data from API
  isLoading?: boolean; // Made optional since component will manage its own loading state
  onSort: (field: string, direction: 'asc' | 'desc') => void;
  onFilter: (filters: any) => void;
  onExport: (options: ExportOptions) => void;
  // External filter and sort state
  externalFilters?: FilterState;
  externalSort?: SortState;
  // New props for API integration
  surveyId?: string; // Survey ID for API calls
  activeTab?: string; // Current active tab to enable lazy loading
}

// Enhanced Response List Types
export interface ResponseListColumn {
  key: string;
  label: string;
  sortable: boolean;
  filterable: boolean;
  type?: 'text' | 'date' | 'select' | 'range';
  visible?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  format?: 'text' | 'date' | 'currency' | 'percentage' | 'badge' | 'code';
  badgeVariant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
}

export interface ResponseListFilter {
  id: string;
  label: string;
  type: 'text' | 'select' | 'dateRange' | 'range';
  options?: Array<{ value: string; label: string }>;
  min?: number;
  max?: number;
}

export interface ResponseListConfig {
  columns: ResponseListColumn[];
  filters: ResponseListFilter[];
  pagination?: {
    enabled: boolean;
    pageSize: number;
    pageSizeOptions: number[];
  };
  export?: {
    enabled: boolean;
    formats: Array<'csv' | 'excel' | 'pdf'>;
  };
  search?: {
    enabled: boolean;
    placeholder: string;
    fields: string[];
  };
}






export interface FilterState {
  [key: string]: any;
}

export interface SortState {
  field: string;
  direction: 'asc' | 'desc';
}



// Export Types
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  includeCharts: boolean;
  includeRawData: boolean;
  fileName?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: FilterState;
  columns?: string[];
}





// API Response Types (extending the existing types)
export interface ApiChartData {
  questionId: string;
  questionText: string;
  questionNumber: number;
  responseType: string;
  totalResponses: number;
  answeredResponses: number;
  averageScore?: number;
  answers: Array<{
    answerId: string;
    answerText: string;
    responseCount: number;
    percentage: number;
  }>;
  branchingQuestions?: ApiChartData[];
}

export interface ApiSurveyData {
  surveyId: string;
  surveyName: string;
  totalResponses: number;
  questions: ApiChartData[];
  properties: Array<{
    propertyId: string;
    propertyName: string;
  }>;
  departments: Array<{
    departmentId: string;
    departmentName: string;
  }>;
  categories: string[];
  sentiments: Array<{
    label: string;
    value: string;
    emoji: string;
  }>;
}

// Utility type for chart generation
export interface ChartGenerationOptions {
  chartType: ChartType;
  questionData: ApiChartData;
  colorScheme?: 'default' | 'sentiment' | 'category';
  theme?: 'light' | 'dark';
  responsive?: boolean;
}
