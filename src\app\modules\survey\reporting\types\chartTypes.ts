// TypeScript interfaces for the survey reporting chart system

export type ChartType = 'pie' | 'bar' | 'donut' | 'stackedBar' | 'line' | 'area';

export interface ChartData {
  id: string;
  questionId: string;
  questionNumber: number;
  title: string;
  type: ChartType;
  totalResponses: number;
  answeredResponses: number;
  data: ChartDataPoint[];
  averageScore?: number;
  category?: string;
  responseType: string;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  percentage: number;
  color: string;
  count?: number;
}

export interface ChartOption {
  type: ChartType;
  id?: string;
  category?: string;
  label: string;
  questionId?: string;
  questionText?: string;
}

export interface ChartFilter {
  id: string;
  name: string;
  type: 'chartType' | 'category' | 'responseType' | 'question';
  value: any;
  isActive: boolean;
}

export interface ChartContainerProps {
  activeTab: string;
  surveyId?: string;
  filters?: ChartFilter[];
}

export interface ChartComponentProps {
  chartData: ChartData;
  theme?: 'light' | 'dark';
  responsive?: boolean;
  showTitle?: boolean;
  showSubtitle?: boolean;
  height?: number;
  customOptions?: any;
}

export interface ChartContextType {
  // Chart selection
  selectedChartType: ChartType | null;
  setSelectedChartType: (type: ChartType | null) => void;

  // Chart options and data
  chartOptions: ChartOption[];
  selectedChartOption: ChartOption | null;
  setSelectedChartOption: (option: ChartOption | null) => void;

  // Chart data
  chartData: ChartData[];
  setChartData: (data: ChartData[]) => void;

  // Filters
  activeFilters: ChartFilter[];
  setActiveFilters: (filters: ChartFilter[]) => void;

  // Loading and error states
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;

  // Survey data
  surveyId: string | null;
  setSurveyId: (id: string | null) => void;
  loadSurveyData: (id: string) => Promise<void>;

  // Theme
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
}

export interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: any) => void;
  currentFilters?: any;
  filterType: 'global' | 'local' | 'property' | 'custom';
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv';
  includeCharts: boolean;
  includeRawData: boolean;
  fileName?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Response List Types
export interface SurveyResponse {
  responseId: string;
  submittedAt: string;
  respondentInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  answers: ResponseAnswer[];
  property?: string;
  department?: string;
}

export interface ResponseAnswer {
  questionId: string;
  questionText: string;
  answerText: string;
  answerValue: any;
  questionType: string;
}

export interface ResponseListProps {
  responses: SurveyResponse[];
  isLoading: boolean;
  onSort: (field: string, direction: 'asc' | 'desc') => void;
  onFilter: (filters: any) => void;
  onExport: (options: ExportOptions) => void;
}

// API Response Types (extending the existing types)
export interface ApiChartData {
  questionId: string;
  questionText: string;
  questionNumber: number;
  responseType: string;
  totalResponses: number;
  answeredResponses: number;
  averageScore?: number;
  answers: Array<{
    answerId: string;
    answerText: string;
    responseCount: number;
    percentage: number;
  }>;
  branchingQuestions?: ApiChartData[];
}

export interface ApiSurveyData {
  surveyId: string;
  surveyName: string;
  totalResponses: number;
  questions: ApiChartData[];
  properties: Array<{
    propertyId: string;
    propertyName: string;
  }>;
  departments: Array<{
    departmentId: string;
    departmentName: string;
  }>;
  categories: string[];
  sentiments: Array<{
    label: string;
    value: string;
    emoji: string;
  }>;
}

// Utility type for chart generation
export interface ChartGenerationOptions {
  chartType: ChartType;
  questionData: ApiChartData;
  colorScheme?: 'default' | 'sentiment' | 'category';
  theme?: 'light' | 'dark';
  responsive?: boolean;
}
