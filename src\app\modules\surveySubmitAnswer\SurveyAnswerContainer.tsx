import React, { useState, useEffect } from "react";
import { ISurveyFullDetails, SurveyQuestionResponseDto } from "../../apis/type";
import { useSurveyAnswerState } from "./hooks/useSurveyAnswerState";
import "./SurveySubmitAnswer.css";
import SurveyQuestionStep from "./SurveyQuestionStep";
import SurveyNavigation from "./SurveyNavigation";
import SwalMessage from "../common/SwalMessage";
import surveyWelcome from "../../../efive_assets/images/surveyWelcome.jpg";

interface Props {
  surveyDetails: ISurveyFullDetails;
}

const SurveyAnswerContainer: React.FC<Props> = ({ surveyDetails }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isSurveySubmitted, setIsSurveySubmitted] = useState(false);
  const [isAttachmentUploading, setIsAttachmentUploading] = useState(false);
  const [showValidationError, setShowValidationError] = useState(false);
  const [validationErrorMessage, setValidationErrorMessage] =
    useState<string>("");
  const questions = surveyDetails.surveyQuestionResponseDtos || [];
  const totalQuestions = questions.length;

  const { answers, updateAnswer, isSubmitting, handleSubmitAnswer } =
    useSurveyAnswerState(surveyDetails.id);

  const handleFinalSubmit = async () => {
    console.log("Survey Submitted");

    // Check if there's a closing message to show
    if (
      surveyDetails.closingMessage &&
      surveyDetails.closingMessage.trim() !== ""
    ) {
      // Show closing message with SwalMessage (auto-close in 5 seconds)
      await SwalMessage(
        "Survey Completed!",
        surveyDetails.closingMessage,
        "OK", // This will trigger auto-close in 5 seconds
        "success",
        false // No cancel button
      );
      // Handle redirect after popup closes
      handleRedirect();
    } else {
      // No closing message, show default success message if there's a redirect URL
      if (
        surveyDetails.redirectUrl &&
        surveyDetails.redirectUrl.trim() !== ""
      ) {
        await SwalMessage(
          "Survey Completed!",
          "Thank you for completing the survey. You will be redirected shortly.",
          "OK",
          "success",
          false
        );
      }
      // Handle redirect
      handleRedirect();
    }
  };

  const handleRedirect = () => {
    // Check if there's a redirect URL
    if (surveyDetails.redirectUrl && surveyDetails.redirectUrl.trim() !== "") {
      try {
        // Add a small delay before redirect to ensure popup is closed
        setTimeout(() => {
          window.location.href = surveyDetails.redirectUrl;
        }, 500);
      } catch (error) {
        console.error("Error redirecting to URL:", error);
        // Fallback: try to open in new tab if direct redirect fails
        window.open(surveyDetails.redirectUrl, "_blank");
      }
    } else {
      // No redirect URL specified, could show a default message or stay on page
      console.log("Survey completed - no redirect URL specified");
    }
  };

  // Show welcome message if available
  const [showWelcome, setShowWelcome] = useState(
    !!surveyDetails.welcomeMessage
  );

  const currentQuestion = questions[currentQuestionIndex];

  // Validation function to check if current question is answered
  const validateCurrentQuestion = (): {
    isValid: boolean;
    errorMessage?: string;
  } => {
    if (!currentQuestion || !currentQuestion.isRequired) {
      return { isValid: true };
    }

    const currentAnswer = answers[currentQuestion.id];

    // Check if answer exists
    if (!currentAnswer) {
      return { isValid: false, errorMessage: "Answer is required" };
    }

    // For COMMENT type questions, validate the comment field
    if (currentQuestion.responseType === "COMMENT") {
      if (!currentAnswer.comment || currentAnswer.comment.trim() === "") {
        return { isValid: false, errorMessage: "Answer is required" };
      }
      return { isValid: true };
    }

    // For other question types, validate the answers array
    if (!currentAnswer.answers || currentAnswer.answers.length === 0) {
      return { isValid: false, errorMessage: "Answer is required" };
    }

    // Check if the first answer is empty
    const firstAnswer = currentAnswer.answers[0];
    if (
      !firstAnswer ||
      (typeof firstAnswer === "string" && firstAnswer.trim() === "")
    ) {
      return { isValid: false, errorMessage: "Answer is required" };
    }

    return { isValid: true };
  };

  const handleNext = async () => {
    if (currentQuestion) {
      // Validate current question before proceeding
      const validation = validateCurrentQuestion();

      if (!validation.isValid) {
        // Show validation error
        setShowValidationError(true);
        setValidationErrorMessage(
          validation.errorMessage || "Answer is required"
        );
        return;
      }

      // Submit current answer before moving to next
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);

      if (currentQuestionIndex < totalQuestions - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        // Reset validation state for new question
        setShowValidationError(false);
        setValidationErrorMessage("");
      }
    }
  };

  const handlePrevious = async () => {
    if (currentQuestion && currentQuestionIndex > 0) {
      // Submit current answer before moving to previous
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      // Reset validation state for new question
      setShowValidationError(false);
      setValidationErrorMessage("");
    }
  };

  const handleSubmit = async () => {
    if (currentQuestion) {
      // Validate current question before submitting
      const validation = validateCurrentQuestion();

      if (!validation.isValid) {
        // Show validation error
        setShowValidationError(true);
        setValidationErrorMessage(
          validation.errorMessage || "Answer is required"
        );
        return;
      }

      // Submit current answer
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);
      // Handle final submission
      await handleFinalSubmit();
      setIsSurveySubmitted(true);
    }
  };

  // Handle answer changes - hide validation error when user provides answer
  const handleAnswerUpdate = (answer: any) => {
    updateAnswer(currentQuestion.id, answer);

    // Hide validation error when user starts answering
    if (showValidationError) {
      setShowValidationError(false);
      setValidationErrorMessage("");
    }
  };

  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  if (showWelcome) {
    return (
      <div
        className="survey-welcome survey-bg"
        // style={
        //   surveyDetails.surveyImage
        //     ? {
        //         backgroundImage: `url(${surveyDetails.surveyImage})`,
        //       }
        //     : undefined
        // }
        style={
          surveyDetails.surveyImage
            ? ({
                "--survey-bg-image": `url(${surveyDetails.surveyImage})`,
              } as React.CSSProperties)
            : undefined
        }
      >
        <div className="h-100 d-flex justify-content-center align-items-center">
          <div className="text-center p-5">
            <img
              src={surveyWelcome}
              // src={surveyDetails.surveyImage}
              alt="Survey"
              className="mb-4 rounded-full"
              style={{ width: "200px", height: "200px", borderRadius: "50%" }}
            />
            <h2 className="mb-4">{surveyDetails.surveyName}</h2>
            <p className="mb-4">{surveyDetails.welcomeMessage}</p>
            <button
              className="btn btn-primary btn-lg"
              onClick={() => setShowWelcome(false)}
            >
              Start Survey
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (totalQuestions === 0) {
    return (
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="card">
              <div className="card-body text-center p-5">
                <h4>No Questions Available</h4>
                <p>This survey doesn't have any questions yet.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`survey-answer-container survey-bg`}
      style={
        surveyDetails.surveyImage
          ? ({
              "--survey-bg-image": `url(${surveyDetails.surveyImage})`,
            } as React.CSSProperties)
          : undefined
      }
    >
      {/* Sticky Header */}
      <div className="survey-sticky-header">
        <div className="progress mb-3">
          <div
            className="progress-bar"
            role="progressbar"
            style={{
              width: isSurveySubmitted
                ? "100%"
                : totalQuestions === 0
                ? "0%"
                : currentQuestionIndex === 0
                ? "0%"
                : `${((currentQuestionIndex + 1) / totalQuestions) * 100}%`,
            }}
            aria-valuenow={isSurveySubmitted
              ? 100
              : totalQuestions === 0
              ? 0
              : currentQuestionIndex === 0
              ? 0
              : ((currentQuestionIndex + 1) / totalQuestions) * 100}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </div>
        <div className="survey-header mb-4 no-margin-bottom">
          <h3 className="mb-2">{surveyDetails.surveyName}</h3>
        </div>
      </div>

      {/* Scrollable Question Area */}
      <div className="survey-question-area">
        <SurveyQuestionStep
          question={currentQuestion}
          questionNumber={currentQuestionIndex + 1}
          answer={answers[currentQuestion.id]}
          onAnswerChange={handleAnswerUpdate}
          onUploadingStateChange={setIsAttachmentUploading}
          showValidationError={showValidationError}
          validationErrorMessage={validationErrorMessage}
        />
      </div>

      {/* Sticky Footer Navigation */}
      <div className="survey-sticky-footer">
        <div>
          {showValidationError && validationErrorMessage && (
            <p
              className="text-danger text-center m-0"
              style={{ fontSize: "15px" }}
            >
              {validationErrorMessage}
            </p>
          )}
          <SurveyNavigation
            currentQuestion={currentQuestionIndex + 1}
            totalQuestions={totalQuestions}
            isFirstQuestion={isFirstQuestion}
            isLastQuestion={isLastQuestion}
            isSubmitting={isSubmitting}
            isAttachmentUploading={isAttachmentUploading}
            onPrevious={handlePrevious}
            onNext={handleNext}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    </div>
  );
};

export default SurveyAnswerContainer;
