import React, { useState, useEffect } from "react";
import { ISurveyFullDetails, SurveyQuestionResponseDto } from "../../apis/type";
import { useSurveyAnswerState } from "./hooks/useSurveyAnswerState";
import "./SurveySubmitAnswer.css";
import SurveyQuestionStep from "./SurveyQuestionStep";
import SurveyNavigation from "./SurveyNavigation";

interface Props {
  surveyDetails: ISurveyFullDetails;
}

const SurveyAnswerContainer: React.FC<Props> = ({ surveyDetails }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const questions = surveyDetails.surveyQuestionResponseDtos || [];
  const totalQuestions = questions.length;

  const {
    answers,
    updateAnswer,
    isSubmitting,
    handleSubmitAnswer,
    handleFinalSubmit,
  } = useSurveyAnswerState(surveyDetails.id);

  // Show welcome message if available
  const [showWelcome, setShowWelcome] = useState(
    !!surveyDetails.welcomeMessage
  );

  const currentQuestion = questions[currentQuestionIndex];

  const handleNext = async () => {
    if (currentQuestion) {
      // Submit current answer before moving to next
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);

      if (currentQuestionIndex < totalQuestions - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      }
    }
  };

  const handlePrevious = async () => {
    if (currentQuestion && currentQuestionIndex > 0) {
      // Submit current answer before moving to previous
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmit = async () => {
    if (currentQuestion) {
      // Submit current answer
      await handleSubmitAnswer(currentQuestion, currentQuestionIndex);
      // Handle final submission
      await handleFinalSubmit();
    }
  };

  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  if (showWelcome) {
    return (
      <div className="survey-welcome">
        <div className="card">
          <div className="card-body text-center p-5">
            {surveyDetails.surveyImage && (
              <img
                src={surveyDetails.surveyImage}
                alt="Survey"
                className="mb-4"
                style={{ maxWidth: "200px", height: "auto" }}
              />
            )}
            <h2 className="mb-4">{surveyDetails.surveyName}</h2>
            <p className="mb-4">{surveyDetails.welcomeMessage}</p>
            <button
              className="btn btn-primary btn-lg"
              onClick={() => setShowWelcome(false)}
            >
              Start Survey
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (totalQuestions === 0) {
    return (
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-md-8">
            <div className="card">
              <div className="card-body text-center p-5">
                <h4>No Questions Available</h4>
                <p>This survey doesn't have any questions yet.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-answer-container">
      <div className="card">
        <div className="card-body p-4">
          {/* Survey Header */}
          <div className="survey-header mb-4">
            <h3 className="mb-2">{surveyDetails.surveyName}</h3>
            <div className="progress mb-3">
              <div
                className="progress-bar"
                role="progressbar"
                style={{
                  width: `${
                    ((currentQuestionIndex + 1) / totalQuestions) * 100
                  }%`,
                }}
                aria-valuenow={
                  ((currentQuestionIndex + 1) / totalQuestions) * 100
                }
                aria-valuemin={0}
                aria-valuemax={100}
              />
            </div>
          </div>

          {/* Current Question */}
          <SurveyQuestionStep
            question={currentQuestion}
            questionNumber={currentQuestionIndex + 1}
            answer={answers[currentQuestion.id]}
            onAnswerChange={(answer) => {
              updateAnswer(currentQuestion.id, answer);
            }}
          />

          {/* Navigation */}
          <SurveyNavigation
            currentQuestion={currentQuestionIndex + 1}
            totalQuestions={totalQuestions}
            isFirstQuestion={isFirstQuestion}
            isLastQuestion={isLastQuestion}
            isSubmitting={isSubmitting}
            onPrevious={handlePrevious}
            onNext={handleNext}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    </div>
  );
};

export default SurveyAnswerContainer;
