import React, { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import PrimaryTabs from "../survey/reporting/components/PrimaryTabs";
import SecondaryTabs from "../survey/reporting/components/SecondaryTabs";
import FilterPanel from "../survey/reporting/components/FilterPanel";
import SummaryCard from "../survey/reporting/components/SummaryCard";
import ActionButtons from "../survey/reporting/components/ActionButtons";
import ChartContainer from "../survey/reporting/components/ChartContainer";
import FullResponseList from "../survey/reporting/components/FullResponseList";
import { ChartContextProvider } from "../survey/reporting/context/ChartContext";
import { SurveyDataProvider } from "../survey/reporting/context/SurveyDataContext";
import { ExportOptions } from "../survey/reporting/types/chartTypes";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import "../survey/survey.css";
import VIewTemplate from "../SurveyDetails/VIewTemplate";

const SurveyReportingPage: React.FC = () => {
  const { setLabels } = useBreadcrumbContext();
  const { surveyId } = useSurveyUtil();
  const [activeTab, setActiveTab] = useState("template");
  const [activeSubTab, setActiveSubTab] = useState("goose");

  useEffect(() => {
    setLabels([
      { path: "/surveys", state: {}, breadcrumb: "Survey" },
      { path: "", state: {}, breadcrumb: "View Template" },
    ]);
  }, [setLabels]);

  // Handler functions
  const handleExport = (options: ExportOptions) => {
    console.log("Export requested:", options);
    // Export functionality can be implemented here
    // This could integrate with the survey data or chart data for export
  };

  return (
    <SurveyDataProvider>
      <div className="h-100">
      {/* Top navigation and action buttons */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <PrimaryTabs activeTab={activeTab} onTabChange={setActiveTab} />
        <ActionButtons
          onExport={handleExport}
        />
      </div>
      
      {activeTab === "template" && <VIewTemplate />}

      {/* //TODO : Change Name to reporting for view */}
      {activeTab === "reporting" && (
        <>
          <ChartContextProvider>
            {/* Secondary tabs */}
            <div className="mb-4">
              <SecondaryTabs
                activeTab={activeSubTab}
                onTabChange={setActiveSubTab}
              />
            </div>

            {/* Filter panel */}
            <div className="mb-4">
              {(activeSubTab === "goose" || activeSubTab === "summary") && (
                <Card className="text-white p-4 rounded-5">
                  <>
                    {activeSubTab === "goose" && <FilterPanel />}

                    {/* Chart Container */}
                    <div className="mb-4">
                      <ChartContainer
                        activeTab={activeSubTab}
                        surveyId="demo-survey-id" // In real implementation, this would come from route params or props
                      />
                    </div>

                    {/* Content area - only show SummaryCard for goose tab */}
                    {activeSubTab === "goose" && (
                      <div className="mb-4">
                        <SummaryCard />
                      </div>
                    )}
                  </>
                </Card>
              )}

              {/* Full Response List Tab */}
              {activeSubTab === "responses" && (
                <div className="mb-4">
                  <FullResponseList
                    surveyId={surveyId}
                    activeTab={activeSubTab}
                  />
                </div>
              )}
            </div>

            {/* Save button */}
            <div
              className="position-fixed px-4 py-3"
              style={{
                bottom: 40,
                left: 0,
                right: 0,
                backgroundColor: "var(--Rx-bg)",
                zIndex: 9,
              }}
            >
              <div className="d-flex justify-content-end">
                <button className="btn rx-btn text-white">
                  Save & Continue
                </button>
              </div>
            </div>
          </ChartContextProvider>
        </>
      )}
      </div>
    </SurveyDataProvider>
  );
};

export default SurveyReportingPage;
