import React, { useEffect, useState } from "react";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import PrimaryTabs from "../survey/reporting/components/PrimaryTabs";
import ActionButtons from "../survey/reporting/components/ActionButtons";
import ReportingContent from "../survey/reporting/components/ReportingContent";
import { SurveyDataProvider } from "../survey/reporting/context/SurveyDataContext";
import { ExportOptions } from "../survey/reporting/types/chartTypes";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import "../survey/survey.css";
import VIewTemplate from "../SurveyDetails/VIewTemplate";

const SurveyReportingPage: React.FC = () => {
  const { setLabels } = useBreadcrumbContext();
  const { surveyId } = useSurveyUtil();
  const [activeTab, setActiveTab] = useState("template");
  const [activeSubTab, setActiveSubTab] = useState("responses");

  useEffect(() => {
    setLabels([
      { path: "/surveys", state: {}, breadcrumb: "Survey" },
      { path: "", state: {}, breadcrumb: "View Template" },
    ]);
  }, [setLabels]);

  // Handler functions
  const handleExport = (options: ExportOptions) => {
    console.log("Export requested:", options);
    // Export functionality can be implemented here
    // This could integrate with the survey data or chart data for export
  };

  return (
    <SurveyDataProvider>
      <div className="h-100">
      {/* Top navigation and action buttons */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <PrimaryTabs activeTab={activeTab} onTabChange={setActiveTab} />
        <ActionButtons
          onExport={handleExport}
        />
      </div>
      
      {activeTab === "template" && <VIewTemplate />}

      {/* Reporting Tab */}
      {activeTab === "reporting" && (
        <>
          <ReportingContent
            surveyId={surveyId}
            activeSubTab={activeSubTab}
            onSubTabChange={setActiveSubTab}
          />

          {/* Save button */}
          {/* <div
            className="position-fixed px-4 py-3"
            style={{
              bottom: 40,
              left: 0,
              right: 0,
              backgroundColor: "var(--Rx-bg)",
              zIndex: 9,
            }}
          >
            <div className="d-flex justify-content-end">
              <button className="btn rx-btn text-white">
                Save & Continue
              </button>
            </div>
          </div> */}
        </>
      )}
      </div>
    </SurveyDataProvider>
  );
};

export default SurveyReportingPage;
