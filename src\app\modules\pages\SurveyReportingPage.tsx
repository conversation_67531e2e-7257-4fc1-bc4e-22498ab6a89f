import React, { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import PrimaryTabs from "../survey/reporting/components/PrimaryTabs";
import SecondaryTabs from "../survey/reporting/components/SecondaryTabs";
import FilterPanel from "../survey/reporting/components/FilterPanel";
import SummaryCard from "../survey/reporting/components/SummaryCard";
import ActionButtons from "../survey/reporting/components/ActionButtons";
import ChartContainer from "../survey/reporting/components/ChartContainer";
import FullResponseList from "../survey/reporting/components/FullResponseList";
import { ChartContextProvider } from "../survey/reporting/context/ChartContext";
import {
  ExportOptions,
  SurveyResponse,
} from "../survey/reporting/types/chartTypes";
import "../survey/survey.css";
import VIewTemplate from "../SurveyDetails/VIewTemplate";

const SurveyReportingPage: React.FC = () => {
  const { setLabels } = useBreadcrumbContext();
  const [activeTab, setActiveTab] = useState("template");
  const [activeSubTab, setActiveSubTab] = useState("goose");
  const [currentFilters, setCurrentFilters] = useState<any>({});
  const [mockResponses] = useState<SurveyResponse[]>([
    {
      responseId: "resp_001",
      submittedAt: "2024-01-15T10:30:00Z",
      respondentInfo: {
        name: "John Doe",
        email: "<EMAIL>",
      },
      property: "Orthopedic Center 1",
      department: "Health",
      answers: [
        {
          questionId: "q1",
          questionText: "Do you agree with the company's new policy changes?",
          answerText: "Yes",
          answerValue: "yes",
          questionType: "yes_no",
        },
        {
          questionId: "q2",
          questionText: "Rate the service from 1 to 5?",
          answerText: "4",
          answerValue: 4,
          questionType: "rating",
        },
      ],
    },
    {
      responseId: "resp_002",
      submittedAt: "2024-01-14T14:20:00Z",
      respondentInfo: {
        name: "Jane Smith",
        email: "<EMAIL>",
      },
      property: "General Hospital",
      department: "Safety",
      answers: [
        {
          questionId: "q1",
          questionText: "Do you agree with the company's new policy changes?",
          answerText: "No",
          answerValue: "no",
          questionType: "yes_no",
        },
        {
          questionId: "q2",
          questionText: "Rate the service from 1 to 5?",
          answerText: "3",
          answerValue: 3,
          questionType: "rating",
        },
      ],
    },
  ]);

  useEffect(() => {
    setLabels([
      { path: "/surveys", state: {}, breadcrumb: "Survey" },
      { path: "", state: {}, breadcrumb: "View Template" },
    ]);
  }, [setLabels]);

  // Handler functions
  const handleFiltersChange = (filters: any) => {
    setCurrentFilters(filters);
  };

  const handleExport = (options: ExportOptions) => {
    console.log("Export requested:", options);
  };

  const handleSort = (field: string, direction: "asc" | "desc") => {
    console.log("Sort requested:", field, direction);
  };

  const handleFilter = (filters: any) => {
    console.log("Filter requested:", filters);
  };

  return (
    <div className="h-100">
      {/* Top navigation and action buttons */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <PrimaryTabs activeTab={activeTab} onTabChange={setActiveTab} />
        {/* //TODO : Active when feature complate */}
        {/* <ActionButtons
          onFiltersChange={handleFiltersChange}
          currentFilters={currentFilters}
          onExport={handleExport}
        /> */}
      </div>
      {activeTab === "template" && <VIewTemplate />}

      {/* //TODO : Change Name to reporting for view */}
      {activeTab === "reporting_1" && (
        <>
          <ChartContextProvider>
            {/* Secondary tabs */}
            <div className="mb-4">
              <SecondaryTabs
                activeTab={activeSubTab}
                onTabChange={setActiveSubTab}
              />
            </div>

            {/* Filter panel */}
            <div className="mb-4">
              {(activeSubTab === "goose" || activeSubTab === "summary") && (
                <Card className="text-white p-4 rounded-5">
                  <>
                    {activeSubTab === "goose" && <FilterPanel />}

                    {/* Chart Container */}
                    <div className="mb-4">
                      <ChartContainer
                        activeTab={activeSubTab}
                        surveyId="demo-survey-id" // In real implementation, this would come from route params or props
                      />
                    </div>

                    {/* Content area - only show SummaryCard for goose tab */}
                    {activeSubTab === "goose" && (
                      <div className="mb-4">
                        <SummaryCard />
                      </div>
                    )}
                  </>
                </Card>
              )}

              {/* Full Response List Tab */}
              {activeSubTab === "responses" && (
                <div className="mb-4">
                  <FullResponseList
                    responses={mockResponses}
                    isLoading={false}
                    onSort={handleSort}
                    onFilter={handleFilter}
                    onExport={handleExport}
                  />
                </div>
              )}
            </div>

            {/* Save button */}
            <div
              className="position-fixed px-4 py-3"
              style={{
                bottom: 40,
                left: 0,
                right: 0,
                backgroundColor: "var(--Rx-bg)",
                zIndex: 9,
              }}
            >
              <div className="d-flex justify-content-end">
                <button className="btn rx-btn text-white">
                  Save & Continue
                </button>
              </div>
            </div>
          </ChartContextProvider>
        </>
      )}
    </div>
  );
};

export default SurveyReportingPage;
