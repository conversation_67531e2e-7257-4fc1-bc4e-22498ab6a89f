import { <PERSON>, <PERSON><PERSON>, Row } from "react-bootstrap";
import React, { useEffect, useState } from "react";
import { ClipLoader } from "react-spinners";
import { RxCross2 } from "react-icons/rx";
import { hotworkspermitService } from "../HotWorkPermit.helper";
import SwalMessage from "../../../common/SwalMessage";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import { useNavigate } from "react-router";
import HotProgressBar from "../HotProgressBar";

const AddNewTemplateNameModal = ({
  openAddNewTemplateName,
  setOpenAddNewTemplateName,
  setHotWorkPerformAtModal,
  parentHwptId,
  setTemplateData,
  templateData,
  hwptid,
  setHwpid,
  step,
  totalSteps,
  onSave,
}: any) => {
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );
  const [templateName, setTemplateName] = useState("");
  const [isloading, setIsloading] = useState(false);
  const navigate = useNavigate();

  // disable button
  const [isDisabled, setIsDisabled] = useState(false);
  const validateFeild = () => {
    if (templateName) {
      setIsDisabled(true);
    } else {
      setIsDisabled(false);
    }
  };
  useEffect(() => {
    validateFeild();
  }, [templateName]);

  // empty feild on modal close
  useEffect(() => {
    if (templateData != "" && openAddNewTemplateName) {
      setTemplateName(templateData);
    } else {
      setTemplateName("");
    }
  }, [openAddNewTemplateName]);
  // save template name
  const handleSubmit = () => {
    if (templateName) {
      const payload = {
        hwptid: hwptid ? hwptid : null,
        templateName: templateName,
        parentHwptId: parentHwptId ? parentHwptId : null,
      };
      console.log("payload", payload);
      let keyinfo = JSON.parse(localStorage.keyinfo);
      setIsloading(true);
      hotworkspermitService
        .savetemplatename(payload)
        .then((response: any) => {
          const resultData = response.data;
          if (response.status === 200) {
            if (resultData?.success === true) {
              const decryptData = encryptDecryptUtil.decryptData(
                resultData.data,
                keyinfo.syckey
              );
              setIsloading(false);
              const parseData = JSON.parse(decryptData);
              setTemplateData(parseData?.displayData);
              console.log(parseData.displayData);

              setHwpid(parseData?.hwptid);
              SwalMessage(
                null,
                resultData?.errormsg,
                "Ok",
                "success",
                false
              ).then((isConfirmed) => {
                if (isConfirmed) {
                  setHotWorkPerformAtModal(true);
                  setOpenAddNewTemplateName(false);
                  if (onSave) {
                    onSave();
                  }
                }
              });
            } else {
              setIsloading(false);
              SwalMessage(null, resultData?.errormsg, "Ok", "error", false);
            }
          } else {
            if (response?.data?.status === 401) {
              setIsloading(false);
              localStorage.removeItem("islogin");
              navigate("/login");
              navigate(0);
            }
          }
        })
        .catch((error) => {
          if (error.response?.status == 401) {
            localStorage.removeItem("islogin");
            navigate("/login");
            navigate(0);
          }
          setIsloading(false);
          SwalMessage(
            null,
            error?.message,
            "Ok",
            "error",
            false
          );
        })
        .finally(() => setIsloading(false));
    } else {
      SwalMessage(
        null,
        "please fill required input field",
        "Ok",
        "error",
        false
      );
    }
  };

  return (
    <>
      {isloading && spinner}
      <Modal
        className="modal-right modal-right-small p-0"
        scrollable={true}
        show={openAddNewTemplateName}
        onHide={() => setOpenAddNewTemplateName(false)}>
        <Modal.Header className="border-0 p-0">
          <Row className="align-items-baseline">
            <Col xs={10} className="mt-auto mb-auto">
              <h2 className="mb-0">Add Template Name</h2>
            </Col>
            <Col xs={2} className="text-end mb-3">
              <span
                className="close-btn cursor-pointer"
                onClick={() => setOpenAddNewTemplateName(false)}>
                <RxCross2 fontSize={20} />
              </span>
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="mt-5 p-0">
          <Row>
            <Col sm={12} className="mt-3">
              <span className="form-label">
                Template Name<span className="text-danger">*</span>
              </span>
              <input
                type="text"
                className="form-control"
                placeholder="Enter Template Name"
                value={templateName}
                onChange={(e: any) => setTemplateName(e.target.value)}
              />
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer className="border-0 p-0 d-block">
          <Row className="align-items-center">
            <Col sm={6}>
              {isDisabled && (
                <HotProgressBar currentStep={step} totalSteps={totalSteps} />
              )}
            </Col>
            <Col sm={6} className="text-end">
              <button
                className="btn rx-btn"
                disabled={!isDisabled}
                onClick={handleSubmit}>
                Save & Continue
              </button>
            </Col>
          </Row>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default AddNewTemplateNameModal;
