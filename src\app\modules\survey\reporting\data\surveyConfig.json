{"properties": [{"label": "Orthopedic1", "value": "orthopedic1"}, {"label": "Orthopedic2", "value": "orthopedic2"}], "categories": ["Health", "Safety", "Procedures", "Operations", "Maintenance"], "sentiments": [{"label": "Positive", "value": "positive", "emoji": "🙂"}, {"label": "Neutral", "value": "neutral", "emoji": "😐"}, {"label": "Negative", "value": "negative", "emoji": "☹️"}], "gooseSummary": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl. <PERSON><PERSON><PERSON> auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.", "chartData": {"pieCharts": [{"id": 1, "questionNumber": 1, "title": "Do you agree with the company's new policy changes?", "totalResponses": 12, "answeredResponses": 12, "data": [{"name": "Yes", "value": 9, "percentage": 80, "color": "#4CAF50"}, {"name": "No", "value": 3, "percentage": 20, "color": "#F44336"}]}, {"id": 2, "questionNumber": 2, "title": "What's your favorite color?", "totalResponses": 12, "answeredResponses": 9, "data": [{"name": "Green", "value": 5, "percentage": 50, "color": "#4CAF50"}, {"name": "Red", "value": 2, "percentage": 15, "color": "#F44336"}, {"name": "Black", "value": 2, "percentage": 15, "color": "#212121"}, {"name": "White", "value": 1, "percentage": 10, "color": "#FFFFFF"}]}], "barCharts": [{"id": 3, "questionNumber": 3, "title": "Rate the service from 1 to 5?", "totalResponses": 12, "answeredResponses": 12, "averageScore": 4.2, "percentage": 80}, {"id": 4, "questionNumber": 4, "title": "How likely are you to recommend us?", "totalResponses": 15, "answeredResponses": 15, "averageScore": 3.8, "percentage": 76}], "stackBarCharts": [{"title": "Overall Satisfaction", "questionNumber": 2, "totalResponses": 12, "answeredResponses": 12, "segments": [{"name": "Good", "value": 75, "color": "#4CAF50"}, {"name": "Poor", "value": 5, "color": "#F44336"}, {"name": "Average", "value": 15, "color": "#FFC107"}, {"name": "Other", "value": 5, "color": "#616161"}]}], "categoryCharts": [{"id": 4, "questionNumber": 4, "category": "Health", "totalResponses": 12, "answeredResponses": 12, "data": [{"name": "Positive", "value": 9, "percentage": 60, "color": "#4CAF50"}, {"name": "Negative", "value": 3, "percentage": 20, "color": "#F44336"}, {"name": "Neutral", "value": 3, "percentage": 20, "color": "#FFC107"}]}, {"id": 5, "questionNumber": 5, "category": "Safety", "totalResponses": 12, "answeredResponses": 12, "data": [{"name": "Positive", "value": 9, "percentage": 60, "color": "#4CAF50"}, {"name": "Negative", "value": 3, "percentage": 20, "color": "#F44336"}, {"name": "Neutral", "value": 3, "percentage": 20, "color": "#FFC107"}]}]}, "responseListConfig": {"columns": [{"key": "responseId", "label": "Response ID", "sortable": true, "filterable": false}, {"key": "submittedAt", "label": "Date", "sortable": true, "filterable": true, "type": "date"}, {"key": "respondentName", "label": "Name", "sortable": true, "filterable": true, "type": "text"}, {"key": "property", "label": "Property", "sortable": true, "filterable": true, "type": "select"}, {"key": "department", "label": "Department", "sortable": true, "filterable": true, "type": "select"}, {"key": "status", "label": "Status", "sortable": true, "filterable": true, "type": "select"}, {"key": "score", "label": "Score", "sortable": true, "filterable": true, "type": "range"}], "filters": [{"id": "property", "label": "Property", "type": "select", "options": [{"value": "orthopedic1", "label": "Orthopedic Center 1"}, {"value": "orthopedic2", "label": "Orthopedic Center 2"}, {"value": "general_hospital", "label": "General Hospital"}, {"value": "clinic_north", "label": "North Clinic"}]}, {"id": "department", "label": "Department", "type": "select", "options": [{"value": "health", "label": "Health"}, {"value": "safety", "label": "Safety"}, {"value": "procedures", "label": "Procedures"}, {"value": "operations", "label": "Operations"}, {"value": "maintenance", "label": "Maintenance"}]}, {"id": "status", "label": "Status", "type": "select", "options": [{"value": "complete", "label": "Complete"}, {"value": "partial", "label": "Partial"}, {"value": "pending", "label": "Pending"}]}, {"id": "date<PERSON><PERSON><PERSON>", "label": "Date Range", "type": "date<PERSON><PERSON><PERSON>"}, {"id": "score", "label": "Score Range", "type": "range", "min": 1, "max": 5}]}, "responses": [{"responseId": "resp_001", "submittedAt": "2024-01-15T10:30:00Z", "respondentInfo": {"name": "<PERSON>", "email": "<EMAIL>"}, "property": "orthopedic1", "department": "health", "status": "complete", "score": 4.2, "answers": [{"questionId": "q1", "questionText": "Do you agree with the company's new policy changes?", "answerText": "Yes", "answerValue": "yes", "questionType": "yes_no"}, {"questionId": "q2", "questionText": "Rate the service from 1 to 5?", "answerText": "4", "answerValue": 4, "questionType": "rating"}]}, {"responseId": "resp_002", "submittedAt": "2024-01-14T14:20:00Z", "respondentInfo": {"name": "<PERSON>", "email": "<EMAIL>"}, "property": "general_hospital", "department": "safety", "status": "complete", "score": 3.8, "answers": [{"questionId": "q1", "questionText": "Do you agree with the company's new policy changes?", "answerText": "No", "answerValue": "no", "questionType": "yes_no"}, {"questionId": "q2", "questionText": "Rate the service from 1 to 5?", "answerText": "3", "answerValue": 3, "questionType": "rating"}]}, {"responseId": "resp_003", "submittedAt": "2024-01-13T09:15:00Z", "respondentInfo": {"name": "<PERSON>", "email": "<EMAIL>"}, "property": "orthopedic2", "department": "procedures", "status": "partial", "score": 4.5, "answers": [{"questionId": "q1", "questionText": "Do you agree with the company's new policy changes?", "answerText": "Yes", "answerValue": "yes", "questionType": "yes_no"}]}, {"responseId": "resp_004", "submittedAt": "2024-01-12T16:45:00Z", "respondentInfo": {"name": "<PERSON>", "email": "<EMAIL>"}, "property": "clinic_north", "department": "operations", "status": "complete", "score": 2.1, "answers": [{"questionId": "q1", "questionText": "Do you agree with the company's new policy changes?", "answerText": "No", "answerValue": "no", "questionType": "yes_no"}, {"questionId": "q2", "questionText": "Rate the service from 1 to 5?", "answerText": "2", "answerValue": 2, "questionType": "rating"}, {"questionId": "q3", "questionText": "How satisfied are you with our service?", "answerText": "Dissatisfied", "answerValue": "dissatisfied", "questionType": "satisfaction"}]}, {"responseId": "resp_005", "submittedAt": "2024-01-11T11:30:00Z", "respondentInfo": {"name": "<PERSON>", "email": "<EMAIL>"}, "property": "orthopedic1", "department": "maintenance", "status": "complete", "score": 4.8, "answers": [{"questionId": "q1", "questionText": "Do you agree with the company's new policy changes?", "answerText": "Yes", "answerValue": "yes", "questionType": "yes_no"}, {"questionId": "q2", "questionText": "Rate the service from 1 to 5?", "answerText": "5", "answerValue": 5, "questionType": "rating"}, {"questionId": "q3", "questionText": "How satisfied are you with our service?", "answerText": "Very Satisfied", "answerValue": "very_satisfied", "questionType": "satisfaction"}]}]}