import { useEffect, useState } from "react";
import { Card, Col, Dropdown, Modal, Row } from "react-bootstrap";
import { BsSave } from "react-icons/bs";
import { CiLocationOn, CiMail, CiPhone } from "react-icons/ci";
import { SlClose } from "react-icons/sl";
import { useNavigate } from "react-router";
import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import Spinner from "../../../../../common/Spinner";
import { FaRegEye } from "react-icons/fa";
import { IoMdMore } from "react-icons/io";
import { swalMessages } from "../../../../../../utils/CommonUtils";
import SwalMessage from "../../../../../common/SwalMessage";
import { escalationService } from "../escalation.helper";
import encryptDecryptUtil from "../../../../../../utils/encrypt-decrypt-util";

function LogsModal({
    openLogsModal,
    setOpenLogsModal,
    setGetModule,
    getModule,
    propertyId
}: any) {
    const userinfo = localStorage.userinfo
        ? JSON.parse(localStorage.userinfo)
        : {};
    const navigate = useNavigate();
    const [isLoading, setisLoading] = useState(false);
    const [isShowGrid, setisShowGrid] = useState(false);
    const [searchModule, setSearchModule] = useState("");
    const [searchUser, setSearchUser] = useState("");
    const [getTicketNo, setGetTicketNo] = useState('');
    const [escalationLogId, setEscalationLogId] = useState('')
    const [totalLogCount, setTotalLogCount] = useState<any>();
    const [totaluserCount, setTotalUsercount] = useState<any>()
    const [logGridData, setLogGridData] = useState<any[]>([])
    const [userGridData, setUserGridData] = useState<any>([])
    const initialSort: Array<any> = [
        { field: "module", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);

    const [activeTab, setactiveTab] = useState<any>("");
    const [refreshKey, setRefreshKey] = useState(0);
    const refreshGrid = (Tab: any) => {
        setRefreshKey((oldKey) => oldKey + 1);
        setactiveTab(Tab);
    };


    // Pagination start for logs
    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);

    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalLogCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
    };

    const itemPerPage: any = [
        {
            label: "5",
            value: 5,
        },
        {
            label: "10",
            value: 10,
        },
        {
            label: "15",
            value: 15,
        },
        {
            label: "All",
            value: totalLogCount,
        },
    ];
    //   Pagination end for logs

    // Pagination start for user
    const initialDataStateForUser: any = { skip: 0, take: 10 };
    const [userPage, setUserPage] = useState<any>(initialDataStateForUser);
    const [userPageSizeValue, setUserPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);

    const userPageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totaluserCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setUserPage({ skip, take: newTake });
        setUserPageSizeValue(newPageSizeValue);
    };

    const userItemPerPage: any = [
        {
            label: "5",
            value: 5,
        },
        {
            label: "10",
            value: 10,
        },
        {
            label: "15",
            value: 15,
        },
        {
            label: "All",
            value: totaluserCount,
        },
    ];
    //   Pagination end for user

    const handleShowUserGrid = (dataItem: any) => {
        setisShowGrid(true);
        setUserGridData([]);
        setSearchUser("");
        setUserPage(initialDataStateForUser);
        setGetTicketNo(dataItem?.moduleViewId);
        setEscalationLogId(dataItem?.escalationLogId);
        setRefreshKey((prev) => prev + 1);
    }
    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        // console.log("dataItem", dataItem)
        return (
            <td className='k-table-td cursor-pointer' onClick={() => handleShowUserGrid(dataItem)}>
                <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
            </td>
        );
    };

    const renderTooltipCell1 = (props: any) => {
        const { dataItem, field, content } = props;
        if (field == "isAcknowledged") {
            return (
                <td className='k-table-td text-center'>
                    <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
                </td>
            );
        } else {
            return (
                <td className='k-table-td'>
                    <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
                </td>
            );
        }
    };

    useEffect(() => {
        if (!openLogsModal) {
            setisShowGrid(false);
            setGetTicketNo('');
            setGetModule();
            setSearchModule('')
            setisShowGrid(false)
            setLogGridData([])
            setUserPage('')
            setEscalationLogId('')
            setUserPage(initialDataStateForUser)
            setPage(initialDataState)
            setPageSizeValue(initialDataState?.take)
            setUserPageSizeValue(initialDataStateForUser?.take)
        }
    }, [openLogsModal]);

    const getEscalationloggrid = async (pageNumber: any) => {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        setisLoading(true)
        try {
            const payload = {
                page: pageNumber,
                size: page.take,
                search: searchModule,
                sortingColumns: [
                    {
                        sortOrder: 0,
                        columnName: ""
                    }
                ],
                escalationId: getModule?.escalationId,
                propertyId: propertyId,
                departmentId: ""
            }
            console.log("payload--",payload)
            await escalationService.escalationloggrid(payload).then((response: any) => {
                if (response?.status == 200) {
                    const resultData = response?.data
                    if (resultData?.success) {
                        const decryptData = encryptDecryptUtil.decryptData(
                            resultData?.data,
                            keyinfo.syckey
                        )
                        const parseData = JSON.parse(decryptData)
                        setTotalLogCount(parseData?.totalCount)
                        setLogGridData(parseData?.data)
                    } else {
                        SwalMessage("error", resultData?.errormsg, "ok", "error", false)
                    }
                }
            })
        } catch (error: any) {
            SwalMessage("error", error?.message, "ok", "error", false)
        } finally {
            setisLoading(false)
        }
    }

    const getEscalationmodulelogs = async (pageNumber: any) => {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        setisLoading(true)
        try {
            const payload = {
                page: pageNumber,
                size: userPage.take,
                search: searchUser,
                sortingColumns: [
                    {
                        sortOrder: 0,
                        columnName: ""
                    }
                ],
                escalationLogId: escalationLogId
            }
            console.log("payload", payload)
            await escalationService.escalationmodulelogs(payload).then((response: any) => {
                if (response?.status == 200) {
                    const resultData = response?.data
                    if (resultData?.success) {
                        const decryptData = encryptDecryptUtil.decryptData(
                            resultData?.data,
                            keyinfo.syckey
                        )
                        const parseData = JSON.parse(decryptData)
                        console.log("parseData", parseData)
                        setTotalUsercount(parseData?.totalCount)
                        setUserGridData(parseData?.data)
                    } else {
                        SwalMessage("error", resultData?.errormsg, "ok", "error", false)
                    }
                }
            })
        } catch (error: any) {
            SwalMessage("error", error?.message, "ok", "error", false)
        } finally {
            setisLoading(false)
        }
    }

    useEffect(() => {
        if (openLogsModal && getModule?.escalationId) {
            const timeoutId = setTimeout(() => {
                const pageNumber = Math.floor(page.skip / page.take) + 1;
                getEscalationloggrid(pageNumber)
            }, 300);
            return () => {
                clearTimeout(timeoutId);
            }
        }
    }, [openLogsModal, searchModule, page])

    useEffect(() => {
        if (openLogsModal && getTicketNo && isShowGrid) {
            const timeoutId = setTimeout(() => {
                const pageNumber = Math.floor(userPage.skip / userPage.take) + 1;
                getEscalationmodulelogs(pageNumber)
            }, 300);
            return () => {
                clearTimeout(timeoutId);
            }
        }
    }, [openLogsModal, getTicketNo, searchUser, userPage])

    return (
        <>
            {isLoading && <Spinner />}
            <Modal
                className="modal-right-full"
                scrollable={true}
                show={openLogsModal}
            >
                <Modal.Header className="p-0 mb-4">
                    <Row>
                        <Col xs={6} className="mt-auto mb-auto">
                            <h2 className="mb-0">Logs</h2>
                        </Col>
                        <Col xs={6} className=" text-end mb-3">
                            {/* <button
                                type="button"
                                className={`btn me-6 mb-sm-0 mb-2 rx-btn`}
                            // onClick={handlesubmit}
                            >
                                <BsSave className="btn-icon-custom" />
                                save
                            </button> */}
                            <span
                                className="btn rx-btn "
                                onClick={() => setOpenLogsModal(false)}
                            >
                                <SlClose className="btn-icon-custom" />
                                Close
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="p-0">
                    <Row>
                        <Col xxl={6} xl={6} lg={12} sm={12} className="mb-3 mobile-margin">
                            <Card className="custom-card p-4">
                                <Row className="align-items-center">
                                    <Col xxl={6} xl={6} lg={6} md={6} sm={12} xs={12}>
                                        <span className="fs-6 me-5"><b>Module: {getModule?.module}</b></span>
                                        <span className="fs-6"><b>Priority: {getModule?.priority}</b></span>
                                    </Col>
                                    <Col xxl={6} xl={6} lg={6} md={6} sm={12} xs={12} className="text-end">
                                        <input
                                            type="text"
                                            className="form-control mb-3"
                                            placeholder="Search..."
                                            value={searchModule}
                                            onChange={(e) => setSearchModule(e.target.value)}
                                        />
                                    </Col>
                                </Row>
                                <div className='card mt-0'>
                                    <div className='card-body p-0'>
                                        <div className='table_div' style={{ width: '100%' }}>
                                            <Tooltip position="bottom" anchorElement="target">
                                                <Grid
                                                    data={orderBy(logGridData, sort)}
                                                    skip={page.skip}
                                                    take={page.take}
                                                    total={totalLogCount}
                                                    pageable={{
                                                        buttonCount: 2,
                                                        pageSizes: itemPerPage?.map((item: any) => item?.label),
                                                        pageSizeValue: pageSizeValue,
                                                    }}

                                                    onPageChange={pageChange}
                                                    sortable={true}
                                                    sort={sort}
                                                    onSortChange={(e: any) => {
                                                        setSort(e.sort);
                                                    }}
                                                >
                                                    <Column
                                                        title='Ticket / Inspection'
                                                        field='moduleViewId'
                                                        cell={(props) => renderTooltipCell({ ...props, content: props?.dataItem?.moduleViewId })}
                                                    />
                                                    <Column
                                                        title='Summery / Title'
                                                        field='title'
                                                        cell={(props) => renderTooltipCell({ ...props, content: props?.dataItem?.title })}
                                                    />
                                                    <Column
                                                        title='Acknowledgment Status'
                                                        field='isAcknowledged'
                                                        cell={(props) => renderTooltipCell({ ...props, content: props?.dataItem?.isAcknowledged })}
                                                    />
                                                </Grid>
                                            </Tooltip>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </Col>

                        <Col xxl={6} xl={6} lg={12} sm={12} className="mb-3 mobile-margin">
                            {isShowGrid == true ? (
                                <Card className="custom-card p-4">
                                    <Row className="align-items-center">
                                        <Col xxl={5} xl={5} lg={5} md={5} sm={12} xs={12}>
                                            <span className="fs-6 me-5"><b>Ticket No : {getTicketNo}</b></span>
                                        </Col>
                                        <Col xxl={6} xl={6} lg={6} md={6} sm={10} xs={10} className="text-end">
                                            <input
                                                type="text"
                                                className="form-control mb-3"
                                                placeholder="Search..."
                                                value={searchUser}
                                                onChange={(e) => setSearchUser(e.target.value)}
                                            />
                                        </Col>
                                        <Col xxl={1} xl={1} lg={1} md={1} sm={2} xs={2} className="text-end">
                                            <span className="cursor-pointer" onClick={() => { setisShowGrid(false), setGetTicketNo(""), setEscalationLogId(''), setUserGridData([]), setSearchUser("") }}><SlClose size={20} /></span>
                                        </Col>
                                    </Row>
                                    <div className='card mt-0'>
                                        <div className='card-body p-0'>
                                            <div className='table_div' style={{ width: '100%' }}>
                                                <Tooltip position="bottom" anchorElement="target">
                                                    <Grid
                                                        data={orderBy(userGridData, sort)}
                                                        skip={userPage.skip}
                                                        take={userPage.take}
                                                        total={totaluserCount}
                                                        pageable={{
                                                            buttonCount: 2,
                                                            pageSizes: userItemPerPage?.map((item: any) => item?.label),
                                                            pageSizeValue: userPageSizeValue,
                                                        }}

                                                        onPageChange={userPageChange}
                                                        sortable={true}
                                                        sort={sort}
                                                        onSortChange={(e: any) => {
                                                            setSort(e.sort);
                                                        }}
                                                    >
                                                        <Column
                                                            title='User'
                                                            field='userName'
                                                            cell={(props) => renderTooltipCell1({ ...props, content: props?.dataItem?.userName })}
                                                        />
                                                        <Column
                                                            title='Notification'
                                                            field='notificationType'
                                                            cell={(props) => renderTooltipCell1({ ...props, content: props?.dataItem?.notificationType })}
                                                        />
                                                        <Column
                                                            title='Time'
                                                            field='sendTime'
                                                            cell={(props) => renderTooltipCell1({ ...props, content: props?.dataItem?.sendTime })}
                                                        />
                                                        <Column
                                                            title='Acknowledged'
                                                            field='isAcknowledged'
                                                            cell={(props) => renderTooltipCell1({ ...props, content: props?.dataItem?.isAcknowledged })}
                                                        />
                                                        <Column
                                                            title='Acknowledged Time'
                                                            field='isAcknowledgedTime'
                                                            cell={(props) => renderTooltipCell1({ ...props, content: props?.dataItem?.isAcknowledgedTime })}
                                                        />
                                                    </Grid>
                                                </Tooltip>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            ) : null}
                        </Col>
                    </Row>
                </Modal.Body>
            </Modal >
        </>
    );
}

export default LogsModal;
