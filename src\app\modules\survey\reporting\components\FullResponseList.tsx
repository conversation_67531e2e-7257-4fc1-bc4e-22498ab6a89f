import React, { useEffect, useMemo } from "react";
import { <PERSON>, Spinner, Alert } from "react-bootstrap";
import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import { useSurveyData } from "../context/SurveyDataContext";

interface FullResponseListProps {
  surveyId?: string;
  activeTab?: string;
}

const FullResponseList: React.FC<FullResponseListProps> = ({
  surveyId,
  activeTab,
}) => {
  // Use the survey data context
  const { tableData, isLoading, error, fetchSurveyData } = useSurveyData();

  // API call effect - only call when tab is active and surveyId is available
  // Added detailed logging to detect infinite loops and cleanup function
  useEffect(() => {

    let isMounted = true;

    if (activeTab === "responses" && surveyId && isMounted) {
      fetchSurveyData(surveyId);
    } else {
      // console.log("❌ Conditions not met - skipping API call");
    }

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
    };
  }, [activeTab, surveyId, fetchSurveyData]);

  // Memoize grid data to prevent unnecessary re-renders
  const gridData = useMemo(() => {
    return orderBy(tableData, [{ field: "questionText", dir: "asc" }]);
  }, [tableData]);

  // Custom cell renderers following SurveyListPage pattern
  const renderQuestion = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <div className="fw-medium" style={{ lineHeight: "1.4" }}>
          {content.questionText}
        </div>
      </td>
    );
  };

  const renderResponseType = (props: any) => {
    const content = props.dataItem;
    const responseType = content.responseType || "UNKNOWN";

    const getResponseTypeBadge = (type: string) => {
      const badgeClasses = {
        SCALE: "bg-primary",
        RATING: "bg-warning text-dark",
        MULTIPLE_CHOICE: "bg-success",
        YES_NO: "bg-info",
        TEXT: "bg-secondary",
      };
      return badgeClasses[type as keyof typeof badgeClasses] || "bg-secondary";
    };

    const displayText = responseType.replace(/_/g, " ");
    const badgeClass = getResponseTypeBadge(responseType);

    return (
      <td className="k-table-td">
        <div className="d-flex justify-content-center">
          <span className={`badge ${badgeClass} text-white`}>
            {displayText}
          </span>
        </div>
      </td>
    );
  };

  const renderTotalAnswers = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <div className="fw-bold text-center fs-6">{content.totalAnswers}</div>
      </td>
    );
  };

  const renderResponseSummary = (props: any) => {
    const content = props.dataItem;
    const answers = content.answers || [];

    if (answers.length === 0) {
      return (
        <td className="k-table-td">
          <div className="text-muted fst-italic d-flex align-items-center">
            <i className="fas fa-info-circle me-1"></i>
            No responses
          </div>
        </td>
      );
    }

    return (
      <td className="k-table-td">
        <div style={{ fontSize: "0.875rem" }}>
          <table
            className="table table-sm table-borderless mb-0"
            style={{ fontSize: "0.8rem" }}
          >
            <thead>
              <tr>
                <th
                  className="p-1 border-bottom"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  Answer
                </th>
                <th
                  className="p-1 border-bottom text-center"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  Count
                </th>
                <th
                  className="p-1 border-bottom text-center"
                  style={{ fontSize: "0.75rem", fontWeight: "600" }}
                >
                  %
                </th>
              </tr>
            </thead>
            <tbody>
              {answers
                .slice(0, 3)
                .map(
                  (
                    answer: {
                      answer: string;
                      count: number;
                      percentage: number;
                    },
                    index: number
                  ) => {
                    const displayAnswer = answer.answer || "No answer text";
                    const displayCount = answer.count || 0;
                    const displayPercentage = answer.percentage || 0;

                    return (
                      <tr key={index}>
                        <td
                          className="p-1"
                          style={{
                            maxWidth: "120px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                          }}
                          title={displayAnswer}
                        >
                          {displayAnswer}
                        </td>
                        <td className="p-1 text-center fw-medium">
                          {displayCount}
                        </td>
                        <td className="p-1 text-center">
                          {displayPercentage === 100
                            ? "100"
                            : displayPercentage.toFixed(1)}
                          %
                        </td>
                      </tr>
                    );
                  }
                )}
            </tbody>
          </table>
          {answers.length > 3 && (
            <div className="text-muted small mt-1 text-center border-top pt-1">
              <i className="fas fa-plus-circle me-1"></i>+{answers.length - 3}{" "}
              more response{answers.length - 3 !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </td>
    );
  };
  if (tableData.length > 0) {
    console.log("🔍 Sample tableData structure:", {
      questionId: tableData[0].questionId,
      questionText: tableData[0].questionText,
      responseType: tableData[0].responseType,
      totalAnswers: tableData[0].totalAnswers,
      answersCount: tableData[0].answers?.length,
      sampleAnswer: tableData[0].answers?.[0],
    });
  }

  // Show loading state
  if (isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "200px" }}
        >
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading survey data...</span>
          </Spinner>
        </div>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <Alert variant="danger">
          <Alert.Heading>Error loading survey data</Alert.Heading>
          <p>{error}</p>
        </Alert>
      </Card>
    );
  }

  // Show empty state when no data is available
  if (tableData.length === 0 && !isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4">
        <Alert variant="info">
          <Alert.Heading>
            <i className="fas fa-chart-bar me-2"></i>
            No survey data available
          </Alert.Heading>
          <p className="mb-0">
            {activeTab === "responses" && surveyId
              ? "No survey responses found for this survey. Responses will appear here once participants submit their answers."
              : "Please select a survey to view response data."}
          </p>
        </Alert>
      </Card>
    );
  }

  return (
    <div>
      <div className="row pageheader mb-7">
        <div className="col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto">
          <h5 className="mb-0 text-dark">
            <i className="fas fa-table me-2"></i>
            Survey Questions ({tableData.length}{" "}
            {tableData.length === 1 ? "question" : "questions"})
          </h5>
        </div>
        <div className="col-xl-6 col-lg-6 col-sm-6 text-end">
          <div className="text-muted small">
            Total responses across all questions
          </div>
        </div>
      </div>

      <div className="card mt-0">
        <div className="card-body p-0" style={{ margin: "0.8px" }}>
          <div className="table_div" style={{ width: "100%" }}>
            <Tooltip position="bottom" anchorElement="target">
              <Grid
                data={gridData}
                sortable={true}
                pageable={{
                  buttonCount: 4,
                  pageSizes: [10, 25, 50, "All"],
                  pageSizeValue: 10,
                }}
              >
                <Column
                  title="Question"
                  field="questionText"
                  width="300px"
                  cell={(props) => renderQuestion({ ...props })}
                />
                <Column
                  title="Response Type"
                  field="responseType"
                  width="140px"
                  cell={(props) => renderResponseType({ ...props })}
                />
                <Column
                  title="Total Answers"
                  field="totalAnswers"
                  width="140px"
                  cell={(props) => renderTotalAnswers({ ...props })}
                />
                <Column
                  title="Response Summary"
                  field="answers"
                  width="300px"
                  cell={(props) => renderResponseSummary({ ...props })}
                />
              </Grid>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FullResponseList;
