import React, { useState, useEffect } from "react";
import { <PERSON>, Table, Spinner, Alert, Form, Row, Col, Button } from "react-bootstrap";
import { ResponseListProps, SurveyResponse, ExportOptions } from "../types/chartTypes";
import { ExportService } from "../services/exportService";
import { useChartContext } from "../context/ChartContext";

const FullResponseList: React.FC<ResponseListProps> = ({
  responses,
  isLoading,
  onSort,
  onFilter,
  onExport
}) => {
  const { chartData } = useChartContext();
  const [sortField, setSortField] = useState<string>('submittedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredResponses, setFilteredResponses] = useState<SurveyResponse[]>(responses);

  // Filter responses based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredResponses(responses);
    } else {
      const filtered = responses.filter(response => 
        response.answers.some(answer => 
          answer.questionText.toLowerCase().includes(searchTerm.toLowerCase()) ||
          answer.answerText.toLowerCase().includes(searchTerm.toLowerCase())
        ) ||
        response.respondentInfo?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        response.property?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        response.department?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredResponses(filtered);
    }
  }, [searchTerm, responses]);

  const handleSort = (field: string) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);
    onSort(field, newDirection);
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    const exportOptions: ExportOptions = {
      format,
      includeCharts: false,
      includeRawData: true,
      fileName: `survey-responses.${format === 'excel' ? 'xlsx' : format}`
    };

    try {
      if (format === 'excel') {
        await ExportService.exportToExcel(chartData, filteredResponses, exportOptions);
      } else if (format === 'csv') {
        await ExportService.exportToCSV(chartData, filteredResponses, exportOptions);
      } else {
        await ExportService.exportToPDF(exportOptions);
      }
      
      if (onExport) {
        onExport(exportOptions);
      }
    } catch (error) {
      console.error(`Error exporting to ${format}:`, error);
    }
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <Card className="p-4 rounded-3 mb-4" style={{ backgroundColor: "var(--card-bg-body)" }}>
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "200px" }}>
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading responses...</span>
          </Spinner>
        </div>
      </Card>
    );
  }

  if (!responses || responses.length === 0) {
    return (
      <Card className="p-4 rounded-3 mb-4" style={{ backgroundColor: "var(--card-bg-body)" }}>
        <Alert variant="info">
          <Alert.Heading>No responses available</Alert.Heading>
          <p>There are no survey responses to display at this time.</p>
        </Alert>
      </Card>
    );
  }

  return (
    <Card className="p-4 rounded-3 mb-4" style={{ backgroundColor: "var(--card-bg-body)" }}>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h5 className="mb-0">Full Response List ({filteredResponses.length} responses)</h5>
        
        <div className="d-flex gap-2">
          <Button 
            variant="outline-primary" 
            size="sm" 
            onClick={() => handleExport('csv')}
          >
            Export CSV
          </Button>
          <Button 
            variant="outline-primary" 
            size="sm" 
            onClick={() => handleExport('excel')}
          >
            Export Excel
          </Button>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <Row className="mb-3">
        <Col md={6}>
          <Form.Control
            type="text"
            placeholder="Search responses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </Col>
        <Col md={6} className="text-end">
          <small className="text-muted">
            Showing {filteredResponses.length} of {responses.length} responses
          </small>
        </Col>
      </Row>

      {/* Responses Table */}
      <div className="table-responsive">
        <Table hover className="mb-0">
          <thead className="table-light">
            <tr>
              <th 
                className="cursor-pointer user-select-none"
                onClick={() => handleSort('responseId')}
              >
                Response ID {getSortIcon('responseId')}
              </th>
              <th 
                className="cursor-pointer user-select-none"
                onClick={() => handleSort('submittedAt')}
              >
                Submitted {getSortIcon('submittedAt')}
              </th>
              <th 
                className="cursor-pointer user-select-none"
                onClick={() => handleSort('respondentInfo.name')}
              >
                Respondent {getSortIcon('respondentInfo.name')}
              </th>
              <th>Property</th>
              <th>Department</th>
              <th>Responses</th>
            </tr>
          </thead>
          <tbody>
            {filteredResponses.map((response) => (
              <tr key={response.responseId}>
                <td>
                  <code className="text-muted">{response.responseId.substring(0, 8)}...</code>
                </td>
                <td>{formatDate(response.submittedAt)}</td>
                <td>
                  <div>
                    <div className="fw-medium">
                      {response.respondentInfo?.name || 'Anonymous'}
                    </div>
                    {response.respondentInfo?.email && (
                      <small className="text-muted">{response.respondentInfo.email}</small>
                    )}
                  </div>
                </td>
                <td>
                  <span className="badge bg-secondary">
                    {response.property || 'N/A'}
                  </span>
                </td>
                <td>
                  <span className="badge bg-info">
                    {response.department || 'N/A'}
                  </span>
                </td>
                <td>
                  <div className="response-summary">
                    {response.answers.slice(0, 2).map((answer, index) => (
                      <div key={index} className="mb-1">
                        <small className="text-muted d-block">
                          Q: {answer.questionText.substring(0, 50)}
                          {answer.questionText.length > 50 ? '...' : ''}
                        </small>
                        <small className="fw-medium">
                          A: {answer.answerText.substring(0, 100)}
                          {answer.answerText.length > 100 ? '...' : ''}
                        </small>
                      </div>
                    ))}
                    {response.answers.length > 2 && (
                      <small className="text-muted">
                        +{response.answers.length - 2} more responses
                      </small>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </div>

      {filteredResponses.length === 0 && searchTerm && (
        <div className="text-center py-4">
          <Alert variant="warning">
            <Alert.Heading>No matching responses</Alert.Heading>
            <p>No responses match your search criteria. Try adjusting your search terms.</p>
            <Button 
              variant="outline-warning" 
              size="sm" 
              onClick={() => setSearchTerm('')}
            >
              Clear Search
            </Button>
          </Alert>
        </div>
      )}
    </Card>
  );
};

export default FullResponseList;
