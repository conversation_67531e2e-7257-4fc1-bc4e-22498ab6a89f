import { useState, useCallback } from "react";
import { v4 as uuidv4 } from "uuid";
import axiosInstance from "../../serverconfig/axiosInstance";
import { APIs } from "../../serverconfig/apiURLs";
import { getDecryptData } from "../../utils/CommonUtils";
import { getFileType } from "../../utils/helper";

interface UploadedAttachment {
  id: string;
  url: string;
  type: string;
  name?: string;
  previewUrl?: string;
  isUploading?: boolean;
}

interface LoadingFile {
  id: string;
  loading: boolean;
  file: File;
}

export const useFileUpload = () => {
  const [loadingFiles, setLoadingFiles] = useState<LoadingFile[]>([]);
  const [uploadedAttachments, setUploadedAttachments] = useState<
    UploadedAttachment[]
  >([]);

  const uploadFile = useCallback(
    async (file: File, fileId: string): Promise<UploadedAttachment | null> => {
      setLoadingFiles((prev) => [...prev, { id: fileId, loading: true, file }]);

      const previewUrl = URL.createObjectURL(file);

      try {
        const formData = new FormData();
        formData.append("files", file);

        const response = await axiosInstance.post(
          APIs.MULTIPART.FILE_UPLOAD,
          formData
        );
        const resData = response?.data;

        if (resData) {
          const decryptedData = getDecryptData(resData.data);

          return {
            id: fileId,
            url: decryptedData?.documenturl,
            type: getFileType(file.type),
            name: file.name,
            previewUrl,
            isUploading: false,
          };
        }
      } catch (error) {
        console.error(`File upload failed for ${file.name}:`, error);
      } finally {
        setLoadingFiles((prev) =>
          prev.map((item) =>
            item.id === fileId ? { ...item, loading: false } : item
          )
        );
      }

      return null;
    },
    []
  );

  const processAndUploadFiles = useCallback(
    async (files: FileList | File[]) => {
      const filesArray = Array.isArray(files) ? files : Array.from(files);

      const tempPreviews: UploadedAttachment[] = filesArray.map((file) => ({
        id: uuidv4(),
        url: "",
        type: getFileType(file.type),
        name: file.name,
        previewUrl: URL.createObjectURL(file),
        isUploading: true,
      }));

      setUploadedAttachments((prev) => [...prev, ...tempPreviews]);

      const finalUploaded: UploadedAttachment[] = [];

      for (const [index, file] of filesArray.entries()) {
        const fileId = tempPreviews[index].id;
        const uploaded = await uploadFile(file, fileId);

        if (uploaded) {
          const uploadedWithId = { ...uploaded, id: fileId };

          finalUploaded.push(uploadedWithId);

          setUploadedAttachments((prev) =>
            prev.map((item) => (item.id === fileId ? uploadedWithId : item))
          );
        }
      }

      return finalUploaded;
    },
    [uploadFile]
  );

  const removeAttachment = (id: string): UploadedAttachment[] => {

    let updatedList: UploadedAttachment[] = [];

    setUploadedAttachments((prev) => {
      const filtered = prev.filter((item) => item.id !== id);
      updatedList = filtered;
      return filtered;
    });

    return updatedList;
  };

  const clearAllAttachments = useCallback(() => {
    setUploadedAttachments([]);
    setLoadingFiles([]);
  }, []);

  return {
    loadingFiles,
    uploadedAttachments,
    uploadFile,
    processAndUploadFiles,
    removeAttachment,
    clearAllAttachments,
  };
};
