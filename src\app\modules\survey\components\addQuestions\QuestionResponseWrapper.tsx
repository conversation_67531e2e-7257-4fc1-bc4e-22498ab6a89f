import React from "react";
import FormLabel from "../../../../component/form/FormLabel";
import QuestionAttchmentInput from "./QuestionAttchmentInput";
import CommentResponse from "../response/CommentResponse";

interface QuestionResponseWrapperProps {
  children: React.ReactNode;
  questionText: string;
  isRequired?: boolean;
  allowAttachment?: boolean;
  allowedAttachmentType?: string[];
  attachments?: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>;
  onAttachmentsChange?: (
    attachments: Array<{
      attachmentUrl: string;
      attachmentType: string;
      attachmentId: string;
    }>
  ) => void;
  allowComment?: boolean;
  comment?: string;
  onCommentChange?: (comment: string) => void;
  allowBranching?: boolean;
  branchingQuestionText?: string;
  allowbranchingAttchement?: boolean;
  branchingAttchementType?: string[];
  branchingAttachments?: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>;
  onBranchingAttachmentsChange?: (
    attachments: Array<{
      attachmentUrl: string;
      attachmentType: string;
      attachmentId: string;
    }>
  ) => void;
  questionNumber?: number;
  readOnly?: boolean;
  id?: string;
  onUploadingStateChange?: (isUploading: boolean) => void;
  onBranchingUploadingStateChange?: (isUploading: boolean) => void;
}

const QuestionResponseWrapper: React.FC<QuestionResponseWrapperProps> = ({
  questionText,
  isRequired = false,
  allowAttachment = false,
  allowedAttachmentType = [],
  attachments = [],
  onAttachmentsChange,
  allowComment = false,
  comment = "",
  onCommentChange,
  allowBranching = false,
  branchingQuestionText = "",
  allowbranchingAttchement = false,
  branchingAttchementType = [],
  branchingAttachments = [],
  onBranchingAttachmentsChange,
  children,
  questionNumber,
  readOnly = false,
  id,
  onUploadingStateChange,
  onBranchingUploadingStateChange,
}) => {
  return (
    <div className="question-response-wrapper">
      <div className="d-flex align-items-start">
        <FormLabel required={isRequired}>{`${
          questionNumber ? questionNumber + ")" : ""
        } ${questionText}`}</FormLabel>
      </div>
      <div className="">{children}</div>

      {allowAttachment && (
        <div className="">
          <QuestionAttchmentInput
            attachments={attachments}
            allowedAttachmentTypes={allowedAttachmentType}
            onAttachmentsChange={onAttachmentsChange}
            disabled={readOnly}
            id={"survey-question-attachment-input" + id}
            onUploadingStateChange={onUploadingStateChange}
          />
        </div>
      )}

      {allowComment && (
        <div className="mt-5">
          <FormLabel required={false}>Add Comment</FormLabel>
          <textarea
            className="form-control"
            value={comment}
            onChange={(e) => onCommentChange?.(e.target.value)}
            readOnly={readOnly}
            placeholder="Add your comment..."
            rows={3}
          />
        </div>
      )}
      {allowBranching && branchingQuestionText && (
        <div className="">
          <hr className="opacity-5" />
          <FormLabel required={false}>{branchingQuestionText}</FormLabel>
          <CommentResponse value={comment} onChange={() => {}} />
          {allowbranchingAttchement && (
            <div className="mt-5">
              <FormLabel required={false}>Branching Attachment</FormLabel>
              <QuestionAttchmentInput
                attachments={branchingAttachments}
                allowedAttachmentTypes={branchingAttchementType}
                onAttachmentsChange={onBranchingAttachmentsChange}
                disabled={readOnly}
                id={"branching-attachment-input" + id}
                onUploadingStateChange={onBranchingUploadingStateChange}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionResponseWrapper;
