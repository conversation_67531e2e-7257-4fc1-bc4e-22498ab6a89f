import React, { useState } from "react";
import { Card, Row, Col } from "react-bootstrap";
import RxReactSelect from "../../../../component/RxReactSelect";
import surveyConfig from "../data/surveyConfig.json";

const FilterPanel: React.FC = () => {
  const [selectedProperties, setSelectedProperties] = useState<any[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedSentiment, setSelectedSentiment] = useState<string | null>(
    null
  );

  const handlePropertyChange = (selected: any) => {
    setSelectedProperties(selected || []);
  };

  const handleCategoryClick = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(selectedCategories.filter((c) => c !== category));
    } else {
      setSelectedCategories([...selectedCategories, category]);
    }
  };

  const handleSentimentClick = (sentiment: string) => {
    setSelectedSentiment(selectedSentiment === sentiment ? null : sentiment);
  };

  return (
    <Row className="g-4">
      <Col md={12}>
        <div className="mb-2" style={{ color: "var(--chart-text)" }}>Property</div>
        <RxReactSelect
          isMulti
          options={surveyConfig.properties}
          value={selectedProperties}
          onChange={handlePropertyChange}
          placeholder="Select properties"
          className="property-select"
        />
      </Col>

      <Col md={12}>
        <div className="mb-2" style={{ color: "var(--chart-text)" }}>Survey Categorization</div>
        <div className="d-flex flex-wrap gap-2">
          {surveyConfig.categories.map((category) => {
            const isActive = selectedCategories.includes(category);
            return (
              <div
                key={category}
                className={`gradient-outline-btn cursor-pointer ${
                  isActive ? "selected" : ""
                }`}
                onClick={() => handleCategoryClick(category)}
              >
                {category}
              </div>
            );
          })}
        </div>
      </Col>

      <Col md={12} className="mb-4">
        <div className="mb-2" style={{ color: "var(--chart-text)" }}>Overall Sentiment</div>
        <div className="d-flex gap-3">
          {surveyConfig.sentiments.map((sentiment) => {
            const isActive = selectedSentiment === sentiment.value;
            return (
              <div
                key={sentiment.value}
                className={["white-outline-btn", isActive && "selected"]
                  .filter(Boolean)
                  .join(" ")}
                onClick={() => handleSentimentClick(sentiment.value)}
              >
                {sentiment.label} {sentiment.emoji}
              </div>
            );
          })}
        </div>
      </Col>
    </Row>
  );
};

export default FilterPanel;
