import { <PERSON>, <PERSON>, <PERSON><PERSON>, Nav, <PERSON> } from "react-bootstrap";
import { FaArrowLeft } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import audio from "../../../../../../efive_assets/images/audio-img.png";
import video from "../../../../../../efive_assets/images/video.png";
import fileicon from "../../../../../../efive_assets/images/file.png";
// import Preview_Audio_Modal from "../../../pages/Preview_Audio_Modal";
// import Preview_Video_Modal from "../../../pages/Preview_Video_Modal";
// import Preview_Image_Modal from "../../../pages/Preview_Image_Modal";
import { useEffect, useState } from "react";
// import { getImage } from "../../../../utils/CommonUtils";
import Lightbox from "yet-another-react-lightbox";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import Video from "yet-another-react-lightbox/plugins/video";
import { getImage } from "../../../../../utils/CommonUtils";
import Preview_Image_Modal from "../../../../pages/Preview_Image_Modal";
import Preview_Video_Modal from "../../../../pages/Preview_Video_Modal";
import Preview_Audio_Modal from "../../../../pages/Preview_Audio_Modal";

const HotWorkCheckInUploadDoc = ({
    attachmentModal,
    setAttachmentModal,
    allAttachements,
    setisGridLoading
}: any) => {
    const [imageModal, setImageModal] = useState<boolean>(false);
    const [audioModal, setAudioModal] = useState<boolean>(false);
    const [videoModal, setVideoModal] = useState<boolean>(false);
    const [file, setFile] = useState<any>();
    const [isLightboxOpen, setLightboxOpen] = useState(false);
    const [lightboxIndex, setLightboxIndex] = useState(0);

    const mediaAttachments = allAttachements?.filter(
        (item: any) => item?.attachmenttype === "image" || item?.attachmenttype === "video" || item?.attachmenttype === "audio"
    );

    const openLightbox = (index: number) => {
        setLightboxIndex(index);
        setLightboxOpen(true);
    };

    const handleOpenMediaModal = async (FileType: any, file: any) => {
        if (FileType.startsWith("image")) {
            setFile(getImage(file));
            setImageModal(true);
        } else if (FileType.startsWith("video")) {
            setFile(getImage(file));
            setVideoModal(true);
        } else if (FileType.startsWith("audio")) {
            setFile(getImage(file));
            setAudioModal(true);
        } else if (
            FileType.startsWith("application") ||
            FileType.startsWith("pdf")
        ) {
            setisGridLoading(true)
            const fileURL = getImage(file);
            function blobToURL(blob: any) {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.readAsDataURL(blob);
                    reader.onloadend = function () {
                        const base64data = reader.result;
                        resolve(base64data);
                    };
                });
            }
            const arrayBuffer = await fetch(fileURL);
            const blob = await arrayBuffer.blob();
            const url = await blobToURL(blob);
            // utils
            const fileName =
                file.split(`attachment_${FileType}_`).pop() || "default.pdf";
            if (fileURL) {
                const anchor = document.createElement("a");
                anchor.href = url as string;
                anchor.download = fileName;
                document.body.appendChild(anchor);
                anchor.click();
                document.body.removeChild(anchor);
                setisGridLoading(false);
            } else {
                setisGridLoading(false);
                console.error("File URL is not valid.");
            }
        }
    };

    const truncateFileName = (fileName: string, maxLength: number) => {
        // Find the file extension and its position
        const extensionMatch = fileName.match(/\.[^\.]+$/);
        const extension = extensionMatch ? extensionMatch[0] : "";
        const nameWithoutExtension = fileName.slice(0, -extension.length);

        // Check if the length of the name (without the extension) exceeds the maxLength
        if (nameWithoutExtension.length > maxLength) {
            return `${nameWithoutExtension.slice(0, maxLength)}... ${extension}`;
        }
        return fileName;
    };

    const checkFileType = (fileType: string, fileUrl: string) => {
        if (fileType.startsWith("image")) {
            return getImage(fileUrl);
        } else if (fileType.startsWith("video")) {
            return video;
        } else if (fileType.startsWith("audio")) {
            return audio;
        } else if (
            fileType.startsWith("application") ||
            fileType.startsWith("pdf")
        ) {
            return fileicon;
        } else {
            return fileicon;
        }
    };

    const attachments =
        allAttachements
            ?.map((attachment: any) => {
                if (attachment?.attachmenttype === "video") {
                    return {
                        type: "video" as const,
                        poster: "",
                        sources: [
                            {
                                src: getImage(attachment?.attachmenturl),
                                type: "video/mp4",
                            },
                        ],
                    };
                }
                if (attachment?.attachmenttype === "image") {
                    return {
                        type: "image" as const,
                        src: getImage(attachment?.attachmenturl),
                    };
                }
                if (attachment?.attachmenttype === "audio") {
                    return {
                        type: "audio" as const,
                        src: getImage(attachment?.attachmenturl),
                    };
                }
                return null;
            })
            .filter((attachment: any) => attachment !== null) || [];

    useEffect(() => {
        if (isLightboxOpen) {
            setTimeout(() => setLightboxOpen(true), 100);
        }
    }, [isLightboxOpen]);
    // console.log("attachments", allAttachements)
    return (
        <>
            <Modal
                className="modal-right modal-right-small p-0"
                scrollable={true}
                show={attachmentModal}
                onHide={() => {
                    setAttachmentModal(false);
                }}
                animation={false}
            >
                <Modal.Header className=" border-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">
                                <span className="me-5">
                                    <FaArrowLeft
                                        className="cursor-pointer"
                                        onClick={() => {
                                            setAttachmentModal(false);
                                        }}
                                    />
                                </span>
                                Media
                            </h2>
                        </Col>
                        <Col xs={2} className=" text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => {
                                    setAttachmentModal(false);
                                }}
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="p-5 mt-2">
                    <Row className="mt-3">
                        {mediaAttachments?.length > 0 ? (mediaAttachments?.map((item: any, index: number) => (
                            <Col sm={6} key={index} className="mb-3">
                                <Card className="cursor-pointer" onClick={() => openLightbox(index)} border="black">
                                    <Card.Img
                                        height={"120px"}
                                        src={
                                            item.attachmenttype === "image"
                                                ? getImage(item.attachmenturl)
                                                : item.attachmenttype === "audio"
                                                    ? audio
                                                    : video
                                        }
                                    />
                                </Card>
                            </Col>
                        ))) : <div className="text-center">No Media</div>}
                    </Row>

                    <Lightbox
                        open={isLightboxOpen}
                        close={() => setLightboxOpen(false)}
                        slides={mediaAttachments?.map((item: any) => {
                            if (item.attachmenttype === "video") {
                                return { type: "video", sources: [{ src: getImage(item.attachmenturl), type: "video/mp4" }] };
                            }
                            if (item.attachmenttype === "image") {
                                return { type: "image", src: getImage(item.attachmenturl) };
                            }
                            if (item.attachmenttype === "audio") {
                                return { type: "audio", src: getImage(item.attachmenturl) };
                            }
                            return null;
                        }).filter((item: any) => item !== null)}
                        index={lightboxIndex}
                        plugins={[Thumbnails, Video]}
                        render={{
                            slide: ({ slide }: any) => {
                                if (slide.type === "audio") {
                                    return (
                                        <div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
                                            <audio controls>
                                                <source src={slide.src} type="audio/mp3" />
                                                Your browser does not support the audio element.
                                            </audio>
                                        </div>
                                    );
                                }
                                return null;
                            },
                        }}
                        on={{
                            entering: () => {
                                // Prevent ResizeObserver spam on video load
                                setTimeout(() => window.dispatchEvent(new Event("resize")), 100);
                            },
                        }}
                    />


                </Modal.Body>
            </Modal>

            <Preview_Image_Modal
                previewimagemodal={imageModal}
                setpreviewimagemodal={setImageModal}
                viewImage={file}
            />
            <Preview_Video_Modal
                previevideomodal={videoModal}
                setprevievideomodal={setVideoModal}
                videoUrl={file}
            />
            <Preview_Audio_Modal
                previeaudiomodal={audioModal}
                setprevieaudiomodal={setAudioModal}
                audioUrl={file}
            />
        </>
    );
};

export default HotWorkCheckInUploadDoc;
