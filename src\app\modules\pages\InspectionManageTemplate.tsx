import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import React, { useEffect, useState } from "react";
import { IoSettingsOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import { ClipLoader } from "react-spinners";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import { getImage, swalMessages } from "../../utils/CommonUtils";
import { Badge, Dropdown } from "react-bootstrap";
import { IoIosCloseCircleOutline, IoMdAdd, IoMdMore } from "react-icons/io";
import { FaRegEye } from "react-icons/fa";
import {
  Md<PERSON>ontentCopy,
  MdOutlineDeleteF<PERSON>ver,
  MdOutlineEdit,
  MdPublishedWithChanges,
} from "react-icons/md";
import { FilterSvg, SortingSvg } from "../../utils/SvgUtils";
import usersvg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import {
  useGetInspectionManageTemplateMutation,
  useDeleteInspectionTemplateByIdMutation,
  useDuplicateInspectionTemplateByIdMutation,
  GetAllInspectionManageTemplateRes,
  usePublishInspectionTemplateByIdMutation,
} from "../../apis/inspectionListAPI";
import SearchBar from "../../shared/component/SearchBar";
import { HiOutlineDocumentDuplicate, HiQrcode } from "react-icons/hi";
import SwalMessage from "../common/SwalMessage";
import { processApiResponse } from "../../utils/helper";
import Print_QRCode_Modal from "./Print_QRCode_Modal";
import { useLayout } from "../../../_metronic/layout/core";
import { GoCheckCircle } from "react-icons/go";

const InspectionManageTemplate = () => {
  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : null;

  const isValidUserPermission =
    userinfo?.isfullaccess?.toString() === "1" &&
    userinfo?.displayusertype === "COMPANY_ADMIN";

  useEffect(() => {
    if (!isValidUserPermission) {
      navigate("/inspections", { replace: true });
    }
  }, [isValidUserPermission]);

  const initialSort: Array<any> = [{ field: "", dir: "asc" }];
  const [sort, setSort] = useState(initialSort);
  const [viewQRcode, setViewQRcode] = useState<string | null>(null);

  const navigate = useNavigate();
  const [getInspectionManageTemplate, { isLoading }] =
    useGetInspectionManageTemplateMutation();

  const [
    deleteInspectionTemplateById,
    { data: deleteData, isLoading: isDeleteLoading },
  ] = useDeleteInspectionTemplateByIdMutation();

  const [
    duplicateInspectionTemplateById,
    { data: duplicateData, isLoading: isDuplicatLoading },
  ] = useDuplicateInspectionTemplateByIdMutation();

  const [publishInspectionTemplateById, { isLoading: iPublishLoading }] =
    usePublishInspectionTemplateByIdMutation();

  const [searchKey, setSearhKey] = useState<string>("");
  const [templateRes, setTemplateRes] =
    useState<GetAllInspectionManageTemplateRes | null>(null);

  const gridTotalCount = templateRes?.data?.totalCount || 0;

  // Pagination start
  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >(initialDataState.take);
  const { setLabels } = useBreadcrumbContext();

  const pageSizesArray = [
    { label: "5", value: 5 },
    { label: "10", value: 10 },
    { label: "15", value: 15 },
    { label: "All", value: gridTotalCount },
  ];

  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );

  const handleDelete = async (id: string, status: boolean) => {
    const confirm = await SwalMessage(
      swalMessages.title.commonTitle,
      status
        ? swalMessages.text.activeMessage
        : swalMessages.text.inactiveMessage,
      status ? "Active Now" : "Inactive Now",
      swalMessages.icon.info,
      true
    );
    if (confirm) {
      deleteInspectionTemplateById({ inspectionTemplateId: id, active: status })
        .unwrap()
        .then((res) => {
          const isSuccess = res?.success;
          isSuccess && fetchTemplate();
          SwalMessage(
            null,
            isSuccess
              ? `Your template has been Successfully ${
                  status ? "Active" : "Inactive"
                }.`
              : res?.errormsg,
            "Ok",
            isSuccess ? "success" : "error",
            false
          );
        });
    }
  };

  const handleDuplicate = async (id: string) => {
    const confirm = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.duplicateTemplate,
      "Duplicate",
      swalMessages.icon.info,
      true
    );
    if (confirm) {
      duplicateInspectionTemplateById({ inspectionTemplateId: id })
        .unwrap()
        .then((res) => {
          const isSuccess = res?.success;
          isSuccess && fetchTemplate();
          SwalMessage(
            null,
            isSuccess
              ? "Your template has been successfully duplicated."
              : res?.errormsg,
            "Ok",
            isSuccess ? "success" : "error",
            false
          );
        });
    }
  };

  const handlePublish = async (id: string) => {
    const confirm = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.publishTemplate,
      "Publish",
      swalMessages.icon.info,
      true
    );
    if (confirm) {
      publishInspectionTemplateById({ inspectionTemplateId: id })
        .unwrap()
        .then((res) => {
          const isSuccess = res?.success;
          isSuccess && fetchTemplate();
          SwalMessage(
            null,
            isSuccess
              ? "Your template has been successfully published."
              : res?.errormsg,
            "Ok",
            isSuccess ? "success" : "error",
            false
          );
        });
    }
  };

  const pageChange = (event: any) => {
    const { skip, take } = event.page;
    const targetEvent = event.targetEvent as any;
    const newTake = targetEvent.value == "All" ? gridTotalCount : take;
    const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

    setPage({ skip, take: newTake });
    setPageSizeValue(newPageSizeValue);
  };

  const renderUserandimage = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <td className={`k-table-td cursor-pointer `}>
        <img
          src={dataItem?.imageUrl ? getImage(dataItem?.imageUrl) : usersvg}
          className={`rounded-circle`}
          width={"32px"}
          height={"32px"}
        />
        <span
          className={`user-name`}
          style={{
            display: "inlineBlock",
            paddingLeft: "10px",
            verticalAlign: "middle",
            lineHeight: "32px",
          }}
          title={content}
        >
          {content}
        </span>
      </td>
    );
  };

  const renderTooltipCell = (props: any) => {
    const { dataItem, field, content } = props;
    return (
      <td className="k-table-td">
        <span
          className="ellipsis-cell cursor-pointer "
          title={content}
          onClick={() => {
            navigate(
              `/inspections/manage-templates/template?id=${dataItem?.templateId}`
            );
          }}
        >
          {dataItem[field]}
        </span>
      </td>
    );
  };

  // Start Render Priority
  const renderPriority = (props: any) => {
    const { dataItem } = props;
    const priority = dataItem.priority.toLowerCase();

    const background =
      priority === "low"
        ? "success"
        : priority === "high"
        ? "warning"
        : priority === "medium"
        ? "primary"
        : priority === "urgent"
        ? "danger"
        : "";

    return (
      <td>
        <Badge
          bg={background}
          className="text-white ellipsis-cell text-capitalize"
          title={priority}
        >
          {priority}
        </Badge>
      </td>
    );
  };

  const renderStatus = (props: any) => {
    const { dataItem } = props;
    const priority = dataItem?.inspectionTemplateStatus?.toLowerCase();

    const background =
      priority === "publish"
        ? "success"
        : priority === "draft"
        ? "warning"
        : "";

    return (
      <td>
        <Badge
          bg={background}
          className="text-white ellipsis-cell text-capitalize"
        >
          {priority}
        </Badge>
      </td>
    );
  };

  const renderaction = (props: any) => {
    const content = props.dataItem;
    const inspectionTemplateStatus =
      props?.dataItem?.inspectionTemplateStatus?.toLowerCase();

    return (
      <>
        <td className="k-table-td">
          <Dropdown className="new-chat-btn" style={{ position: "static" }}>
            <Dropdown.Toggle as="span" className="fs-1 cursor-pointer ms-2">
              <IoMdMore className="td-icon cursor-pointer" />
            </Dropdown.Toggle>
            <Dropdown.Menu align="end">
              {inspectionTemplateStatus === "draft" && (
                <Dropdown.Item
                  onClick={() => handlePublish(content?.templateId)}
                >
                  <span className="fs-5">
                    <MdPublishedWithChanges className="me-4" size={18} />
                    Publish Template
                  </span>
                </Dropdown.Item>
              )}
              <Dropdown.Item
                onClick={() =>
                  navigate(
                    `/inspections/manage-templates/template?id=${content?.templateId}`
                  )
                }
              >
                <span className="fs-5">
                  <MdOutlineEdit className="me-4" size={18} />
                  Edit Details
                </span>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => handleDuplicate(content?.templateId)}
              >
                <span className="fs-5">
                  <HiOutlineDocumentDuplicate className="me-4" size={16} />
                  Duplicate
                </span>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() =>
                  handleDelete(
                    content?.templateId,
                    !(content?.isActive?.toString() === "true")
                  )
                }
              >
                <span className="fs-5">
                  {content?.isActive?.toString() === "true" ? (
                    <IoIosCloseCircleOutline className="me-4" size={18} />
                  ) : (
                    <GoCheckCircle className="me-4" size={18} />
                  )}
                  {content?.isActive?.toString() === "true"
                    ? "Inactive"
                    : "Active"}
                </span>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </td>
      </>
    );
  };

  const renderViewQR = (props: any) => <td> View QR</td>;

  const renderQrcode = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="text-center">
        <HiQrcode
          className="td-icon cursor-pointer user-image p-1"
          onClick={() => handleQRView(content.qrCodeBase64)}
        />
      </td>
    );
  };

  const renderActiveStatus = (props: any) => {
    const { dataItem } = props;
    const status = dataItem?.isActive?.toLowerCase();
    return (
      <td>
        {dataItem.isActive?.toString() === "true" ? (
          <Badge bg="success" className="text-white">
            Active
          </Badge>
        ) : (
          <Badge bg="danger" className="text-white">
            Inactive
          </Badge>
        )}
      </td>
    );
  };

  const handleQRView = (qrcode: any) => {
    setViewQRcode(qrcode);
  };

  useEffect(() => {
    setLabels([
      { path: "/inspections", state: {}, breadcrumb: "Inspections" },
      {
        path: "/inspections/manage-templates",
        state: {},
        breadcrumb: "Manage Templates",
      },
    ]);
  }, []);

  const fetchTemplate = async () => {
    const pageNumber = Math.floor(page.skip / page.take) + 1;
    const payload = {
      page: pageNumber,
      size: page.take,
      search: searchKey,
      sortingColumns: [],
    };
    await getInspectionManageTemplate(payload)
      .unwrap()
      .then((res) => {
        processApiResponse({
          res,
          onSuccess: () => {
            setTemplateRes(res);
          },
        });
      })
      .catch((err) => {});
  };

  //Get Inspection List
  useEffect(() => {
    fetchTemplate();
  }, [searchKey, page]); //Add dep

  const loding =
    isLoading || isDeleteLoading || isDuplicatLoading || iPublishLoading;

  return (
    <>
      <div>
        {loding && spinner}
        <div className="row pageheader mb-7">
          <div className=" col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto">
            <div className="row">
              <div className=" col-xl-6 col-lg-6 col-sm-6 col-6">
                <SearchBar
                  type="text"
                  onChange={(e) => setSearhKey(e.target.value)}
                />
              </div>
            </div>
          </div>
          <div className=" col-xl-6 col-lg-6 col-sm-6 text-end">
            <div className="d-flex justify-content-end">
              <div className=" p-0">
                <Link
                  to={"/inspections/manage-templates/template"}
                  className="btn  rx-btn ms-3 mt-3 mt-md-0"
                >
                  <IoMdAdd className="btn-icon-custom" />
                  Add New Template
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="card border-0 mt-0 ">
          <div className="card-body p-0">
            <div className="table_div" style={{ width: "100%" }}>
              <Tooltip position="bottom" anchorElement="target">
                <Grid
                  data={orderBy(templateRes?.data?.data || [], sort)}
                  skip={page.skip}
                  take={page.take}
                  total={templateRes?.data?.totalCount}
                  pageable={{
                    buttonCount: 4,
                    pageSizes: pageSizesArray.map((size: any) => size.label),
                    pageSizeValue: pageSizeValue,
                  }}
                  onPageChange={pageChange}
                  sortable
                  sort={sort}
                  onSortChange={(e: any) => setSort(e.sort)}
                >
                  <Column
                    title="QR Code"
                    headerClassName="center-header"
                    width="90px"
                    cell={renderQrcode}
                  />

                  {/* ID */}
                  <Column
                    title="Templates Name"
                    field="templateName"
                    cell={(props) =>
                      renderTooltipCell({
                        ...props,
                        content: props.dataItem.templateName,
                      })
                    }
                  />

                  <Column
                    title="Template Version"
                    field="templateVersion"
                    width="150px"
                    className="text-center"
                  />

                  <Column
                    title="Properties"
                    field="propertiesCount"
                    width="100px"
                    className="text-center"
                  />

                  <Column
                    title="Questions"
                    field="inspectionQuestionsCount"
                    width="98px"
                    className="text-center"
                  />

                  {/* Inspected by Column */}
                  <Column
                    title="Created by"
                    field="fullName"
                    cell={(props) =>
                      renderUserandimage({
                        ...props,
                        content: props.dataItem.fullName,
                      })
                    }
                  />

                  {/* Priority */}
                  <Column
                    title="Priority"
                    field="priority"
                    headerClassName="center-header"
                    width="90px"
                    cell={(props) =>
                      renderPriority({
                        ...props,
                        content: props.dataItem?.priority,
                      })
                    }
                  />

                  <Column
                    title="Template Status"
                    field="inspectionTemplateStatus"
                    headerClassName="center-header"
                    width="90px"
                    cell={(props) =>
                      renderStatus({
                        ...props,
                      })
                    }
                  />

                  <Column
                    title="Status"
                    field="isActive"
                    headerClassName="center-header"
                    width="82px"
                    cell={(props) =>
                      renderActiveStatus({
                        ...props,
                      })
                    }
                  />

                  {/* Action */}
                  <Column
                    title="Action"
                    width="80px"
                    cell={(props) =>
                      renderaction({
                        ...props,
                        content: props.dataItem.templateId,
                      })
                    }
                  />
                </Grid>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
      <Print_QRCode_Modal
        qrmodal={!!viewQRcode}
        setqrmodal={setViewQRcode}
        qrBase64={viewQRcode}
        setGridLoading={() => {}}
        title={"Download QR Code"}
      />
    </>
  );
};

export default InspectionManageTemplate;
