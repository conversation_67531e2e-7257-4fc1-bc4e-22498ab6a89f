import * as Yup from "yup";
import { ResponseType } from "../util/constant";

export interface BranchingFormValues {
  questionText: string;
  allowAttachment: boolean;
  attachmentType: string[];
  responseType: ResponseType; //TODO : Change to enum if needed
}

export const useBranchingForm = () => {
  const initialValues: BranchingFormValues = {
    questionText: "",
    allowAttachment: false,
    attachmentType: [],
    responseType: ResponseType.COMMENT
  };

  const validationSchema = Yup.object().shape({
    questionText: Yup.string().required("Branching question text is required"),
    allowAttachment: Yup.boolean(),
    attachmentType: Yup.array().when("allowbranchingAttchement", {
      is: true,
      then: (schema) => schema.min(1, "At least one attachment type must be selected"),
    }),
  });

  return {
    initialValues,
    validationSchema,
  };
}; 