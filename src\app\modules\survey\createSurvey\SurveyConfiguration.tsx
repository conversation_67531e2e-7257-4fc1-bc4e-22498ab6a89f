import React, { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import FormSwitch from "../../../component/form/FormSwitch";
import FormButton from "../../../component/form/FormButton";
import Footer from "./Footer";
import {
  useSetSurveyConfigurationMutation,
  useGetQrSurveyCodeMutation,
  useGetSurveyFullDetailsMutation,
} from "../../../apis/survaysAPI";
import { SurveyConfigurationPayload } from "../../../apis/type";
import useSurveyUtil from "../helper/useDetectSurvayType";
import { processApiResponse } from "../../../utils/helper";
import SwalMessage from "../../common/SwalMessage";
import Print_QRCode_Modal from "../../pages/Print_QRCode_Modal";
import { Loader } from "../../../component";
import { useNavigate } from "react-router";

const contactMethods = ["Text", "Call", "Email"];
const contactTimes = ["Morning", "Afternoon", "Evening"];

const defaultInitialValues: Omit<SurveyConfigurationPayload, "surveyId"> = {
  outsideContact: false,
  customerName: false,
  customerPhone: false,
  customerEmail: false,
  customerContactPreference: "Email",
  customerPreferredContactTime: "Afternoon",
  customerContactMessage: "",
  isCustomerContactPreferenceRequired: false,
  isCustomerPreferredContactTimeRequired: false,
};

interface Props {
  handleNext: () => void;
  handleBack: () => void;
}

const SurveyConfiguration: React.FC<Props> = ({ handleNext, handleBack }) => {
  const navigate = useNavigate();
  const { surveyId } = useSurveyUtil();
  const [setSurveyConfiguration, { isLoading }] =
    useSetSurveyConfigurationMutation();
  const [getQrSurveyCode, { isLoading: isQrLoading }] =
    useGetQrSurveyCodeMutation();
  const [getSurveyFullDetails, { isLoading: isFetchingDetails }] =
    useGetSurveyFullDetailsMutation();

  const [initialValues, setInitialValues] = useState(defaultInitialValues);
  const [showQrModal, setShowQrModal] = useState(false);
  const [qrBase64, setQrBase64] = useState("");

  const userdetail = JSON.parse(localStorage.getItem("userdetail") as string);

  // Pre-populate form if surveyId exists
  useEffect(() => {
    if (surveyId) {
      getSurveyFullDetails({ surveyId })
        .unwrap()
        .then((res) => {
          if (res?.data) {
            // Map the response data to our form structure
            setInitialValues({
              outsideContact: res.data.outsideContact || false,
              customerName: res.data.customerName,
              customerPhone: res.data.customerPhone,
              customerEmail: res.data.customerEmail,
              customerContactPreference: res.data.customerContactPreference,
              customerPreferredContactTime:
                res.data.customerPreferredContactTime,
              customerContactMessage: res.data.customerContactMessage,
              isCustomerContactPreferenceRequired:
                !!res.data.customerContactPreference,
              isCustomerPreferredContactTimeRequired:
                !!res.data.customerPreferredContactTime,
            });
          }
        })
        .catch(() => {
          SwalMessage(
            null,
            "Failed to load survey details",
            "Ok",
            "error",
            false
          );
        });
    }
  }, [surveyId]);

  const handleSubmit = async (values: typeof defaultInitialValues) => {
    if (!surveyId) {
      SwalMessage(null, "Survey ID is required", "Ok", "error", false);
      return;
    }
    // "surveyId": "string",
    //   "outsideContact": true,
    //   "customerName": true,
    //   "customerPhone": true,
    //   "customerEmail": true,
    //   "isCustomerContactPreferenceRequired": true,
    //   "customerContactPreference": "string",
    //   "isCustomerPreferredContactTimeRequired": true,
    //   "customerPreferredContactTime": "string",
    //   "customerContactMessage": "string"
    try {
      // Prepare payload for setSurveyConfiguration
      const payload: SurveyConfigurationPayload = {
        surveyId: surveyId,
        outsideContact: values.outsideContact,
        customerName: values.customerName,
        customerPhone: values.customerPhone,
        customerEmail: values.customerEmail,
        isCustomerContactPreferenceRequired:
          values.isCustomerContactPreferenceRequired,
        customerContactPreference: values.isCustomerContactPreferenceRequired
          ? values.customerContactPreference
          : "",
        isCustomerPreferredContactTimeRequired:
          values.isCustomerPreferredContactTimeRequired,
        customerPreferredContactTime:
          values.isCustomerPreferredContactTimeRequired
            ? values.customerPreferredContactTime
            : "",
        customerContactMessage: values.customerContactMessage,
      };

      // Call setSurveyConfiguration
      const configRes = await setSurveyConfiguration(payload).unwrap();

      processApiResponse({
        res: configRes,
        onSuccess: async () => {
          // Call getQrSurveyCode after successful configuration
          try {
            const qrRes = await getQrSurveyCode({ surveyId }).unwrap();

            processApiResponse({
              res: qrRes,
              onSuccess: (qrData) => {
                // Show QR Code modal
                setQrBase64(qrData.data?.qrcodeBase64 || "");
                setShowQrModal(true);
              },
            });
          } catch (qrError) {
            SwalMessage(
              null,
              "Failed to generate QR code",
              "Ok",
              "error",
              false
            );
          }
        },
      });
    } catch (error) {
      SwalMessage(
        null,
        "Failed to save survey configuration",
        "Ok",
        "error",
        false
      );
    }
  };

  return (
    <>
      {(isLoading || isQrLoading || isFetchingDetails) && <Loader />}
      <div className="mb-5">
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          onSubmit={handleSubmit}
        >
          {({ values, handleSubmit }) => (
            <Form>
              <div className="custom-card p-5 mb-5 mt-5">
                <div className="d-flex gap-2 align-items-center mb-5">
                  <Field type="checkbox" name="outsideContact" />
                  <p className="m-0">Do you want outside contact details?</p>
                </div>

                {values.outsideContact && (
                  <div style={{ maxWidth: 800 }}>
                    <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                      <p className="m-0">Customer Name</p>
                      <FormSwitch name="customerName" />
                    </div>
                    <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                      <p className="m-0">Customer Phone</p>
                      <FormSwitch name="customerPhone" />
                    </div>
                    <div className="p-4 response-viewer-box-wrapper mb-4 d-flex align-items-center justify-content-between">
                      <p className="m-0">Customer Email</p>
                      <FormSwitch name="customerEmail" />
                    </div>

                    <div className="p-4 response-viewer-box-wrapper mb-4">
                      <p className="m-0 mb-2">Customer Contact Message</p>
                      <Field
                        name="customerContactMessage"
                        className="form-control"
                        placeholder="Enter contact message"
                      />
                    </div>

                    <div className="mb-1 d-flex align-items-center justify-content-between pe-4 mb-5">
                      <p className="m-0">Customer Contact Preferences</p>
                      <FormSwitch name="isCustomerContactPreferenceRequired" />
                    </div>
                    {values.isCustomerContactPreferenceRequired && (
                      <div className="d-flex mt-1 mb-5">
                        {contactMethods.map((method) => (
                          <label
                            key={method}
                            style={{ marginRight: 16 }}
                            className="d-flex align-items-center gap-2"
                          >
                            <Field
                              type="radio"
                              name="customerContactPreference"
                              value={method}
                            />{" "}
                            {method}
                          </label>
                        ))}
                      </div>
                    )}

                    <div className="mb-1 d-flex align-items-center justify-content-between pe-4">
                      <p className="m-0">Customer Preferred Contact Time</p>
                      <FormSwitch name="isCustomerPreferredContactTimeRequired" />
                    </div>
                    {values.isCustomerPreferredContactTimeRequired && (
                      <div className="d-flex mt-3">
                        {contactTimes.map((time) => (
                          <label
                            key={time}
                            style={{ marginRight: 16 }}
                            className="d-flex align-items-center gap-2"
                          >
                            <Field
                              type="radio"
                              name="customerPreferredContactTime"
                              value={time}
                            />
                            <span>{time}</span>
                          </label>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              <Footer
                handleBack={handleBack}
                handleNext={handleSubmit}
                saveLabel="Save & Create QR Code"
              />
            </Form>
          )}
        </Formik>

        {/* QR Code Modal */}
        {showQrModal && (
          <Print_QRCode_Modal
            qrmodal={showQrModal}
            setqrmodal={(value: boolean) => {
              setShowQrModal(value)
              navigate("/surveys")
            }}
            qrBase64={qrBase64}
            userdetail={userdetail}
            setGridLoading={() => {}}
            title="QR Code"
          />
        )}
      </div>
    </>
  );
};

export default SurveyConfiguration;
