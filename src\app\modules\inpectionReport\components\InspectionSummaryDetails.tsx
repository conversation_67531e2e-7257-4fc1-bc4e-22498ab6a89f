import React from "react";
import { Badge, Form } from "react-bootstrap";
import { formatCustomDate, snakeCaseToTitleCase } from "../../../utils/helper";
import { InspectionReportDetails } from "../../../apis/type";
import usersvg from "../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";

interface Props {
  report: InspectionReportDetails;
  showSwitchValue?: boolean;
}

const InspectionSummaryDetails: React.FC<Props> = ({
  report,
  showSwitchValue,
}) => {
  const permissiontype = JSON.parse(
    localStorage.getItem("userdetail") as string
  )?.permission;
  const isPropertyBaseUser = permissiontype !== "D";

  const details = report?.departmentDetailsResponse?.departmentId
    ? report?.departmentDetailsResponse
    : report?.propertyDetailsResponse;

  const address = details?.address;
  const city = details?.city;
  const state = details?.state;
  const zipcode = details?.zipcode;

  const priority = report.priority.toLowerCase();

  const background =
    priority === "low"
      ? "success"
      : priority === "high"
      ? "warning"
      : priority === "medium"
      ? "primary"
      : priority === "urgent"
      ? "danger"
      : "";

  const inspectionFor = `${report.inspectionForMember.firstName} ${report.inspectionForMember.lastName}`;
  return (
    <div>
      <h2 className="fs-5 fw-semibold">
        #{report?.id || ""} {report.inspectionTemplateTitle}
      </h2>
      <div className="small">{address}</div>
      <div className="small">{`${state}, ${city}-${zipcode}`}</div>
      <div className="mt-4 d-flex flex-column gap-3">
        {/* <div className="d-flex justify-content-between">
          <span className="small">Type</span>
          <span className="small">Late Night Shop</span>
          </div> */}

        {report?.templateVersion && (
          <div className="d-flex justify-content-between">
            <span className="small">Template Version</span>
            <span className="small">{report?.templateVersion}</span>
          </div>
        )}

        {report?.inspectionType && (
          <div className="d-flex justify-content-between">
            <span className="small">Inspection Type </span>
            <span className="small">
              {snakeCaseToTitleCase(report?.inspectionType?.split("_")[0])}
            </span>
          </div>
        )}

        <div className="d-flex justify-content-between">
          <span className="small">Property/Department</span>
          <span className="small">
            {isPropertyBaseUser
              ? report?.propertyDetailsResponse?.propertyName
              : report?.departmentDetailsResponse?.departmentName}
          </span>
          {/* <span className="small">{}</span> */}
        </div>
        <div className="d-flex justify-content-between align-items-center">
          <span className="small">Priority</span>
          <Badge
            bg={background}
            className="text-white ellipsis-cell text-capitalize"
            title={priority}
            pill
          >
            {priority}
          </Badge>
        </div>

        {/* <div className="d-flex justify-content-between">
          <span className="small">Frequency</span>
          <span className="small">None</span>
        </div> */}

        <div className="d-flex justify-content-between">
          <span className="small">Inspection for</span>
          <div className="d-flex align-items-center justify-content-end">
            <div
              className="k-avatar k-rounded-full k-avatar-solid k-avatar-solid-primary me-1"
              style={{
                height: "20px",
                width: "20px",
              }}
            >
              <span className="k-avatar-image">
                <img src={usersvg} alt="" />
              </span>
            </div>
            <p className="m-0 text-end">
              <span className="small">{inspectionFor}</span>
            </p>
          </div>
        </div>

        <div className="d-flex justify-content-between">
          <span className="small">Created at</span>
          <span className="small">{formatCustomDate(report.createdAt)}</span>
        </div>

        {report?.inspectionTime && (
          <div className="d-flex justify-content-between">
            <span className="small">Inspection Time</span>
            <span className="small">{report.inspectionTime}</span>
          </div>
        )}

        {report?.permissionToViewMembers &&
          report?.permissionToViewMembers.length > 0 && (
            <div className="d-flex justify-content-between gap-3">
              <span className="small">Permission to view</span>
              <span className="small">
                {/* {report?.permissionToViewMembers.join(", ")} */}
                {report?.permissionToViewMembers.map((member, index) => (
                  <div
                    key={index}
                    className="d-flex align-items-center justify-content-end gap-1 mb-1"
                  >
                    <div
                      className="k-avatar k-rounded-full k-avatar-solid k-avatar-solid-primary me-1"
                      style={{
                        height: "20px",
                        width: "20px",
                      }}
                    >
                      <span className="k-avatar-image">
                        <img src={usersvg} alt="" />
                      </span>
                    </div>
                    <p className="m-0 text-end">
                      {member.firstName} {member.lastName}
                    </p>
                  </div>
                ))}
              </span>
            </div>
          )}

        {/* <div className="d-flex justify-content-between">
          <span className="small">Share report with vendor {showSwitchValue?.toString()}</span>
          {showSwitchValue ? (
            <span>Yes</span>
          ) : (
            <Form.Check type="switch" id="share-switch" />
          )}
        </div> */}
      </div>
    </div>
  );
};

export default InspectionSummaryDetails;
