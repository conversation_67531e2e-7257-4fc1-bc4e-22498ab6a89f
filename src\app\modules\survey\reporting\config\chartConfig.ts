// Chart Configuration System
// This file defines the chart types and their configurations for the survey reporting system

export interface ChartTypeConfig {
  id: string;
  name: string;
  description: string;
  icon?: string;
  supportedResponseTypes: string[];
  defaultOptions: any;
  isEnabled: boolean;
}

export interface FilterConfig {
  id: string;
  name: string;
  type: 'global' | 'local' | 'property' | 'custom';
  component: string;
  isEnabled: boolean;
  config: any;
}

// Chart Types Configuration
export const CHART_TYPES: Record<string, ChartTypeConfig> = {
  PIE_CHART: {
    id: 'pie',
    name: 'Pie Chart',
    description: 'Circular chart showing data distribution',
    supportedResponseTypes: ['multiple_choice', 'single_choice', 'yes_no'],
    defaultOptions: {
      height: 170,
      legend: { position: 'right' },
      dataLabels: { enabled: true }
    },
    isEnabled: true
  },
  BAR_CHART: {
    id: 'bar',
    name: 'Bar Chart',
    description: 'Horizontal bar chart for ratings and scales',
    supportedResponseTypes: ['rating', 'scale', 'nps'],
    defaultOptions: {
      height: 120,
      horizontal: true,
      stacked: true
    },
    isEnabled: true
  },
  DONUT_CHART: {
    id: 'donut',
    name: 'Donut Chart',
    description: 'Donut chart for category-based data',
    supportedResponseTypes: ['multiple_choice', 'single_choice'],
    defaultOptions: {
      height: 300,
      donut: { size: '65%' },
      legend: { position: 'bottom' }
    },
    isEnabled: true
  },
  STACKED_BAR_CHART: {
    id: 'stackedBar',
    name: 'Stacked Bar Chart',
    description: '100% stacked bar chart for comparative data',
    supportedResponseTypes: ['multiple_choice', 'rating'],
    defaultOptions: {
      height: 120,
      stacked: true,
      stackType: '100%'
    },
    isEnabled: true
  },
  LINE_CHART: {
    id: 'line',
    name: 'Line Chart',
    description: 'Line chart for trend analysis',
    supportedResponseTypes: ['rating', 'scale', 'date'],
    defaultOptions: {
      height: 200,
      stroke: { curve: 'smooth' }
    },
    isEnabled: false // Can be enabled in future
  },
  AREA_CHART: {
    id: 'area',
    name: 'Area Chart',
    description: 'Area chart for cumulative data',
    supportedResponseTypes: ['rating', 'scale'],
    defaultOptions: {
      height: 200,
      fill: { type: 'gradient' }
    },
    isEnabled: false // Can be enabled in future
  }
};

// Filter Types Configuration
export const FILTER_TYPES: Record<string, FilterConfig> = {
  GLOBAL_FILTER: {
    id: 'global',
    name: 'Global Filter',
    type: 'global',
    component: 'GlobalFilterModal',
    isEnabled: true,
    config: {
      fields: ['property', 'department', 'dateRange', 'status']
    }
  },
  LOCAL_FILTER: {
    id: 'local',
    name: 'Local Filter',
    type: 'local',
    component: 'LocalFilterModal',
    isEnabled: true,
    config: {
      fields: ['category', 'sentiment', 'responseType']
    }
  },
  PROPERTY_ASSIGNMENT: {
    id: 'propertyAssignment',
    name: 'Assign Global Property',
    type: 'property',
    component: 'PropertyAssignmentModal',
    isEnabled: true,
    config: {
      allowMultiple: true,
      required: false
    }
  },
  CUSTOM_FILTER: {
    id: 'custom',
    name: 'Custom',
    type: 'custom',
    component: 'CustomFilterModal',
    isEnabled: true,
    config: {
      allowDatePicker: true,
      allowCustomFields: true
    }
  }
};

// Chart Color Schemes
export const COLOR_SCHEMES = {
  DEFAULT: ['#4CAF50', '#F44336', '#FFC107', '#2196F3', '#9C27B0', '#FF9800'],
  SENTIMENT: {
    positive: '#4CAF50',
    neutral: '#FFC107',
    negative: '#F44336'
  },
  CATEGORY: {
    health: '#4CAF50',
    safety: '#F44336',
    procedures: '#2196F3',
    operations: '#FF9800',
    maintenance: '#9C27B0'
  },
  GRADIENT: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe']
};

// Export Configuration
export const EXPORT_CONFIG = {
  PDF: {
    enabled: true,
    options: {
      format: 'a4',
      orientation: 'portrait',
      quality: 2,
      margin: 10
    }
  },
  EXCEL: {
    enabled: true,
    options: {
      sheetName: 'Survey Report',
      includeCharts: false,
      includeRawData: true
    }
  },
  CSV: {
    enabled: true,
    options: {
      delimiter: ',',
      includeHeaders: true
    }
  }
};

// Utility functions
export const getEnabledChartTypes = (): ChartTypeConfig[] => {
  return Object.values(CHART_TYPES).filter(chart => chart.isEnabled);
};

export const getEnabledFilters = (): FilterConfig[] => {
  return Object.values(FILTER_TYPES).filter(filter => filter.isEnabled);
};

export const getChartTypeById = (id: string): ChartTypeConfig | undefined => {
  return Object.values(CHART_TYPES).find(chart => chart.id === id);
};

export const getFilterById = (id: string): FilterConfig | undefined => {
  return Object.values(FILTER_TYPES).find(filter => filter.id === id);
};

export const getColorForValue = (value: string, scheme: 'sentiment' | 'category' | 'default' = 'default'): string => {
  if (scheme === 'sentiment') {
    const lowerValue = value.toLowerCase();
    return COLOR_SCHEMES.SENTIMENT[lowerValue as keyof typeof COLOR_SCHEMES.SENTIMENT] || COLOR_SCHEMES.DEFAULT[0];
  }
  
  if (scheme === 'category') {
    const lowerValue = value.toLowerCase();
    return COLOR_SCHEMES.CATEGORY[lowerValue as keyof typeof COLOR_SCHEMES.CATEGORY] || COLOR_SCHEMES.DEFAULT[0];
  }
  
  // Default color scheme - cycle through colors
  const index = Math.abs(value.split('').reduce((a, b) => a + b.charCodeAt(0), 0)) % COLOR_SCHEMES.DEFAULT.length;
  return COLOR_SCHEMES.DEFAULT[index];
};
