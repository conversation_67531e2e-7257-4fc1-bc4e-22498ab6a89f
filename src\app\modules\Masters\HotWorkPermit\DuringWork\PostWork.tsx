import { Col, Row, Tooltip as React<PERSON>ool<PERSON>, Card } from "react-bootstrap";
import { getImage } from "../../../../utils/CommonUtils";
import { useEffect, useState } from "react";
import gooseiamge from "../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/Sidebar-footer-logo.svg";
import { DropDownList } from "@progress/kendo-react-dropdowns";
import userimage from "../../../../../efive_assets/images/user.jpg";
import Loopvendoorssvg from "../../../../../efive_assets/images/LoopVendorPostWork.svg";
import { MdOutlineEdit, MdOutlineInfo } from "react-icons/md";
import { PostWorkMember } from "./TabComponent/PostWorkMember";
import { CheckinsTab } from "./TabComponent/CheckinsTab";
import { CommentCheckins } from "./TabComponent/CommentCheckins";
import { Postworkinspection } from "./TabComponent/Postworkinspection";
import { useNavigate } from "react-router";

const statusData = [
  {
    id: "1",
    label: "Expired",
  },
  {
    id: "2",
    label: "Completed",
  },
];

// console.log(statusData);

const popupSettings = {
  className: "k-dropdown-custom-popup",
};

export const PostWork = () => {
  const navigate = useNavigate();

  const [color, setColor] = useState<string>("");
  const [expireIn, setExpireIn] = useState<any>(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleDetails = () => {
    setIsExpanded(!isExpanded);
  };

  const handleChange = (e: any) => {
    const selectedStatus = e.value.label;
    if (selectedStatus === "Expired") {
      setColor("red");
    } else if (selectedStatus === "Completed") {
      setColor("green");
    }
  };

  return (
    <>
      <Row>
        <Col xxl={8} xl={8} lg={8} sm={12}>
          <Row>
            <Col xxl={12} xl={12} lg={12} sm={12} className="">
              <ul className="nav nav-tabs nav-line-tabs fs-6">
                <li className="nav-item">
                  <a
                    className="nav-link active mb-0"
                    data-bs-toggle="tab"
                    href="#kt_tab_pane_1">
                    Check ins
                  </a>
                </li>
                <li className="nav-item view-nav-item">
                  <a
                    className="nav-link mb-0"
                    data-bs-toggle="tab"
                    href="#kt_tab_pane_2"
                    //   onClick={refreshGrid}
                  >
                    Comments
                  </a>
                </li>
                <li className="nav-item view-nav-item">
                  <a
                    className="nav-link mb-0"
                    data-bs-toggle="tab"
                    href="#kt_tab_pane_3">
                    Members
                  </a>
                </li>
                {expireIn ? (
                  <li className="nav-item view-nav-item">
                    <a
                      className="nav-link mb-0"
                      data-bs-toggle="tab"
                      href="#kt_tab_pane_4">
                      Post work Inspection
                    </a>
                  </li>
                ) : null}
              </ul>
            </Col>
          </Row>

          <div className="tab-content mt-5" id="myTabContent">
            <div
              className="tab-pane fade  active show"
              id="kt_tab_pane_1"
              role="tabpanel">
              <CheckinsTab />
            </div>
            <div
              className="tab-pane fade   "
              id="kt_tab_pane_2"
              role="tabpanel">
              <CommentCheckins />
            </div>
            <div
              className="tab-pane fade   "
              id="kt_tab_pane_3"
              role="tabpanel">
              <PostWorkMember />
            </div>
            <div
              className="tab-pane fade   "
              id="kt_tab_pane_4"
              role="tabpanel">
              <Postworkinspection />
            </div>
          </div>
        </Col>
        <Col md={4} sm={12} className="ticket-detail-right">
          <div
            className={`details custom-card p-5 border-3  ${
              isExpanded ? "expanded" : "collapsed"
            }`}>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex gap-3 align-items-center justify-content-between settingSlider w-100">
                <span className="fs-2">
                  <span>
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_6443_46352)">
                        <path
                          d="M18.581 2.14024L12.316 0.0512408C12.1109 -0.0170803 11.8891 -0.0170803 11.684 0.0512408L5.419 2.14024C4.42291 2.47112 3.55642 3.1075 2.94265 3.95895C2.32887 4.8104 1.99904 5.83363 2 6.88324V12.0002C2 19.5632 11.2 23.7402 11.594 23.9142C11.7218 23.971 11.8601 24.0004 12 24.0004C12.1399 24.0004 12.2782 23.971 12.406 23.9142C12.8 23.7402 22 19.5632 22 12.0002V6.88324C22.001 5.83363 21.6711 4.8104 21.0574 3.95895C20.4436 3.1075 19.5771 2.47112 18.581 2.14024ZM20 12.0002C20 17.4552 13.681 21.0332 12 21.8892C10.317 21.0362 4 17.4692 4 12.0002V6.88324C4.00006 6.25352 4.19828 5.63978 4.56657 5.12898C4.93486 4.61819 5.45455 4.23623 6.052 4.03724L12 2.05424L17.948 4.03724C18.5455 4.23623 19.0651 4.61819 19.4334 5.12898C19.8017 5.63978 19.9999 6.25352 20 6.88324V12.0002Z"
                          fill="white"
                        />
                        <path
                          d="M15.3 8.30105L11.112 12.5011L8.86804 10.1611C8.77798 10.0626 8.66913 9.98314 8.5479 9.9274C8.42666 9.87165 8.2955 9.84073 8.16213 9.83646C8.02877 9.83219 7.89589 9.85466 7.77134 9.90253C7.64679 9.95041 7.53308 10.0227 7.43691 10.1152C7.34074 10.2077 7.26405 10.3185 7.21137 10.4411C7.15869 10.5637 7.13107 10.6956 7.13015 10.8291C7.12923 10.9625 7.15502 11.0948 7.20601 11.2181C7.257 11.3414 7.33215 11.4532 7.42704 11.5471L9.73304 13.9471C9.90501 14.1328 10.1129 14.2817 10.3441 14.3849C10.5752 14.488 10.8249 14.5432 11.078 14.5471H11.111C11.3591 14.5479 11.6048 14.4994 11.834 14.4045C12.0632 14.3095 12.2712 14.17 12.446 13.9941L16.718 9.72205C16.8113 9.62894 16.8854 9.51837 16.936 9.39665C16.9865 9.27492 17.0126 9.14442 17.0128 9.01261C17.0129 8.8808 16.9871 8.75025 16.9368 8.62842C16.8865 8.50659 16.8126 8.39586 16.7195 8.30255C16.6264 8.20925 16.5159 8.1352 16.3941 8.08462C16.2724 8.03405 16.1419 8.00795 16.0101 8.00781C15.8783 8.00767 15.7477 8.0335 15.6259 8.08381C15.5041 8.13413 15.3933 8.20794 15.3 8.30105Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_6443_46352">
                          <rect width="24" height="24" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                  HW3829
                </span>
                <span className="text-end">
                  <img src={gooseiamge} alt="" />
                </span>
              </div>
            </div>
            <p className="fs-4 mt-3">Hot Work Permit</p>
            <span className="opacity-25 fs-12px">
              <span>Buckeye State Bank</span>
              <br />
              <span>Main Building, First Floor, Boiler Room</span>
              {/* {TicketDetail?.address?.city} {TicketDetail?.address?.state}
              <br />
              {TicketDetail?.address?.addressline1}
              {TicketDetail?.address?.addressline2}
              {TicketDetail?.address?.city} {TicketDetail?.address?.state} */}
              {/* {TicketDetail?.address?.zipcode} */}
            </span>
            <div className="d-flex align-items-center justify-content-between mt-7">
              <span>status</span>
              <DropDownList
                data={statusData}
                popupSettings={popupSettings}
                textField="label"
                dataItemKey="id"
                className="custom-dropdown "
                onChange={handleChange}
                style={{ backgroundColor: color }}
              />
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Created by</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                <img
                  src={userimage}
                  alt="uselogo"
                  height={"24px"}
                  width={"24px"}
                  className="rounded-circle"
                />
                <span>John Wick</span>
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Assigned to</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                <img
                  src={userimage}
                  alt="uselogo"
                  height={"24px"}
                  width={"24px"}
                  className="rounded-circle"
                />
                <span>Abubakar Saddique</span>
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Work to be Performed</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                Grinding
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Safety Officer post work</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                {/* {TicketDetail?.assignToMember?.length > 0 &&
                  TicketDetail?.assignToMember?.map((data: any, index: any) => {
                    return (
                      <div key={index}>
                        {index < 1 && (
                          <>
                            <img
                              src={getImage(data?.imageUrl)}
                              alt="uselogo"
                              className="rounded-circle"
                              height={"24px"}
                              width={"24px"}
                            />
                            <span className="ms-2">
                              {data?.firstName} {data?.lastName}
                            </span>
                          </>
                        )}
                      </div>
                    );
                  })}
                {TicketDetail?.assignToMember?.length == 0 && (
                  <span>Not Assigned</span>
                )} */}
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Loop Vendor</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                <img
                  src={Loopvendoorssvg}
                  alt="uselogo"
                  height={"24px"}
                  width={"24px"}
                  className="rounded-circle"
                />
                <span>HVAC Maintenance</span>
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Safety Officer during work</span>
              <div className="d-flex align-items-center justify-content-between gap-2">
                <div className="d-flex user-select-none">
                  {/* {TicketDetail?.obdserverMember?.map(
                    (data: any, index: any) => {
                      return (
                        <div key={index}>
                          {index < 3 && (
                            <Avatar
                              rounded="full"
                              size={"medium"}
                              type="image"
                              style={{
                                marginLeft: "-15px",
                                height: "24px",
                                width: "24px",
                              }}
                            >
                              <img src={getImage(data?.imageUrl)} alt="" />
                            </Avatar>
                          )}
                        </div>
                      );
                    }
                  )}
                  {TicketDetail?.obdserverMember?.length > 3 && (
                    <OverlayTrigger
                      placement="top"
                      overlay={(props: any) =>
                        renderTooltip(
                          props,
                          TicketDetail?.obdserverMember,
                          true
                        )
                      }
                    >
                      <div>
                        <Avatar
                          rounded="full"
                          className="custom-card form-label"
                          style={{
                            marginLeft: "-15px",
                            border: "1px solid white",
                            borderRadius: "50%",
                            height: "24px",
                            width: "24px",
                          }}
                        >
                          <span className="fs-12px">
                            {TicketDetail?.obdserverMember?.length - 3}+
                          </span>
                        </Avatar>
                      </div>
                    </OverlayTrigger> */}
                  {/* )} */}
                </div>
              </div>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Permit Expiry</span>
              <span>March 07 2024, 8:00 AM</span>
            </div>
            <div className="d-flex align-items-center justify-content-between mt-2">
              <span>Insurance Company</span>
              <span>Farm</span>
            </div>
          </div>
          {expireIn ? null : (
            <Row className="mt-4">
              <Col sm={12}>
                <div className="custom-card p-5">
                  <div className="d-flex align-items-center justify-content-between mt-2">
                    <div>
                      <h3>06:42:44</h3>
                      <span>Farm</span>
                    </div>
                    <span>
                      <MdOutlineEdit />
                    </span>
                  </div>
                </div>
              </Col>
            </Row>
          )}
          <Row className="mt-4">
            <Col sm={6}>
              <div
                className="custom-card p-5 badgediv"
                // onClick={() => setshowCalenderModal(true)}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_2319_41077)">
                    <path
                      d="M20 0H4C2.93913 0 1.92172 0.421427 1.17157 1.17157C0.421427 1.92172 0 2.93913 0 4L0 16C0 17.0609 0.421427 18.0783 1.17157 18.8284C1.92172 19.5786 2.93913 20 4 20H6.9L11.351 23.763C11.5316 23.9158 11.7605 23.9997 11.997 23.9997C12.2335 23.9997 12.4624 23.9158 12.643 23.763L17.1 20H20C21.0609 20 22.0783 19.5786 22.8284 18.8284C23.5786 18.0783 24 17.0609 24 16V4C24 2.93913 23.5786 1.92172 22.8284 1.17157C22.0783 0.421427 21.0609 0 20 0ZM22 16C22 16.5304 21.7893 17.0391 21.4142 17.4142C21.0391 17.7893 20.5304 18 20 18H17.1C16.6273 18.0001 16.1699 18.1677 15.809 18.473L12 21.69L8.193 18.473C7.83156 18.1673 7.3734 17.9997 6.9 18H4C3.46957 18 2.96086 17.7893 2.58579 17.4142C2.21071 17.0391 2 16.5304 2 16V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H20C20.5304 2 21.0391 2.21071 21.4142 2.58579C21.7893 2.96086 22 3.46957 22 4V16Z"
                      fill="white"
                    />
                    <path
                      d="M7 7H12C12.2652 7 12.5196 6.89464 12.7071 6.70711C12.8946 6.51957 13 6.26522 13 6C13 5.73478 12.8946 5.48043 12.7071 5.29289C12.5196 5.10536 12.2652 5 12 5H7C6.73478 5 6.48043 5.10536 6.29289 5.29289C6.10536 5.48043 6 5.73478 6 6C6 6.26522 6.10536 6.51957 6.29289 6.70711C6.48043 6.89464 6.73478 7 7 7Z"
                      fill="white"
                    />
                    <path
                      d="M17 9H7C6.73478 9 6.48043 9.10536 6.29289 9.29289C6.10536 9.48043 6 9.73478 6 10C6 10.2652 6.10536 10.5196 6.29289 10.7071C6.48043 10.8946 6.73478 11 7 11H17C17.2652 11 17.5196 10.8946 17.7071 10.7071C17.8946 10.5196 18 10.2652 18 10C18 9.73478 17.8946 9.48043 17.7071 9.29289C17.5196 9.10536 17.2652 9 17 9Z"
                      fill="white"
                    />
                    <path
                      d="M17 13H7C6.73478 13 6.48043 13.1054 6.29289 13.2929C6.10536 13.4804 6 13.7348 6 14C6 14.2652 6.10536 14.5196 6.29289 14.7071C6.48043 14.8946 6.73478 15 7 15H17C17.2652 15 17.5196 14.8946 17.7071 14.7071C17.8946 14.5196 18 14.2652 18 14C18 13.7348 17.8946 13.4804 17.7071 13.2929C17.5196 13.1054 17.2652 13 17 13Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_2319_41077">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span>Chat</span>
                <span className="bg-danger fs-10px rounded-circle badge ">
                  2
                </span>
              </div>
            </Col>
            <Col sm={6}>
              <div
                className="custom-card p-5 badgediv"
                // onClick={() => setsetChatModal(true)}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_4966_17790)">
                    <path
                      d="M7.83493 16.1705C7.60493 15.9405 7.38893 15.6885 7.19393 15.4225C6.86893 14.9765 6.96693 14.3505 7.41393 14.0255C7.85993 13.7005 8.48493 13.7985 8.81093 14.2445C8.93993 14.4225 9.08493 14.5935 9.24793 14.7555C10.0509 15.5585 11.1179 16.0005 12.2529 16.0005C13.3879 16.0005 14.4559 15.5585 15.2579 14.7555L20.7579 9.25545C22.4149 7.59845 22.4149 4.90145 20.7579 3.24445C19.1009 1.58745 16.4039 1.58745 14.7469 3.24445L13.6889 4.30245C13.2979 4.69345 12.6659 4.69345 12.2749 4.30245C11.8839 3.91145 11.8839 3.27945 12.2749 2.88845L13.3329 1.83045C15.7699 -0.607547 19.7349 -0.607547 22.1719 1.83045C24.6089 4.26745 24.6089 8.23245 22.1719 10.6695L16.6719 16.1695C15.4919 17.3505 13.9219 18.0005 12.2529 18.0005C10.5839 18.0005 9.01393 17.3505 7.83493 16.1705ZM6.25293 24.0005C7.92293 24.0005 9.49193 23.3505 10.6719 22.1695L11.7299 21.1115C12.1209 20.7215 12.1209 20.0885 11.7299 19.6975C11.3399 19.3065 10.7069 19.3075 10.3159 19.6975L9.25693 20.7555C8.45393 21.5585 7.38693 22.0005 6.25193 22.0005C5.11693 22.0005 4.04993 21.5585 3.24693 20.7555C2.44393 19.9525 2.00193 18.8855 2.00193 17.7505C2.00193 16.6155 2.44393 15.5475 3.24693 14.7455L8.74693 9.24545C9.54993 8.44245 10.6169 8.00045 11.7519 8.00045C12.8869 8.00045 13.9549 8.44245 14.7569 9.24545C14.9169 9.40645 15.0629 9.57745 15.1929 9.75545C15.5169 10.2025 16.1419 10.3025 16.5899 9.97645C17.0369 9.65145 17.1359 9.02645 16.8109 8.57945C16.6209 8.31745 16.4059 8.06645 16.1719 7.83245C14.9909 6.65045 13.4209 6.00045 11.7519 6.00045C10.0829 6.00045 8.51293 6.65045 7.33293 7.83145L1.83393 13.3315C0.65293 14.5115 0.00292969 16.0815 0.00292969 17.7505C0.00292969 19.4195 0.65293 20.9895 1.83393 22.1695C3.01393 23.3505 4.58293 24.0005 6.25293 24.0005Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_4966_17790">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>

                <span>Link Items</span>
              </div>
            </Col>
            <Col sm={6}>
              <div
                className="custom-card p-5 badgediv mt-2"
                onClick={() => navigate("/postwork/viewpermit")}>
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_6443_45210)">
                    <path
                      d="M19.95 5.54L16.46 2.05C15.14 0.73 13.38 0 11.51 0H7C4.24 0 2 2.24 2 5V19C2 21.76 4.24 24 7 24H17C19.76 24 22 21.76 22 19V10.49C22 8.62 21.27 6.86 19.95 5.54ZM18.54 6.95C18.86 7.27 19.13 7.62 19.35 8H15.01C14.46 8 14.01 7.55 14.01 7V2.66C14.39 2.88 14.74 3.15 15.06 3.47L18.55 6.96L18.54 6.95ZM20 19C20 20.65 18.65 22 17 22H7C5.35 22 4 20.65 4 19V5C4 3.35 5.35 2 7 2H11.51C11.67 2 11.84 2 12 2.02V7C12 8.65 13.35 10 15 10H19.98C20 10.16 20 10.32 20 10.49V19ZM7.09 13H6C5.45 13 5 13.45 5 14V18.44C5 18.79 5.28 19.06 5.62 19.06C5.96 19.06 6.24 18.78 6.24 18.44V17.22H7.08C8.26 17.22 9.22 16.27 9.22 15.11C9.22 13.95 8.26 13 7.08 13H7.09ZM7.09 15.97H6.26V14.25H7.1C7.58 14.25 7.99 14.64 7.99 15.11C7.99 15.58 7.58 15.97 7.1 15.97H7.09ZM19.02 13.63C19.02 13.98 18.74 14.25 18.4 14.25H16.71V15.39H17.95C18.3 15.39 18.57 15.67 18.57 16.01C18.57 16.35 18.29 16.63 17.95 16.63H16.71V18.43C16.71 18.78 16.43 19.05 16.09 19.05C15.75 19.05 15.47 18.77 15.47 18.43V13.62C15.47 13.27 15.75 13 16.09 13H18.4C18.75 13 19.02 13.28 19.02 13.62V13.63ZM12.09 13.01H11C10.45 13.01 10 13.46 10 14.01V18.45C10 18.8 10.28 19.01 10.62 19.01C10.96 19.01 12.08 19.01 12.08 19.01C13.26 19.01 14.22 18.06 14.22 16.9V15.12C14.22 13.96 13.26 13.01 12.08 13.01H12.09ZM12.98 16.9C12.98 17.37 12.57 17.76 12.09 17.76H11.26V14.26H12.1C12.58 14.26 12.99 14.65 12.99 15.12V16.9H12.98Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_6443_45210">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>

                <span>View Permit</span>
              </div>
            </Col>
            <Col sm={6}>
              <div
                className="custom-card p-5 badgediv mt-2"
                // onClick={() => setMoreDetailModal(true)}
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M7.33335 8.66675V4.66675C7.33335 4.30008 7.63335 4.00008 8.00002 4.00008C8.36668 4.00008 8.66669 4.30008 8.66669 4.66675V8.66675C8.66669 9.03341 8.36668 9.33342 8.00002 9.33342C7.63335 9.33342 7.33335 9.03341 7.33335 8.66675ZM8.00002 10.0001C7.44668 10.0001 7.00002 10.4467 7.00002 11.0001C7.00002 11.5534 7.44668 12.0001 8.00002 12.0001C8.55335 12.0001 9.00002 11.5534 9.00002 11.0001C9.00002 10.4467 8.55335 10.0001 8.00002 10.0001ZM15.72 13.2534C15.2534 14.1534 14.2734 14.6667 13.0467 14.6667H2.96002C1.72668 14.6667 0.753351 14.1534 0.286684 13.2534C-0.186649 12.3467 -0.0533158 11.1867 0.620018 10.2134L5.98002 1.73341C6.45335 1.05341 7.20002 0.666748 8.00002 0.666748C8.80002 0.666748 9.54669 1.05341 10 1.71341L15.3867 10.2267C16.06 11.2001 16.1867 12.3534 15.7134 13.2534H15.72ZM14.2867 10.9734C14.2867 10.9734 14.2734 10.9601 14.2734 10.9467L8.89335 2.44675C8.70002 2.17341 8.36668 2.00008 8.00002 2.00008C7.63335 2.00008 7.30002 2.17341 7.09335 2.47341L1.72668 10.9467C1.31335 11.5334 1.22002 12.1734 1.46002 12.6334C1.69335 13.0867 2.22668 13.3334 2.95335 13.3334H13.0334C13.76 13.3334 14.2934 13.0867 14.5267 12.6334C14.7667 12.1734 14.6734 11.5334 14.28 10.9734H14.2867Z"
                    fill="#ffffff"
                  />
                </svg>
                <span>Incident Report</span>
              </div>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
};
