import React from "react";
import { LuChartNoAxesColumnIncreasing, LuList } from "react-icons/lu";
import GooseSvg from "../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/Sidebar-footer-logo.svg";
import RxReactSelect from "../../../../component/RxReactSelect";
import { useChartContext } from "../context/ChartContext";

interface SecondaryTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const SecondaryTabs: React.FC<SecondaryTabsProps> = ({
  activeTab,
  onTabChange,
}) => {
  const { selectedChartOption, setSelectedChartOption } = useChartContext();

  const tabs = [
    {
      id: "goose",
      label: "Goose",
      icon: <img src={GooseSvg} alt="goose" width={16} height={16} />,
    },
    {
      id: "summary",
      label: "Summary",
      icon: <LuChartNoAxesColumnIncreasing />,
    },
    {
      id: "responses",
      label: "Full Response List",
      icon: <LuList />,
    },
  ];

  // Create chart type options (grouped by type, not individual charts)
  const chartTypeOptions = [
    { label: "Pie Charts", value: { type: "pie", id: 1, label: "Pie Charts" } },
    { label: "Donut Charts", value: { type: "donut", id: 1, label: "Donut Charts" } },
    { label: "Bar Charts", value: { type: "bar", id: 3, label: "Bar Charts" } },
    { label: "Stacked Bar Charts", value: { type: "stackedBar", id: 1, label: "Stacked Bar Charts" } },
    { label: "Line Charts", value: { type: "line", id: 3, label: "Line Charts" } },
    { label: "Area Charts", value: { type: "area", id: 3, label: "Area Charts" } },
    { label: "Scatter Plots", value: { type: "scatter", id: 3, label: "Scatter Plots" } },
    { label: "Category Charts", value: { type: "category", category: "Health", label: "Category Charts" } },
  ];

  // Find the currently selected chart type option
  const selectedOption = chartTypeOptions.find(
    option => option.value.type === selectedChartOption.type
  );

  // Handle chart selection change - NO TAB SWITCHING
  const handleChartChange = (selected: any) => {
    if (selected && selected.value) {
      setSelectedChartOption(selected.value);
      // REMOVED: No automatic tab switching - user stays on current tab
    }
  };

  return (
    <div className="d-flex gap-3 pb-2 align-items-center justify-content-between">
      <div className="d-flex gap-3 pb-2 align-items-center justify-content-center">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className="px-3 py-2 cursor-pointer"
            onClick={() => onTabChange(tab.id)}
            style={{
              color: "black",
              borderRadius: "8px",
              backgroundColor: "white",
              opacity: activeTab === tab.id ? "1" : "0.5",
            }}
          >
            {tab.icon} {tab.label}
          </div>
        ))}
      </div>

      <div style={{ minWidth: 200 }}>
        <RxReactSelect
          options={chartTypeOptions}
          value={selectedOption}
          onChange={handleChartChange}
          placeholder="Select Chart Type"
          className="basic-select"
          styles={{
            control: (provided) => ({
              ...provided,
              minHeight: "30px",
              height: "30px",
              fontSize: "0.8rem",
              border: "none",
              boxShadow: "none",
              borderRadius: "10px",
            }),
            valueContainer: (provided) => ({
              ...provided,
              height: "30px",
              padding: "0 8px",
            }),
            indicatorsContainer: (provided) => ({
              ...provided,
              height: "30px",
            }),
            input: (provided) => ({
              ...provided,
              margin: 0,
              padding: 0,
            }),
            placeholder: (provided) => ({
              ...provided,
              fontSize: "0.8rem",
            }),
            singleValue: (provided) => ({
              ...provided,
              fontSize: "0.8rem",
            }),
          }}
        />
      </div>
    </div>
  );
};

export default SecondaryTabs;
