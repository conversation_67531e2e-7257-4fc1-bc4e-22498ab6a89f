import noimage from "../../efive_assets/images/noimage.jpg";
// import { BACKEND_BASE_URL } from "../serverconfig/constants";
import moment from "moment";
import { StylesConfig, Theme } from "react-select";
import encryptDecryptUtil from "./encrypt-decrypt-util";
import { display } from "html2canvas/dist/types/css/property-descriptors/display";
const BACKEND_BASE_URL = import.meta.env.VITE_REACT_BACKEND_URL;

const formatContactInputNumber = (inputPhoneNumber: string) => {
  const formattedPhoneNumber = inputPhoneNumber?.replace(/\D/g, "").slice(0, 10);
  let formattedValue = "";

  for (let i = 0; i < formattedPhoneNumber?.length; i++) {
    if (i === 0) {
      formattedValue += "(";
    } else if (i === 3) {
      formattedValue += ")-";
    } else if (i === 6) {
      formattedValue += "-";
    }
    formattedValue += formattedPhoneNumber.charAt(i);
  }
  return formattedValue;
};

export type dropdownObject = {
  label: any;
  value: any;
};

export type filterObject = {
  properties?: dropdownObject[] | null | [];
  locktype?: any | {} | null;
  equipmentid?: any | {} | null;
  search?: any | null;
  isTransfer?: any | null;
  departmentIds?: any[] | null;
  department_Id?: any | null;
};

const formatPhoneNumber = (phoneNumber: string): string => {
  const formattedPhoneNumber = phoneNumber.replace(
    /^(\+)?(\d{1,3})(\d{3})(\d{3})(\d{4})$/,
    "+$2 ($3)-$4-$5"
  );
  return formattedPhoneNumber;
};

const swalMessages = {
  title: {
    commonTitle: "Are You Sure?",
  },
  text: {
    deleteCompanyMsg:
      "Do you really want to delete this company? This process cannot be undone.",
    deleteUserMsg:
      "Do you really want to delete this company? This process cannot be undone.",
    deleteDepartment:
      "Do you really want to delete this Department? This process cannot be undone.",
    deleteProperty:
      "Do you really want to delete this Property? This process cannot be undone.",
    deleteTicket:
      "Do you really want to delete this Ticket? This process cannot be undone.",
    deleteLock:
      "Do you really want to delete this Lock? This process cannot be undone.",
    deleteLockImageMsg:
      "Do you really want to delete this Lock Image? This process cannot be undone.",
    deleteLotoTransferFormMsg:
      "Do you really want to delete this LOTO Transfer Form? This process cannot be undone.",
    deleteLotoFormMsg:
      "Do you really want to delete this LotoForm? This process cannot be undone.",
    deleteLotoFormVideoMsg:
      "Do you really want to delete this Video? This process cannot be undone.",
    deleteLotoFormDocumentMsg:
      "Do you really want to delete this Document? This process cannot be undone.",
    deleteLotoFormImageMsg:
      "Do you really want to delete this Image? This process cannot be undone.",
    deleteLockboxImageMsg:
      "Do you really want to delete this LockBox Image? This process cannot be undone.",
    deleteLockBox:
      "Do you really want to delete this LockBox? This process cannot be undone.",
    deleteLotoFormCommentMsg:
      "Do you really want to delete this Comment? This process cannot be undone.",
    deleteProcedureMsg:
      "Do you really want to delete this Procedure? This process cannot be undone.",
    // goose chat
    deteleGooseChatGrouplist:
      "Do you really want to delete this Chat? This process cannot be undone.",
    deteleTemplate:
      "Do you really want to delete this Template? This process cannot be undone.",
    duplicateTemplate:
      "Do you really want to duplicate this Template?",
    publishTemplate:
      "Do you really want to publish this Template?",
    inactiveMessage : "Do you really want to Inactive this Template?",  
    activeMessage : "Do you really want to Active this Template?"  
  },
  endMessage: "This process cannot be undone.",
  icon: {
    success: "success",
    info: "info",
    warning: "warning",
    error: "error",
  },
  confirmButtonText: {
    delete: "Delete",
    change: "Change",
  },
};

const imageOnError = (event: {
  currentTarget: { src: any; className: string };
}) => {
  event.currentTarget.src = noimage;
  event.currentTarget.className = "image-input-wrapper";
};

const imageOnErrorForForms = (event: {
  currentTarget: {
    src: any;
    className: string;
    style: { height: string; width: string };
  };
}) => {
  event.currentTarget.src = noimage;
  event.currentTarget.className = "image-input-wrapper";
  event.currentTarget.style.height = "50px";
  event.currentTarget.style.width = "50px";
};

const profileImageOnError = (event: {
  currentTarget: { src: any; className: string };
}) => {
  event.currentTarget.src = noimage;
  event.currentTarget.className = "profile_img";
};

const userImageOnError = (event: {
  currentTarget: { src: any; className: string };
}) => {
  event.currentTarget.src = noimage;
  event.currentTarget.className = "user_img";
};

const getImage = (imagePath: string) => {
  if (imagePath) {
    return BACKEND_BASE_URL + "resources/files" + imagePath;
  } else {
    return noimage;
  }
};

const findObjectByValue = (valueToFind: any, data: any) => {
  // console.log("find------------------------------",valueToFind,data);
  // console.log("---------",data?.find((item: { value: any }) => item ));

  return data?.find((item: { value: any }) => item.value == valueToFind);
};

// property Common utils.
const findPropertyByPropertyId = (valueToFind: any, data: any) => {
  return data?.find(
    (item: { propertyid: any }) => item.propertyid == valueToFind
  );
};

const findObjectsByValues = (valuesToFind: any, data: any) => {
  // console.log(valuesToFind);
  // console.log(data);

  const foundObjects: any = [];
  valuesToFind.forEach((valueToFind: any) => {
    const foundObject = data?.find((item: any) => item.value == valueToFind);
    if (foundObject) {
      foundObjects.push(foundObject);
    }
  });
  return foundObjects;
};

const findValuesFromSelectedObjects = (objectArray: any) => {
  const foundValues: any = [];
  objectArray.forEach((obj: any) => {
    foundValues.push(obj.value);
  });
  return foundValues;
};

const getLabelByValue = (data: any, value: any) => {
  if (data && data.length > 0) {
    for (let i = 0; i < data.length; i++) {
      const locks = data[i].locks;
      for (let j = 0; j < locks.length; j++) {
        if (locks[j].value === value) {
          return locks[j].label;
        }
      }
    }
  }
  return "N/A";
};

const inputRegex = {
  withoutSpecialChars: /^[a-zA-Z0-9\s]*$/,
  validSpecialChars: /^[^<>`'`]*$/,
  validBase64:
    /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/,
  utf8Regex:
    /^[\u0000-\u007F\u00C0-\u00F6\u00F8-\u00FF\u0100-\u017F\u0180-\u024F\u2C60-\u2C7F\uA720-\uA7FF\uFB00-\uFB06\uF800-\uFFFF]+$/,
};

const formatDate = (dateString: string): string => {
  // Create a new Date object from the input string
  const date = new Date(dateString);

  // Extract the date components
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0"); // getMonth() returns 0-indexed month
  const year = date.getFullYear();

  // Extract the time components
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const ampm = hours >= 12 ? "pm" : "am";

  // Convert hours to 12-hour format
  hours = hours % 12 || 12;

  // Format the date and time
  const formattedDate = `${day}/${month}/${year}, ${String(hours).padStart(
    2,
    "0"
  )}:${minutes} ${ampm}`;

  return formattedDate;
};
const extensionPacks = {
  imageExtensions: ["image/jpeg", "image/png", "image/jpg"],
  videoExtensions: [
    "video/mp4",
    "video/quicktime",
    "video/x-ms-asf",
    "video/webm",
    "video/x-msvideo",
    "video/3gpp",
  ],
  documentExtensions: ["application/pdf"],
  mediaSizes: {
    MB2: 2 * 1024 * 1024,
    MB5: 5 * 1024 * 1024,
    MB10: 10 * 1024 * 1024,
    MB15: 15 * 1024 * 1024,
    MB20: 20 * 1024 * 1024,
  },
};

function formatDateString(dateString: string): string {
  return moment(dateString).format("DD-MM-YYYY HH:mm");
}

const getBase64 = async (file: any) => {
  return await new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    if (file) {
      reader.readAsDataURL(file);
    }
  });
};

const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64String = reader.result as string;
      resolve(base64String.split(",")[1]); // Extract the base64 string (remove data URL prefix)
    };
    reader.onerror = () => {
      reject(new Error("Error converting blob to base64"));
    };
    reader.readAsDataURL(blob);
  });
};

const DoubleSpaceToDotInput = (data: string) => {
  let value = data;
  if (value.slice(-2) === "  ") {
    value = value.slice(0, -2) + ". ";
  }
  // Automatic capitalization at the start of the sentence
  const sentences = value.split(/([.?!]\s+)/); // Split by sentence-ending punctuation followed by space

  const capitalizedValue = sentences
    .map((sentence, index) => {
      // Capitalize the first letter of each sentence
      if (index % 2 === 0 && sentence.length > 0) {
        return sentence.charAt(0).toUpperCase() + sentence.slice(1);
      }
      return sentence;
    })
    .join("");

  return capitalizedValue;
};

function formatSingleDate(dateStr: string, IsTimeShow: boolean) {
  if (!dateStr) {
    return "";
  }

  // Determine the correct format based on the day and month positions
  let format = "YYYY-MM-DD HH:mm:ss"; // Default to the standard format

  const dateParts = dateStr.split(" ")[0].split("-");
  if (parseInt(dateParts[1], 10) > 12) {
    // If the second part is greater than 12, it's likely in "YYYY-DD-MM" format
    format = "YYYY-MM-DD HH:mm:ss";
  }

  // Parse the input date string using the detected format
  const parsedDate = moment(dateStr, format);

  if (!parsedDate.isValid()) {
    return "";
  }

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "June",
    "July",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const month = months[parsedDate.month()];
  const day = parsedDate.date().toString().padStart(2, "0");
  const year = parsedDate.year();

  // Format the base date
  let formattedDate = `${month} ${day} ${year}`;

  // If IsTimeShow is true, show the time in 24-hour format
  if (IsTimeShow) {
    const hours24 = parsedDate.format("HH"); // 24-hour format
    const minutes = parsedDate.format("mm");
    const seconds = parsedDate.format("ss");
    formattedDate += ` ${hours24}:${minutes}:${seconds}`;
  }
  return formattedDate;
}

const validateUrl = (url: string) => {
  const urlPattern = new RegExp(
    "^(https?:\\/\\/)?" + // protocol (optional)
    "((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|" + // domain name
    "localhost|" + // OR localhost
    "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|" + // OR IPv4
    "\\[?[a-fA-F0-9:]+\\]?)" + // OR IPv6
    "(\\:\\d+)?(\\/[-a-zA-Z0-9@:%._\\+~#=]*)*" + // port and path
    "(\\?[;&a-zA-Z0-9%_\\+\\-=]*)?" + // query string (optional)
    "(\\#[-a-zA-Z0-9_]*)?$" // fragment identifier (optional)
  );
  return urlPattern.test(url);
};

const setEncryptData = (data: unknown): string => {
  const keyInfo = JSON.parse(localStorage.keyinfo);
  const strData = typeof data === "string" ? data : JSON.stringify(data);
  const payload = encryptDecryptUtil.encryptData(strData, keyInfo.syckey);
  return payload;
};

const getDecryptData = (data: string): any => {
  try {
    const keyInfo = JSON.parse(localStorage.keyinfo);
    const result = encryptDecryptUtil.decryptData(data, keyInfo.syckey);
    const encResponse = JSON.parse(result);
    return encResponse;
  } catch (error) {
    console.error(error);
    return data;
  }
};

const getSelectStyles = (): StylesConfig<any, boolean> => ({
  control: (provided) => ({
    ...provided,
    backgroundColor: "var(--r-select-control-bg)",
    color: "var(--r-select-color)",
    minHeight: "45px",
    borderRadius: "8px",
    border: "0.5px solid #8c8c8c",
    background: "var(--Rx-15-F6-color)",
    marginTop: "5px",
    "@media only screen and (max-width: 767px)": {
      minHeight: "36px",
    },
    "@media only screen and (min-width: 768px) and (max-width: 1023px)": {
      minHeight: "36px",
    },
  }),
  valueContainer: (provided) => ({
    ...provided,
    paddingLeft: "14px",
  }),
  placeholder: (provided) => ({
    ...provided,
    fontSize: "16px",
    color: "var(--Rx-title)",
    opacity: 0.6,
    fontWeight: 300,
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: "var(--r-select-menu-bg)",
  }),
  menuList: (provided, state) => ({
    ...provided,
    color: "var(--r-select-color)",
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isFocused
      ? "var(--r-select-option-focus)"
      : "var(--r-select-menu-bg)",
    color: state.isFocused || state.isSelected ? '#fff' : "var(--r-select-color)",
  }),
  singleValue: (provided) => ({
    ...provided,
    fontSize: "16px",
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: "var(--r-select-multi-chip-bg)",
    color: "var( --r-select-color)",
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: "var( --r-select-color)",
  }),
  multiValueRemove: (provided) => ({
    ...provided,
    color: "var( --r-select-color)",
    ":hover": {
      backgroundColor: "var(--r-select-remove-bg)",
      color: "#ffffff",
    },
  }),
  input: (provided) => ({
    ...provided,
    fontSize: "16px",
    fontWeight: 300,
  }),
  indicatorSeparator: (provided) => ({
    ...provided,
    display: "none",
  }),
});

const getSelectTheme = () => (theme: Theme) => ({
  ...theme,
  colors: {
    ...theme.colors,
    primary25: "var(--r-select-primary25)",
    primary: "var(--r-select-primary)",
    neutral0: "var(--r-select-control-bg)",
    neutral80: "var(--r---r-select-color)",
  },
});

export function mergeReactSelectStyle(
  base: { [key: string]: any },
  override: { [key: string]: any }
) {
  const merged = { ...base };

  for (const key in override) {
    if (base[key]) {
      // Both base and override have this key, merge them
      merged[key] = (...args: any) => ({
        ...base[key](...args),
        ...override[key](...args),
      });
    } else {
      // Only override has this key
      merged[key] = override[key];
    }
  }

  return merged;
}

// const AutoCapitalize = (value: string, isBlur: boolean = false) => {
// const AutoCapitalize = (value: string, isBlur: boolean = false) => {
//   const EXCLUDED_WORDS = [
//     "and",
//     "or",
//     "but",
//     "a",
//     "an",
//     "the",
//     "as",
//     "at",
//     "by",
//     "for",
//     "in",
//     "of",
//     "on",
//     "to",
//     "with",
//     "is",
//     "it",
//   ];
//   const sanitizedValue = value?.replace(/[^a-zA-Z0-9\s,.-]/g, ""); // Allow only alphanumeric, spaces, commas, periods, and hyphens

//   // Capitalize the last word after a space is added
//   const words = sanitizedValue?.trim().split(" ");
//   // Capitalize the last word after a space is added
//   // const words = value.trim().split(' ');
//   const lastWord = words?.pop(); // Get the last word

//   if (lastWord && sanitizedValue?.endsWith(" ")) {
//     // Capitalize the last word only if a space was entered
//     const capitalizedWord =
//       lastWord?.charAt(0).toUpperCase() + lastWord?.slice(1);
//     const FirstLatterCapital = EXCLUDED_WORDS?.includes(
//       capitalizedWord?.toLowerCase()
//     )
//       ? capitalizedWord?.toLocaleLowerCase()
//       : capitalizedWord;
//     return words.concat(FirstLatterCapital).join(" ") + " "; // Add space back
//   }

//   if (isBlur && lastWord) {
//     const capitalizedWord =
//       lastWord?.charAt(0).toUpperCase() + lastWord?.slice(1);
//     const FirstLatterCapital = EXCLUDED_WORDS?.includes(
//       capitalizedWord?.toLowerCase()
//     )
//       ? capitalizedWord?.toLocaleLowerCase()
//       : capitalizedWord;
//     return words?.concat(FirstLatterCapital).join(" "); // Add space back
//   }

//   return sanitizedValue; // Return original value if no space detected
// };

const AutoCapitalize = (value: string, isBlur: boolean = false): string => {
  const EXCLUDED_WORDS = [
    "and", "or", "but", "a", "an", "the", "as", "at", "by", "for", "in",
    "of", "on", "to", "with", "is", "it",
  ];

  const sanitizedValue = value.replace(/[^a-zA-Z0-9\s,.-]/g, ""); // Keep safe characters

  const words = sanitizedValue.trim().split(/\s+/);

  const capitalizedWords = words.map((word, index) => {
    const lowerWord = word.toLowerCase();
    if (index === 0 || !EXCLUDED_WORDS.includes(lowerWord)) {
      return lowerWord.charAt(0).toUpperCase() + lowerWord.slice(1);
    } else {
      return lowerWord;
    }
  });

  let result = capitalizedWords.join(" ");

  // preserve trailing space if user is typing
  if (!isBlur && value.endsWith(" ")) {
    result += " ";
  }
  return result;
};


const autoCapitalizeWithSpecialChar = (value: string, isBlur: boolean = false): string => {
  const EXCLUDED_WORDS = [
    "and", "or", "but", "a", "an", "the", "as", "at", "by", "for", "in",
    "of", "on", "to", "with", "is", "it",
  ];

  const words = value.trim().split(/\s+/);

  const capitalizedWords = words.map((word, index) => {
    const lowerWord = word.toLowerCase();
    if (index === 0 || !EXCLUDED_WORDS.includes(lowerWord)) {
      return lowerWord.charAt(0).toUpperCase() + lowerWord.slice(1);
    } else {
      return lowerWord;
    }
  });

  let result = capitalizedWords.join(" ");

  // preserve trailing space if user is typing
  if (!isBlur && value.endsWith(" ")) {
    result += " ";
  }
  return result;
};


const AutoCapitalizeDescription = (value: string) => {
  if (value?.trim().length === 0) return value; // If input is empty, return as is
  let result = ""; // Final processed string
  let capitalizeNext = true; // Flag to determine when to capitalize

  for (let i = 0; i < value?.length; i++) {
    const char = value[i];

    if (capitalizeNext && char !== " ") {
      result += char.toUpperCase(); // Capitalize the character
      capitalizeNext = false; // Reset the flag
    } else {
      result += char; // Append character as-is
    }

    // Handle cases to capitalize after a period followed by a space
    if (char === ".") {
      capitalizeNext = true; // Capitalize after the next non-space character
    } else if (char === " " && i > 0 && value[i - 1] === ".") {
      capitalizeNext = true; // Handle period followed by a space
    }
  }

  return result;
};

const getStatusColor = (connectionStatus: string) => {
  switch (connectionStatus) {
    case "initial":
      return "gray";
    case "connected":
      return "green";
    case "pending":
      return "yellow";
    case "failed":
      return "red";
    default:
      return "gray"; // Fallback color
  }
};

// for access
interface AccessCheckResult {
  moduleId: string | null;
  actions: number[];
  hasAction: (actionCode: number) => boolean;
}
const getAccessDetails = (): AccessCheckResult => {
  const path = window.location.pathname;

  // parse userInfo from localStorage
  const userInfo = JSON.parse(
    localStorage.getItem("userinfo") as string
  );

  const sidebarmenu = userInfo?.sidebarmenu || [];
  const accessList = userInfo?.access || [];
  const userType = userInfo?.displayusertype || "";

  // If COMPANY_ADMIN, allow all access
  if (userType === "COMPANY_ADMIN") {
    return {
      moduleId: null,
      actions: [],
      hasAction: () => true,
    };
  }

  const currentModule = sidebarmenu.find((item: any) => item.linkurl === path);
  const moduleId = currentModule?.linkid || null;
  const moduleAccess = accessList.find((item: any) => item.module === moduleId);
  const actions = moduleAccess?.action || [];

  // Define checker function
  const hasAction = (actionCode: number): boolean => actions.includes(actionCode);

  return {
    moduleId,
    actions,
    hasAction,
  };
};


export {
  blobToBase64,
  DoubleSpaceToDotInput,
  extensionPacks,
  findObjectByValue,
  findObjectsByValues,
  findPropertyByPropertyId,
  findValuesFromSelectedObjects,
  formatContactInputNumber,
  formatDate,
  formatDateString,
  formatPhoneNumber,
  formatSingleDate,
  getBase64,
  getDecryptData,
  getImage,
  getLabelByValue,
  getSelectStyles,
  getSelectTheme,
  imageOnError,
  imageOnErrorForForms,
  inputRegex,
  profileImageOnError,
  setEncryptData,
  swalMessages,
  userImageOnError,
  validateUrl,
  AutoCapitalize,
  AutoCapitalizeDescription,
  getStatusColor,
  autoCapitalizeWithSpecialChar,
  getAccessDetails
};
