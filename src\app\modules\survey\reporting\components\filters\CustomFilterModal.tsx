import React, { useState, useEffect } from "react";
import { Col, Form, Row, Button } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import { DateRangePicker } from "@progress/kendo-react-dateinputs";
import SingleSelectDropdown from "../../../../common/SingleSelectDropdown";

interface CustomFilterRule {
  id: string;
  field: string;
  operator: string;
  value: any;
  dataType: string;
}

interface CustomFilterData {
  rules: CustomFilterRule[];
  logic: "AND" | "OR";
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  customFields: { [key: string]: any };
}

const CustomFilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentFilters = {}
}) => {
  const [filterData, setFilterData] = useState<CustomFilterData>({
    rules: [],
    logic: "AND",
    dateRange: {
      start: null,
      end: null
    },
    customFields: {}
  });

  const [tempFilterData, setTempFilterData] = useState<CustomFilterData>(filterData);

  // Available fields for custom filtering
  const availableFields = [
    { label: "Question Text", value: "questionText", dataType: "string" },
    { label: "Response Count", value: "responseCount", dataType: "number" },
    { label: "Response Percentage", value: "responsePercentage", dataType: "number" },
    { label: "Question Type", value: "questionType", dataType: "string" },
    { label: "Category", value: "category", dataType: "string" },
    { label: "Sentiment", value: "sentiment", dataType: "string" },
    { label: "Created Date", value: "createdDate", dataType: "date" },
    { label: "Last Modified", value: "lastModified", dataType: "date" }
  ];

  const operatorsByType = {
    string: [
      { label: "Contains", value: "contains" },
      { label: "Equals", value: "equals" },
      { label: "Starts with", value: "startsWith" },
      { label: "Ends with", value: "endsWith" },
      { label: "Does not contain", value: "notContains" }
    ],
    number: [
      { label: "Equals", value: "equals" },
      { label: "Greater than", value: "greaterThan" },
      { label: "Less than", value: "lessThan" },
      { label: "Greater than or equal", value: "greaterThanOrEqual" },
      { label: "Less than or equal", value: "lessThanOrEqual" },
      { label: "Between", value: "between" }
    ],
    date: [
      { label: "Equals", value: "equals" },
      { label: "Before", value: "before" },
      { label: "After", value: "after" },
      { label: "Between", value: "between" }
    ]
  };

  const logicOptions = [
    { label: "AND (All conditions must match)", value: "AND" },
    { label: "OR (Any condition can match)", value: "OR" }
  ];

  useEffect(() => {
    if (currentFilters && Object.keys(currentFilters).length > 0) {
      const newFilterData = {
        rules: currentFilters.rules || [],
        logic: currentFilters.logic || "AND",
        dateRange: currentFilters.dateRange || { start: null, end: null },
        customFields: currentFilters.customFields || {}
      };

      // Only update if the data has actually changed
      setFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.rules) !== JSON.stringify(newFilterData.rules) ||
          prev.logic !== newFilterData.logic ||
          JSON.stringify(prev.dateRange) !== JSON.stringify(newFilterData.dateRange) ||
          JSON.stringify(prev.customFields) !== JSON.stringify(newFilterData.customFields);

        return hasChanged ? newFilterData : prev;
      });

      setTempFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.rules) !== JSON.stringify(newFilterData.rules) ||
          prev.logic !== newFilterData.logic ||
          JSON.stringify(prev.dateRange) !== JSON.stringify(newFilterData.dateRange) ||
          JSON.stringify(prev.customFields) !== JSON.stringify(newFilterData.customFields);

        return hasChanged ? newFilterData : prev;
      });
    }
  }, [currentFilters?.rules, currentFilters?.logic, currentFilters?.dateRange, currentFilters?.customFields]);

  const handleApply = () => {
    setFilterData(tempFilterData);
    onApply(tempFilterData);
    onClose();
  };

  const handleReset = () => {
    const resetData = {
      rules: [],
      logic: "AND" as "AND" | "OR",
      dateRange: { start: null, end: null },
      customFields: {}
    };
    setTempFilterData(resetData);
    setFilterData(resetData);
  };

  const addRule = () => {
    const newRule: CustomFilterRule = {
      id: Date.now().toString(),
      field: "",
      operator: "",
      value: "",
      dataType: "string"
    };
    setTempFilterData(prev => ({
      ...prev,
      rules: [...prev.rules, newRule]
    }));
  };

  const removeRule = (ruleId: string) => {
    setTempFilterData(prev => ({
      ...prev,
      rules: prev.rules.filter(rule => rule.id !== ruleId)
    }));
  };

  const updateRule = (ruleId: string, updates: Partial<CustomFilterRule>) => {
    setTempFilterData(prev => ({
      ...prev,
      rules: prev.rules.map(rule => 
        rule.id === ruleId ? { ...rule, ...updates } : rule
      )
    }));
  };

  const handleLogicChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      logic: selected?.value || "AND"
    }));
  };

  const handleDateRangeChange = (event: any) => {
    setTempFilterData(prev => ({
      ...prev,
      dateRange: {
        start: event.value.start,
        end: event.value.end
      }
    }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Custom Filter"
      filterType="custom"
    >
      <Col sm={12} className="mb-3">
        <Form.Label>Filter Logic</Form.Label>
        <SingleSelectDropdown
          data={logicOptions}
          getter={logicOptions.find(option => option.value === tempFilterData.logic)}
          setter={handleLogicChange}
          placeholder="Select Logic"
        />
      </Col>

      <Col sm={12} className="mb-3">
        <div className="d-flex justify-content-between align-items-center mb-2">
          <Form.Label>Filter Rules</Form.Label>
          <Button variant="outline-primary" size="sm" onClick={addRule}>
            Add Rule
          </Button>
        </div>
        
        {tempFilterData.rules.map((rule, index) => (
          <div key={rule.id} className="border rounded p-3 mb-2">
            <Row>
              <Col md={3}>
                <Form.Label>Field</Form.Label>
                <SingleSelectDropdown
                  data={availableFields}
                  getter={availableFields.find(field => field.value === rule.field)}
                  setter={(selected: any) => {
                    const field = availableFields.find(f => f.value === selected?.value);
                    updateRule(rule.id, {
                      field: selected?.value || "",
                      dataType: field?.dataType || "string",
                      operator: "",
                      value: ""
                    });
                  }}
                  placeholder="Select Field"
                />
              </Col>
              
              <Col md={3}>
                <Form.Label>Operator</Form.Label>
                <SingleSelectDropdown
                  data={operatorsByType[rule.dataType as keyof typeof operatorsByType] || []}
                  getter={operatorsByType[rule.dataType as keyof typeof operatorsByType]?.find(op => op.value === rule.operator)}
                  setter={(selected: any) => updateRule(rule.id, { operator: selected?.value || "" })}
                  placeholder="Select Operator"
                />
              </Col>
              
              <Col md={4}>
                <Form.Label>Value</Form.Label>
                {rule.dataType === "date" ? (
                  <DateRangePicker
                    value={{ start: null, end: null }}
                    onChange={(event: any) => updateRule(rule.id, { value: event.value })}
                    className="form-control"
                  />
                ) : (
                  <Form.Control
                    type={rule.dataType === "number" ? "number" : "text"}
                    value={rule.value}
                    onChange={(e) => updateRule(rule.id, { value: e.target.value })}
                    placeholder="Enter value"
                  />
                )}
              </Col>
              
              <Col md={2} className="d-flex align-items-end">
                <Button 
                  variant="outline-danger" 
                  size="sm" 
                  onClick={() => removeRule(rule.id)}
                >
                  Remove
                </Button>
              </Col>
            </Row>
          </div>
        ))}
        
        {tempFilterData.rules.length === 0 && (
          <div className="text-center text-muted p-3">
            No rules added. Click "Add Rule" to create custom filter conditions.
          </div>
        )}
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Date Range Filter</Form.Label>
        <DateRangePicker
          value={{
            start: tempFilterData.dateRange.start,
            end: tempFilterData.dateRange.end
          }}
          onChange={handleDateRangeChange}
          className="form-control"
        />
      </Col>
    </BaseFilterModal>
  );
};

export default CustomFilterModal;
