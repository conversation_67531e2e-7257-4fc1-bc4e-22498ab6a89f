import React from "react";
import { <PERSON>, Spinner } from "react-bootstrap";
import { LuThumbsUp, LuThumbsDown, LuCopy, Lu<PERSON>he<PERSON> } from "react-icons/lu";
import surveyConfig from "../data/surveyConfig.json";
import GradientStar from "../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/GradientStar.svg";
import { useActionWithFeedback } from "../../../../hooks/useActionWithFeedback";

const SummaryCard: React.FC = () => {
  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(surveyConfig.gooseSummary);
  };

  const { status: copyStatus, executeAction: executeCopy } = useActionWithFeedback(copyToClipboard);

  const handleSummarize = () => {
    console.log("Summarize clicked");
  };

  // Render the appropriate content based on status
  const renderCopyButtonContent = () => {
    switch (copyStatus) {
      case 'processing':
        return <Spinner animation="border" size="sm" role="status" aria-hidden="true" />;
      case 'success':
        return <LuCheck size={18} />;
      default:
        return <LuCopy size={18} />;
    }
  };

  return (
    <>
      <Card className="bg-white text-dark p-4 rounded-3">
        <h5 className="mb-3 fw-bold text-dark">Goose Summary</h5>
        <p className="mb-4">{surveyConfig.gooseSummary}</p>
      </Card>

      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex gap-3 mt-2">
          <button className="btn btn-sm btn-light rounded-circle p-2">
            <LuThumbsUp size={18} />
          </button>
          <button className="btn btn-sm btn-light rounded-circle p-2">
            <LuThumbsDown size={18} />
          </button>
          <button
            className="btn btn-sm btn-light rounded-circle p-2"
            onClick={executeCopy}
          >
            {renderCopyButtonContent()}
          </button>
        </div>
      </div>
      <button
        className="btn rx-btn me-xl-4 me-0 mb-3 mb-xl-0 mb-sm-0 mb-md-0 mobile-margin d-flex align-items-center justify-content-center gap-2"
        onClick={handleSummarize}
      >
        <img src={GradientStar} width={16} height={16} alt="star" />
        Summarize
      </button>
    </>
  );
};

export default SummaryCard;
