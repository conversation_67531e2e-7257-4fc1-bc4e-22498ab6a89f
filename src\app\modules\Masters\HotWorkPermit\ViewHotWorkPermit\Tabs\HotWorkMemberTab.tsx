import React, { useState } from "react";
import { Card } from "react-bootstrap";
import { MdOutlineInfo } from "react-icons/md";
import { IoMdMore } from "react-icons/io";
import userimage from "../../../../../../efive_assets/images/user.jpg";
import { HotWorkViewMemberModal } from "../HotWorkViewModal/HotWorkViewMemberModal"

export const HotWorkMemberTab = () => {
  const [memberSortingModal, setMemberSortinfModal] = useState<boolean>(false);

  return (
    <>
      <div className="d-flex flex-end mb-3">
        <div className="cursor-pointer text-center user-image sorting-member" onClick={() => setMemberSortinfModal(true)}>
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_6443_46340)">
              <path
                d="M20 0.833333C20 1.29417 19.6267 1.66667 19.1667 1.66667H8.33335C7.87335 1.66667 7.50001 1.29417 7.50001 0.833333C7.50001 0.3725 7.87335 0 8.33335 0H19.1667C19.6267 0 20 0.3725 20 0.833333ZM16.6667 4.16667H8.33335C7.87335 4.16667 7.50001 4.53917 7.50001 5C7.50001 5.46083 7.87335 5.83333 8.33335 5.83333H16.6667C17.1267 5.83333 17.5 5.46083 17.5 5C17.5 4.53917 17.1267 4.16667 16.6667 4.16667ZM14.1667 8.33333H8.33335C7.87335 8.33333 7.50001 8.70583 7.50001 9.16667C7.50001 9.6275 7.87335 10 8.33335 10H14.1667C14.6267 10 15 9.6275 15 9.16667C15 8.70583 14.6267 8.33333 14.1667 8.33333ZM11.6667 12.5H8.33335C7.87335 12.5 7.50001 12.8725 7.50001 13.3333C7.50001 13.7942 7.87335 14.1667 8.33335 14.1667H11.6667C12.1267 14.1667 12.5 13.7942 12.5 13.3333C12.5 12.8725 12.1267 12.5 11.6667 12.5ZM6.42251 16.0775L5.00001 17.5V0.833333C5.00001 0.3725 4.62668 0 4.16668 0C3.70668 0 3.33335 0.3725 3.33335 0.833333V17.5L1.91001 16.0767C1.58418 15.7508 1.05751 15.7508 0.73168 16.0767C0.405846 16.4025 0.405846 16.9292 0.73168 17.255L2.98751 19.5108C3.31251 19.8358 3.73918 19.9983 4.16668 19.9983C4.59418 19.9983 5.02001 19.8358 5.34501 19.5108L7.60085 17.255C7.92668 16.9292 7.92668 16.4025 7.60085 16.0767C7.27501 15.7508 6.74835 15.7517 6.42251 16.0775Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_6443_46340">
                <rect width="20" height="20" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div className="notification">
          <span className="text-muted cursor-pointer">
            <MdOutlineInfo size={15} />
          </span>
          <span className="tooltip-text">
            Displays the role of each member involved in the permit,
            highlighting their responsibilities during the post-work phase.
          </span>
        </div>
      </div>
      <div className="member-section">
        <span >Created by</span>
        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">

              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>John Wick</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
         <span>Assigned to</span>

        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>John Wick</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
                <span>Fire watcher post work (2)</span>

        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>Abubakar Saddique</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>Abubakar Saddique</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
                <span>Fire watcher during work (2)</span>

        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>Ralph Edwards</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
        <Card className="mb-4 mt-3 custom-card cursor-pointer">
          <Card.Body className="p-2">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div className="d-flex align-items-center  gap-5">
                <div>
                  <img
                    src={userimage}
                    alt="uselogo"
                    height={"48px"}
                    width={"48px"}
                    className="rounded-circle"
                  />
                </div>
                <div className="mt-3">
                  <span>Ralph Edwards</span>
                  <p className="text-muted fs-8">Job Title</p>
                </div>
              </div>
              <div className="time d-flex flex-column flex-end">
                <span>
                  <IoMdMore size={20} />
                </span>
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>

      <HotWorkViewMemberModal memberSortingModal={memberSortingModal} setMemberSortinfModal={setMemberSortinfModal} />
    </>
  );
};
