import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useEffect, useState } from 'react';
import { Badge, Col, Dropdown, Row } from "react-bootstrap";
import { IoMdMore } from 'react-icons/io';
import { MdOutlineEdit } from "react-icons/md";
import { useNavigate } from 'react-router-dom';
import AddEsclationModal from "./EscalationModals/AddEsclationModal";
import { escalationService } from "./escalation.helper";
import { LuLogs } from "react-icons/lu";
import LogsModal from "./EscalationModals/LogsModal";
import ViewEscalationUserModal from "./EscalationModals/ViewEscalationUserModal";
import { FaHistory } from "react-icons/fa";
import ChangesLogsModal from "./EscalationModals/ChangesLogsModal";
import { useBreadcrumbContext } from "../../../../../../_metronic/layout/components/header/BreadcrumbsContext";
import encryptDecryptUtil from "../../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../../common/SwalMessage";
import { swalMessages } from "../../../../../utils/CommonUtils";
import Spinner from "../../../../common/Spinner";


function Escalation({
    propertyId,
    refreshKey,
    activeTab,
}: any) {
    const navigate = useNavigate();
    const initialSort: Array<any> = [
        { field: "User Name", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);
    const [totalCount, setTotalCount] = useState<any>();
    const [gridData, setGridData] = useState<any>([])
    const [search, setSearch] = useState<any>("")
    const [addEscalationModal, setAddEscalationModal] = useState(false);
    const [openLogsModal, setOpenLogsModal] = useState(false);
    const [openChangeLogsModal, setOpenChangeLogsModal] = useState(false);
    const [editData, setEditData] = useState<any>();
    const [escalationId, setEscalationId] = useState<string>("");
    const [userData, setUserData] = useState<any[]>([])
    const [getModule, setGetModule] = useState<any>();
    const [viewUserData, setViewUserData] = useState<any>()
    const [viewMemberModal, setViewMemberModal] = useState<boolean>(false)
    const [gridLoading, setGridLoading] = useState<boolean>(false)
    const [shouldFetchGrid, setShouldFetchGrid] = useState(false);
    // Pagination start
    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);

    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
    };

    const itemPerPage: any = [
        {
            label: "5",
            value: 5,
        },
        {
            label: "10",
            value: 10,
        },
        {
            label: "15",
            value: 15,
        },
        {
            label: "All",
            value: totalCount,
        },
    ];
    //   Pagination end

    // Grid API 
    const escalationGrid = (
        page: any,
        search: any,
        propertyId: any,
        departmentId: any
    ) => {
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        escalationService
            .getGridData(page, page.take, search, propertyId, departmentId)
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        console.log("object", encResponse.data)
                        setGridData(encResponse.data);
                        setTotalCount(encResponse?.totalCount);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    };

    useEffect(() => {
        const pageNumber = Math.floor(page.skip / page.take) + 1;
        if (activeTab === "EscalationTab") {
            if (!addEscalationModal && !shouldFetchGrid) {
                setTimeout(() => {
                    escalationGrid(pageNumber, search, propertyId, null);
                }, 500);
            }
        }
    }, [search, page, refreshKey, activeTab]);


    const getUserMemberData = async (isMemberModal: boolean, propertyId: string): Promise<any[]> => {
        setGridLoading(true)
        let keyinfo = JSON.parse(localStorage.keyinfo);
        try {
            const res = await escalationService.getEscalationUserData(propertyId, "", "", "");
            if (res?.data?.success) {
                const result = encryptDecryptUtil.decryptData(
                    res?.data?.data,
                    keyinfo.syckey
                );
                const encResponse = JSON.parse(result);
                if (isMemberModal) {
                    setViewUserData(encResponse);
                }
                return encResponse || [];
            }
        } catch (err: any) {
            SwalMessage('error', err?.message, 'OK', 'error', false);
        } finally {
            setGridLoading(false)
        }
        return [];
    };

    // Get Edit data 
    const handleedit = async (id: any) => {
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);

        try {
            const [editResponse, userData] = await Promise.all([
                escalationService.getEditData(id, propertyId),
                getUserMemberData(false, propertyId)
            ]);

            // Handle edit data
            if (editResponse.status === 200 && editResponse.data.success === true) {
                const result = encryptDecryptUtil.decryptData(
                    editResponse.data.data,
                    keyinfo.syckey
                );
                const encResponse = JSON.parse(result);
                setEscalationId(id);
                setEditData(encResponse);
                setUserData(userData);
                setAddEscalationModal(true);
            } else {
                SwalMessage(null, editResponse.data.errormsg, "Ok", "error", false);
            }
        } catch (error: any) {
            if (error?.response?.status === 401) {
                // localStorage.removeItem("islogin");
                navigate("/dashboard");
                // navigate(0);
            }
            SwalMessage(null, error?.response?.data?.message, "Ok", "error", false);
        } finally {
            setGridLoading(false);
        }
    };

    // Change Status
    const changeStatus = async (id: any, data: any) => {
        const pageNumber = Math.floor(page.skip / page.take) + 1;
        const confirmed = await SwalMessage(
            `Are You Sure ?`,
            `Do you really want to change the status of ${data?.module} module to ${data?.active == "1" ? "Inactive" : "Active"} ?`,
            swalMessages.confirmButtonText.change,
            swalMessages.icon.info,
            true
        );
        if (confirmed) {
            try {
                setGridLoading(true);
                const response = await escalationService
                    .changeStatus(id, data?.active === "1" ? 0 : 1)
                    .then((response: any) => {
                        setGridLoading(false);
                        return response.data;
                    })
                    .catch((e: any) => {
                        if (e.response.status == 401) {
                            // localStorage.removeItem("islogin");
                            navigate("/dashboard");
                            // navigate(0);
                        }
                        SwalMessage(null, e.message, "Ok", "error", false);
                    });

                if (response.success == true) {
                    SwalMessage(
                        null,
                        response.errormsg,
                        "Ok",
                        "success",
                        false
                    )
                        .then(() => {
                            escalationGrid(pageNumber, "", propertyId, null);
                        });
                }
            } catch (error) {
                console.error("Error fetching data:", error);
                SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
            }
        }
    };
    const renderswitch = (id: any, data: any) => {
        if (data?.active === "N/A") {
            return (
                <td className='k-table-td'>
                    <span className='ellipsis-cell'>{data?.active}</span>
                </td>
            );
        }

        return (
            <td>
                {/* <div className="d-flex align-items-center">
                    <div className="me-3">
                        <input
                            id={id}
                            type="checkbox"
                            className="checkbox"
                            onChange={() => changeStatus(id, data)}
                            checked={data?.active === "1" ? false : true}
                        />
                        <label htmlFor={id} className="switch">
                            <span className="switch__circle">
                                <span className="switch__circle-inner"></span>
                            </span>
                            <span className="switch__left">Active</span>
                            <span className="switch__right">Inactive</span>
                        </label>
                    </div>
                </div> */}
                {data?.active === "1" ? (
                    <Badge bg="success" className="text-white">
                        Active
                    </Badge>
                ) : (
                    <Badge bg="danger" className="text-white">
                        Inactive
                    </Badge>
                )}
            </td>
        );
    };


    const renderaction = (props: any) => {
        const { content, dataItem } = props
        return (
            <td className="k-table-td">

                <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                    <Dropdown.Toggle
                        as="span"
                        className="fs-1 cursor-pointer ms-2"
                    >
                        <IoMdMore className="td-icon cursor-pointer" />
                    </Dropdown.Toggle>
                    <Dropdown.Menu align="end">
                        <Dropdown.Item onClick={() => handleedit(content)}>
                            <span className="fs-5">
                                <MdOutlineEdit className="me-4" size={18} />
                                Edit
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => { setOpenLogsModal(true); setGetModule(dataItem) }}>
                            <span className="fs-5">
                                <LuLogs className="me-4" size={16} />
                                Escalation Logs
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => { setOpenChangeLogsModal(true); setGetModule(dataItem) }}>
                            <span className="fs-5">
                                <FaHistory className="me-4" size={14} />
                                Audit Logs
                            </span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </td>
        );
    };
    // end action

    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        return (
            <td className='k-table-td'>
                <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
            </td>
        );
    };

    // end tooltip

    // Render Counts start
    const renderCounts = (props: any) => {
        const { dataItem } = props;

        return (
            <td className="k-table-td ">
                <span
                    className="ellipsis-cell text-center cursor-pointer"
                    onClick={() => { setEscalationId(dataItem?.escalationId); setViewMemberModal(true) }}
                >
                    {dataItem.memberCount}
                </span>
            </td>
        );
    };
    // Render Counts End


    const renderPriority = (props: any) => {
        const { dataItem } = props;
        const priority = dataItem.priority;

        const background =
            priority === "Low"
                ? "success"
                : priority === "High"
                    ? "warning"
                    : priority === "Medium"
                        ? "primary"
                        : priority === "Urgent"
                            ? "danger"
                            : "";

        return (
            <td>
                <Badge
                    bg={background}
                    className="text-white ellipsis-cell"
                    style={{ width: '80px', textAlign: 'center' }}
                >
                    {priority}
                </Badge>
            </td>
        );
    };

    return (
        <>
            {gridLoading && <Spinner />}
            <Row className='pageheader mb-7'>
                <Col xl={9} lg={9} md={8} sm={12} className='mt-auto mb-auto'>
                    <h1 className='page-title mobile-margin mb-0'>
                        Escalation
                    </h1>
                </Col>
                <Col xl={3} lg={3} md={4} sm={12} className="mt-auto mb-auto text-end">
                    <input
                        type="text"
                        className="form-control"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                        placeholder="Search..."
                    />
                    {/* <Link
                        to={""}
                        className="btn rx-btn ms-3 mobile-margin mb-lg-3"

                    >
                        <IoFilter className="btn-icon-custom" />
                        Filter{" "}
                    </Link>
                    <Link to={""} className="btn rx-btn ms-3 mobile-margin mb-lg-3" >
                        <FaFileDownload className="btn-icon-custom" />

                        Export
                    </Link>
                    <span className="btn rx-btn ms-3 mobile-margin mb-lg-3" onClick={() => setAddEscalationModal(true)}>
                        <IoMdAdd className="btn-icon-custom" />
                        Add{" "}
                    </span> */}
                </Col>
            </Row >
            <div className='card mt-5'>
                <div className='card-body p-0'>
                    <div className='table_div tab-view-table' style={{ width: '100%' }}>
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid
                                key={refreshKey}
                                data={orderBy(gridData, sort)}
                                skip={page.skip}
                                take={page.take}
                                total={totalCount}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: itemPerPage.map((item: any) => item.label),
                                    pageSizeValue: pageSizeValue,
                                }}

                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column
                                    title='Module'
                                    field='module'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.module })}
                                />
                                <Column
                                    title='Priority/Severity'
                                    field='priority'
                                    cell={(props) =>
                                        renderPriority({
                                            ...props,
                                            content: props.dataItem?.priority,
                                        })
                                    } />
                                <Column
                                    title='Grace Period'
                                    field='gracePeriod'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.gracePeriod })}
                                />
                                <Column
                                    title="Members"
                                    // width="140px"
                                    field="memberCount"
                                    headerClassName="center-header"
                                    className="text-center"
                                    cell={(props) => renderCounts({ ...props })}
                                />
                                <Column
                                    title='Active'
                                    field='active'
                                    cell={(props) =>
                                        renderswitch(
                                            props.dataItem.escalationId,
                                            props.dataItem
                                        )
                                    } />
                                <Column
                                    title='Action'
                                    width={"120px"}
                                    cell={(props) => renderaction({ ...props, content: props.dataItem.escalationId })}
                                />
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>
            <AddEsclationModal
                addEscalationModal={addEscalationModal}
                setAddEscalationModal={setAddEscalationModal}
                escalationId={escalationId}
                setEscalationId={setEscalationId}
                editData={editData}
                userData={userData}
                escalationGrid={escalationGrid}
                setLoading={setGridLoading}
                setShouldFetchGrid={setShouldFetchGrid}
                propertyId={propertyId}
            />

            <LogsModal
                setOpenLogsModal={setOpenLogsModal}
                openLogsModal={openLogsModal}
                setGetModule={setGetModule}
                getModule={getModule}
                propertyId={propertyId}
            />

            <ChangesLogsModal
                setOpenChangeLogsModal={setOpenChangeLogsModal}
                openChangeLogsModal={openChangeLogsModal}
                setGetModule={setGetModule}
                getModule={getModule}
            />

            <ViewEscalationUserModal
                viewMemberModal={viewMemberModal}
                setViewMemberModal={setViewMemberModal}
                escalationId={escalationId}
                setEscalationId={setEscalationId}
                viewUserData={viewUserData}
                setViewUserData={setViewUserData}
                setLoading={setGridLoading}
                propertyId={propertyId}
            />
        </>
    )
}

export default Escalation;