import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { useGetSurveyReportingSummaryMutation } from '../../../../apis/survaysAPI';
import { 
  SurveyResponseTableRow, 
  ActualApiSurveyReportingSummaryData 
} from '../types/chartTypes';
import {
  transformApiResponseToTableData,
  transformApiResponseToPieChartData,
  transformApiResponseToStackedBarChartData,
  PieChartData,
  StackedBarChartData
} from '../utils/responseDataTransformer';

// Define the shape of the context
interface SurveyDataContextType {
  // Raw API data
  rawApiData: ActualApiSurveyReportingSummaryData | null;
  // Transformed data for tables
  tableData: SurveyResponseTableRow[];
  // Transformed data for charts
  pieChartData: PieChartData[];
  stackedBarChartData: StackedBarChartData[];
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  // Functions
  fetchSurveyData: (surveyId: string) => Promise<void>;
  clearData: () => void;
}

// Create the context with undefined default value
const SurveyDataContext = createContext<SurveyDataContextType | undefined>(undefined);

interface SurveyDataProviderProps {
  children: ReactNode;
}

/**
 * Provider component that wraps the application to provide survey data
 */
export const SurveyDataProvider: React.FC<SurveyDataProviderProps> = ({ children }) => {
  // API mutation hook
  const [getSurveyReportingSummary, { isLoading }] = useGetSurveyReportingSummaryMutation();
  
  // State management
  const [rawApiData, setRawApiData] = useState<ActualApiSurveyReportingSummaryData | null>(null);
  const [tableData, setTableData] = useState<SurveyResponseTableRow[]>([]);
  const [pieChartData, setPieChartData] = useState<PieChartData[]>([]);
  const [stackedBarChartData, setStackedBarChartData] = useState<StackedBarChartData[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Fetch survey data function
  const fetchSurveyData = useCallback(async (surveyId: string) => {
    try {
      setError(null);
      const response = await getSurveyReportingSummary({ surveyId }).unwrap();
      const surveyData = response.data as unknown as ActualApiSurveyReportingSummaryData;
      
      // Update all state at once
      setRawApiData(surveyData);
      setTableData(transformApiResponseToTableData(surveyData));
      setPieChartData(transformApiResponseToPieChartData(surveyData));
      setStackedBarChartData(transformApiResponseToStackedBarChartData(surveyData));
    } catch (error: unknown) {
      console.error("Error fetching survey data:", error);
      const errorMessage = error && typeof error === 'object' && 'data' in error
        ? (error as { data?: { message?: string } }).data?.message || "Failed to load survey data"
        : "Failed to load survey data";
      
      // Reset all data on error
      setError(errorMessage);
      setRawApiData(null);
      setTableData([]);
      setPieChartData([]);
      setStackedBarChartData([]);
    }
  }, [getSurveyReportingSummary]);

  // Clear all data function
  const clearData = useCallback(() => {
    setRawApiData(null);
    setTableData([]);
    setPieChartData([]);
    setStackedBarChartData([]);
    setError(null);
  }, []);

  // Create memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    rawApiData,
    tableData,
    pieChartData,
    stackedBarChartData,
    isLoading,
    error,
    fetchSurveyData,
    clearData,
  }), [rawApiData, tableData, pieChartData, stackedBarChartData, isLoading, error, fetchSurveyData, clearData]);

  return (
    <SurveyDataContext.Provider value={contextValue}>
      {children}
    </SurveyDataContext.Provider>
  );
};

/**
 * Hook to access the survey data context
 * @throws Error if used outside of SurveyDataProvider
 */
export const useSurveyData = (): SurveyDataContextType => {
  const context = useContext(SurveyDataContext);
  if (context === undefined) {
    throw new Error('useSurveyData must be used within a SurveyDataProvider');
  }
  return context;
};

/**
 * Specialized hook for chart data that provides a simplified interface
 * for components that only need chart-related data
 */
export const useSurveyChartData = () => {
  const { pieChartData, stackedBarChartData, isLoading, error } = useSurveyData();

  // Get chart data by type
  const getChartData = useCallback((chartType: 'pie' | 'stackedBar') => {
    return chartType === 'pie' ? pieChartData : chartType === 'stackedBar' ? stackedBarChartData : [];
  }, [pieChartData, stackedBarChartData]);

  // Return memoized object with only chart-related properties
  return useMemo(() => ({
    pieChartData,
    stackedBarChartData,
    isLoading,
    error,
    getChartData,
    hasData: pieChartData.length > 0 || stackedBarChartData.length > 0
  }), [pieChartData, stackedBarChartData, isLoading, error, getChartData]);
};
