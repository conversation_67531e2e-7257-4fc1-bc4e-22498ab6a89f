import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { useGetSurveyReportingSummaryMutation } from '../../../../apis/survaysAPI';
import { 
  SurveyResponseTableRow, 
  ActualApiSurveyReportingSummaryData 
} from '../types/chartTypes';
import { transformApiResponseToTableData } from '../utils/responseDataTransformer';

interface SurveyDataContextType {
  // Raw API data
  rawApiData: ActualApiSurveyReportingSummaryData | null;
  
  // Transformed data for tables
  tableData: SurveyResponseTableRow[];
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  
  // Functions
  fetchSurveyData: (surveyId: string) => Promise<void>;
  clearData: () => void;
}

const SurveyDataContext = createContext<SurveyDataContextType | undefined>(undefined);

interface SurveyDataProviderProps {
  children: ReactNode;
}

export const SurveyDataProvider: React.FC<SurveyDataProviderProps> = ({ children }) => {
  const [getSurveyReportingSummary, { isLoading }] = useGetSurveyReportingSummaryMutation();
  
  // State
  const [rawApiData, setRawApiData] = useState<ActualApiSurveyReportingSummaryData | null>(null);
  const [tableData, setTableData] = useState<SurveyResponseTableRow[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch survey data - memoized to prevent infinite loops
  const fetchSurveyData = useCallback(async (surveyId: string) => {
    // console.log("🔄 fetchSurveyData called with surveyId:", surveyId);

    try {
      setError(null);
      // console.log("🔄 Fetching survey data for surveyId:", surveyId);

      const response = await getSurveyReportingSummary({ surveyId }).unwrap();
      // console.log("🔄 Raw API response:", response);

      const surveyData = response.data as unknown as ActualApiSurveyReportingSummaryData;
      // console.log("🔄 Extracted survey data:", surveyData);

      // Store raw data
      setRawApiData(surveyData);

      // Transform and store table data
      const transformedData = transformApiResponseToTableData(surveyData);
      // console.log("🔄 Transformed table data:", transformedData);
      setTableData(transformedData);

    } catch (error: unknown) {
      console.error("❌ Error fetching survey reporting summary:", error);
      const errorMessage = error && typeof error === 'object' && 'data' in error
        ? (error as { data?: { message?: string } }).data?.message || "Failed to load survey data"
        : "Failed to load survey data";
      setError(errorMessage);
      setRawApiData(null);
      setTableData([]);
    }
  }, [getSurveyReportingSummary]); // Only depend on the mutation function

  // Function to clear data - memoized to prevent unnecessary re-renders
  const clearData = useCallback(() => {
    // console.log("🔄 clearData called");
    setRawApiData(null);
    setTableData([]);
    setError(null);
  }, []);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: SurveyDataContextType = useMemo(() => ({
    rawApiData,
    tableData,
    isLoading,
    error,
    fetchSurveyData,
    clearData,
  }), [rawApiData, tableData, isLoading, error, fetchSurveyData, clearData]);

  return (
    <SurveyDataContext.Provider value={contextValue}>
      {children}
    </SurveyDataContext.Provider>
  );
};

// Custom hook to use the survey data context
export const useSurveyData = (): SurveyDataContextType => {
  const context = useContext(SurveyDataContext);
  if (context === undefined) {
    throw new Error('useSurveyData must be used within a SurveyDataProvider');
  }
  return context;
};

// Helper hook for chart data
export const useSurveyChartData = () => {
  const { rawApiData, tableData, isLoading, error } = useSurveyData();

  // Transform data for different chart types - memoized to prevent unnecessary recalculations
  const getChartData = useCallback((chartType: 'pie' | 'bar' | 'stackedBar') => {
    console.log("🔄 getChartData called for chartType:", chartType);

    if (!rawApiData || !rawApiData.responseDtos) {
      console.log("❌ No rawApiData or responseDtos available");
      return [];
    }

    switch (chartType) {
      case 'pie':
        // Return data suitable for pie charts
        return rawApiData.responseDtos.map(question => ({
          questionId: question.questionId,
          questionText: question.questinText,
          data: question.answerResponseDtos.map(answer => ({
            label: answer.answer,
            value: answer.count,
            percentage: answer.percentage
          }))
        }));

      case 'bar':
      case 'stackedBar':
        // Return data suitable for bar charts
        return rawApiData.responseDtos.map(question => ({
          questionId: question.questionId,
          questionText: question.questinText,
          categories: question.answerResponseDtos.map(answer => answer.answer),
          data: question.answerResponseDtos.map(answer => answer.count)
        }));

      default:
        return [];
    }
  }, [rawApiData]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    rawApiData,
    tableData,
    isLoading,
    error,
    getChartData,
  }), [rawApiData, tableData, isLoading, error, getChartData]);
};
