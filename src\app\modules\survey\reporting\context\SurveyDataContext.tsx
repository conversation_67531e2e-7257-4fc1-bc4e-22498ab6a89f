import React, { createContext, useContext, useState, useCallback, useMemo, ReactNode } from 'react';
import { useGetSurveyReportingSummaryMutation } from '../../../../apis/survaysAPI';
import { 
  SurveyResponseTableRow, 
  ActualApiSurveyReportingSummaryData 
} from '../types/chartTypes';
import {
  transformApiResponseToTableData,
  transformApiResponseToPieChartData,
  transformApiResponseToStackedBarChartData,
  PieChartData,
  StackedBarChartData
} from '../utils/responseDataTransformer';

interface SurveyDataContextType {
  // Raw API data
  rawApiData: ActualApiSurveyReportingSummaryData | null;

  // Transformed data for tables
  tableData: SurveyResponseTableRow[];

  // Transformed data for charts
  pieChartData: PieChartData[];
  stackedBarChartData: StackedBarChartData[];

  // Loading and error states
  isLoading: boolean;
  error: string | null;

  // Functions
  fetchSurveyData: (surveyId: string) => Promise<void>;
  clearData: () => void;
}

const SurveyDataContext = createContext<SurveyDataContextType | undefined>(undefined);

interface SurveyDataProviderProps {
  children: ReactNode;
}

export const SurveyDataProvider: React.FC<SurveyDataProviderProps> = ({ children }) => {
  const [getSurveyReportingSummary, { isLoading }] = useGetSurveyReportingSummaryMutation();
  
  // State
  const [rawApiData, setRawApiData] = useState<ActualApiSurveyReportingSummaryData | null>(null);
  const [tableData, setTableData] = useState<SurveyResponseTableRow[]>([]);
  const [pieChartData, setPieChartData] = useState<PieChartData[]>([]);
  const [stackedBarChartData, setStackedBarChartData] = useState<StackedBarChartData[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch survey data - memoized to prevent infinite loops
  const fetchSurveyData = useCallback(async (surveyId: string) => {
    // console.log("🔄 fetchSurveyData called with surveyId:", surveyId);

    try {
      setError(null);
      // console.log("🔄 Fetching survey data for surveyId:", surveyId);

      const response = await getSurveyReportingSummary({ surveyId }).unwrap();

      // 🔍 CRITICAL DEBUG: Log the exact API response structure
      console.log("🔍 RAW API RESPONSE STRUCTURE:");
      console.log("🔍 Full response:", response);
      console.log("🔍 response.data:", response.data);
      console.log("🔍 Type of response.data:", typeof response.data);
      console.log("🔍 Keys in response.data:", response.data ? Object.keys(response.data) : 'No data');

      const surveyData = response.data as unknown as ActualApiSurveyReportingSummaryData;

      // 🔍 CRITICAL DEBUG: Check if the API response has the expected structure
      console.log("🔍 SURVEY DATA STRUCTURE ANALYSIS:");
      console.log("🔍 surveyData:", surveyData);
      console.log("🔍 surveyData keys:", Object.keys(surveyData || {}));
      console.log("🔍 surveyData.responseDtos exists:", !!surveyData?.responseDtos);
      console.log("🔍 surveyData.questions exists:", !!(surveyData as any)?.questions);
      console.log("🔍 surveyData.responseDtos length:", surveyData?.responseDtos?.length || 0);
      console.log("🔍 surveyData.questions length:", (surveyData as any)?.questions?.length || 0);

      // Check if we're using the wrong property name
      if (!(surveyData as any)?.responseDtos && (surveyData as any)?.questions) {
        console.log("🚨 CRITICAL: API response uses 'questions' not 'responseDtos'!");
        console.log("🔍 First question structure:", (surveyData as any).questions[0]);

        // Check for MULTIPLE_CHOICE in the questions array
        const questionsArray = (surveyData as any).questions;
        const multipleChoiceInQuestions = questionsArray.filter((q: any) =>
          q.responseType?.toUpperCase() === 'MULTIPLE_CHOICE'
        );
        console.log("🔍 MULTIPLE_CHOICE in questions array:", multipleChoiceInQuestions.length);
        console.log("🔍 MULTIPLE_CHOICE details:", multipleChoiceInQuestions);
      }

      // Store raw data
      setRawApiData(surveyData);

      // Transform and store table data
      const transformedTableData = transformApiResponseToTableData(surveyData);
      setTableData(transformedTableData);

      // Transform and store chart data
      const transformedPieChartData = transformApiResponseToPieChartData(surveyData);
      setPieChartData(transformedPieChartData);

      const transformedStackedBarChartData = transformApiResponseToStackedBarChartData(surveyData);
      setStackedBarChartData(transformedStackedBarChartData);

      console.log("🔄 Chart data transformed:", {
        pieCharts: transformedPieChartData.length,
        stackedBarCharts: transformedStackedBarChartData.length
      });

      // Debug: Log question types being processed
      if (surveyData.responseDtos) {
        const questionTypes = surveyData.responseDtos.map(q => ({
          type: q.responseType,
          text: q.questinText,
          hasBranching: !!q.branchingQuestion,
          branchingType: q.branchingQuestion?.responseType,
          hasAnswers: q.answerResponseDtos && q.answerResponseDtos.length > 0,
          answerCount: q.answerResponseDtos?.length || 0
        }));
        console.log("🔍 Question types in survey:", questionTypes);

        // Specifically check for MULTIPLE_CHOICE questions
        const multipleChoiceQuestions = surveyData.responseDtos.filter(q =>
          q.responseType?.toUpperCase() === 'MULTIPLE_CHOICE'
        );
        console.log("🔍 MULTIPLE_CHOICE questions found:", multipleChoiceQuestions.length);
        console.log("🔍 MULTIPLE_CHOICE questions details:", multipleChoiceQuestions.map(q => ({
          text: q.questinText,
          answers: q.answerResponseDtos?.length || 0,
          answerDetails: q.answerResponseDtos
        })));
      }

    } catch (error: unknown) {
      console.error("❌ Error fetching survey reporting summary:", error);
      const errorMessage = error && typeof error === 'object' && 'data' in error
        ? (error as { data?: { message?: string } }).data?.message || "Failed to load survey data"
        : "Failed to load survey data";
      setError(errorMessage);
      setRawApiData(null);
      setTableData([]);
      setPieChartData([]);
      setStackedBarChartData([]);
    }
  }, [getSurveyReportingSummary]); // Only depend on the mutation function

  // Function to clear data - memoized to prevent unnecessary re-renders
  const clearData = useCallback(() => {
    // console.log("🔄 clearData called");
    setRawApiData(null);
    setTableData([]);
    setPieChartData([]);
    setStackedBarChartData([]);
    setError(null);
  }, []);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: SurveyDataContextType = useMemo(() => ({
    rawApiData,
    tableData,
    pieChartData,
    stackedBarChartData,
    isLoading,
    error,
    fetchSurveyData,
    clearData,
  }), [rawApiData, tableData, pieChartData, stackedBarChartData, isLoading, error, fetchSurveyData, clearData]);

  return (
    <SurveyDataContext.Provider value={contextValue}>
      {children}
    </SurveyDataContext.Provider>
  );
};

// Custom hook to use the survey data context
export const useSurveyData = (): SurveyDataContextType => {
  const context = useContext(SurveyDataContext);
  if (context === undefined) {
    throw new Error('useSurveyData must be used within a SurveyDataProvider');
  }
  return context;
};

// Helper hook for chart data
export const useSurveyChartData = () => {
  const { pieChartData, stackedBarChartData, isLoading, error } = useSurveyData();

  // Get chart data by type - memoized to prevent unnecessary recalculations
  const getChartData = useCallback((chartType: 'pie' | 'stackedBar') => {
    console.log("🔄 getChartData called for chartType:", chartType);

    switch (chartType) {
      case 'pie':
        return pieChartData;
      case 'stackedBar':
        return stackedBarChartData;
      default:
        return [];
    }
  }, [pieChartData, stackedBarChartData]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    pieChartData,
    stackedBarChartData,
    isLoading,
    error,
    getChartData,
    hasData: pieChartData.length > 0 || stackedBarChartData.length > 0
  }), [pieChartData, stackedBarChartData, isLoading, error, getChartData]);
};
