import React from "react";
import { MdOutlineInfo } from "react-icons/md";
import { Card } from "react-bootstrap";
import userimage from "../../../../../../efive_assets/images/user.jpg";
import { BsPrinterFill } from "react-icons/bs";

export const Postworkinspection = () => {
  return (
    <>
      <div className="d-flex flex-end mb-2">
        <div className="notification">
          <span className="text-muted cursor-pointer">
            <MdOutlineInfo size={15} />
          </span>
          <span className="tooltip-text">
            Each item on this list should be checked to confirm that post-work
            safety procedures have been followed.
          </span>
        </div>
      </div>
      <div className="inspection">
        <div className="inspection-report-details">
          <Card className="mb-2 custom-card cursor-pointer">
            <Card.Body className="p-2 mx-3">
              <div className="title-section d-flex justify-content-between mb-3">
                <div className="title">
                  <span className="fs-5">Inspection Report</span>
                </div>
                <div className="inspection-report-print">
                  <div className="print-report">
                    <span>
                      <BsPrinterFill size={20} className="mx-2" />
                    </span>
                    <span className="fs-5">Print Report</span>
                  </div>
                </div>
              </div>
              <div className="inspection-content">
                {/* Area Inspection */}
                <div className="title mt-8">
                  <span className="">Area Inspection</span>
                </div>
                <div className="inspection-content-conditionn d-flex align-items-center mt-2">
                  <span>
                    <input type="checkbox" />
                  </span>
                  <span className="mx-2">
                    Complete a final inspection of the Red Tag area to ensure it
                    is free from hazards.
                  </span>
                </div>

                {/* Fire Watch */}
                <div className="title mt-8">
                  <span className="">Fire Watch</span>
                </div>
                <div className="inspection-content-conditionn  d-flex align-items-center mt-2">
                  <span>
                    <input type="checkbox" />
                  </span>
                  <span className="mx-2">
                    Confirm that all fire extinguishers and fire fighting
                    equipment used are returned and recharged if necessary.
                  </span>
                </div>

                {/* Equipment and Material Safety */}
                <div className="title mt-8">
                  <span className="">Equipment and Material Safety</span>
                </div>
                <div className="inspection-content-conditionn  d-flex align-items-center mt-2">
                  <span>
                    <input type="checkbox" />
                  </span>
                  <span className="mx-2">
                    Confirm that all fire extinguishers and firefighting
                    equipment used are returned and recharged if necessary.
                  </span>
                </div>

                {/* Personnel Verification */}
                <div className="title mt-8">
                  <span className="">Personnel Verification</span>
                </div>
                <div className="inspection-content-conditionn  d-flex align-items-center mt-2">
                  <span>
                    <input type="checkbox" />
                  </span>
                  <span className="mx-2">
                    Verify the presence and actions of the fire watch personnel
                    during and after the hot work.
                  </span>
                </div>

                {/* Final Check and Documentation */}
                <div className="title mt-8">
                  <span className="">Final Check and Documentation</span>
                </div>
                <div className="inspection-content-conditionn d-flex align-items-center mt-2">
                  <span>
                    <input type="checkbox" />
                  </span>
                  <span className="mx-2">
                    Complete a final inspection of the Red Tag area to ensure it
                    is free from hazards.
                  </span>
                </div>
              </div>
            </Card.Body>
          </Card>
        </div>
        <div className="inspected-section">
          <Card className="mb-2 custom-card cursor-pointer">
            <Card.Body className="p-2">
              <div className="title mt-2">
                <span className="mx-3 mb-3 fs-5">Inspected by</span>
              </div>
              <div className="d-flex align-items-center justify-content-between gap-2 mt-3">
                <div className="d-flex align-items-center gap-5">
                  <div>
                    <img
                      src={userimage}
                      alt="uselogo"
                      height={"48px"}
                      width={"48px"}
                      className="rounded-circle"
                    />
                  </div>
                  <div className="mt-3">
                    <span>Eleanor Pena</span>
                    <p className="text-muted ">Check in at 5:17 PM</p>
                  </div>
                </div>
                <div className="time d-flex flex-column flex-end">
                  <span>Ralph Edwards</span>
                  <span className="text-muted">March 07 2024, 8:00 AM</span>
                </div>
              </div>
            </Card.Body>
          </Card>
        </div>
      </div>
    </>
  );
};
