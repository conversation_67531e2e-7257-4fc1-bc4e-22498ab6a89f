import React from "react";
import { Card, Col, Row } from "react-bootstrap";
import { MdArrowDownward } from "react-icons/md";

import { BsPrinterFill } from "react-icons/bs";
import { IoMdMore } from "react-icons/io";
import Componentsvg from "../../../../../efive_assets/images/Component 64.svg";

export const ViewPermit = () => {
  return (
    <>
      <Row>
        <Col sm={6}>
          <span className="title fs-2">Permit details</span>
        </Col>
        <Col sm={6} className="text-end view-permit">
          <span className="mx-3">
            <button className="btn rx-btn mt-3 mt-md-0 rounded-2">
              <span>
                <MdArrowDownward size={20} />
              </span>
              Download
            </button>
          </span>
          <span className="mx-3">
            <button className="btn rx-btn  mt-3 mt-md-0 rounded-2">
              <span className="mx-2">
                <BsPrinterFill size={20} />
              </span>
              Print Permit
            </button>
          </span>
        </Col>
      </Row>
      <Row>
        <Col sm={12} className="d-flex justify-content-center mt-3">
          <Card
            className="  mb-2 custom-card cursor-pointer mt-3"
            style={{ width: "80%", height: "450px" }}
          >
            <Card.Body className="p-2">
              <div className="d-flex align-items-center justify-content-around gap-2">
                <div className="d-flex align-items-center   gap-5">
                  <div className="mt-3">
                    <span className="fs-2">Hot Work Permit</span>
                    <p className="text-muted fs-6">Amgen</p>
                  </div>
                </div>
                <div className="time d-flex flex-end">
                  <span className="mb-1">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_6443_46352)">
                        <path
                          d="M18.581 2.14024L12.316 0.0512408C12.1109 -0.0170803 11.8891 -0.0170803 11.684 0.0512408L5.419 2.14024C4.42291 2.47112 3.55642 3.1075 2.94265 3.95895C2.32887 4.8104 1.99904 5.83363 2 6.88324V12.0002C2 19.5632 11.2 23.7402 11.594 23.9142C11.7218 23.971 11.8601 24.0004 12 24.0004C12.1399 24.0004 12.2782 23.971 12.406 23.9142C12.8 23.7402 22 19.5632 22 12.0002V6.88324C22.001 5.83363 21.6711 4.8104 21.0574 3.95895C20.4436 3.1075 19.5771 2.47112 18.581 2.14024ZM20 12.0002C20 17.4552 13.681 21.0332 12 21.8892C10.317 21.0362 4 17.4692 4 12.0002V6.88324C4.00006 6.25352 4.19828 5.63978 4.56657 5.12898C4.93486 4.61819 5.45455 4.23623 6.052 4.03724L12 2.05424L17.948 4.03724C18.5455 4.23623 19.0651 4.61819 19.4334 5.12898C19.8017 5.63978 19.9999 6.25352 20 6.88324V12.0002Z"
                          fill="white"
                        />
                        <path
                          d="M15.3 8.30105L11.112 12.5011L8.86804 10.1611C8.77798 10.0626 8.66913 9.98314 8.5479 9.9274C8.42666 9.87165 8.2955 9.84073 8.16213 9.83646C8.02877 9.83219 7.89589 9.85466 7.77134 9.90253C7.64679 9.95041 7.53308 10.0227 7.43691 10.1152C7.34074 10.2077 7.26405 10.3185 7.21137 10.4411C7.15869 10.5637 7.13107 10.6956 7.13015 10.8291C7.12923 10.9625 7.15502 11.0948 7.20601 11.2181C7.257 11.3414 7.33215 11.4532 7.42704 11.5471L9.73304 13.9471C9.90501 14.1328 10.1129 14.2817 10.3441 14.3849C10.5752 14.488 10.8249 14.5432 11.078 14.5471H11.111C11.3591 14.5479 11.6048 14.4994 11.834 14.4045C12.0632 14.3095 12.2712 14.17 12.446 13.9941L16.718 9.72205C16.8113 9.62894 16.8854 9.51837 16.936 9.39665C16.9865 9.27492 17.0126 9.14442 17.0128 9.01261C17.0129 8.8808 16.9871 8.75025 16.9368 8.62842C16.8865 8.50659 16.8126 8.39586 16.7195 8.30255C16.6264 8.20925 16.5159 8.1352 16.3941 8.08462C16.2724 8.03405 16.1419 8.00795 16.0101 8.00781C15.8783 8.00767 15.7477 8.0335 15.6259 8.08381C15.5041 8.13413 15.3933 8.20794 15.3 8.30105Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_6443_46352">
                          <rect width="24" height="24" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                  <span className="fs-3 mx-1">HW3829</span>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </>
  );
};
