import html2canvas from "html2canvas";
import QRCodeStyling from "qr-code-styling";
import { useEffect, useRef, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import { SlClose } from "react-icons/sl";
import logo from "../../../efive_assets/images/bhm_dark.png";
import { FaDownload } from "react-icons/fa";
import { TfiPrinter } from "react-icons/tfi";
import SwalMessage from "../common/SwalMessage";
import { Loader } from "../../component";

function QRScreen({ qrBase64, title }: any) {
  const [dotcolor, setdotcolor] = useState("black");
  const [gridLoading, setGridLoading] = useState(false);
  const [bgcolor, setbgcolor] = useState("white");
  const [cornersquare, setcornersquare] = useState("black");
  const [cornerdot, setcornerdot] = useState("black");
  const [qrsize, setqrsize] = useState({ width: 200, height: 200 });

  const divRef = useRef<HTMLDivElement>(null);

  const handleDownload = () => {
    if (divRef.current) {
      setGridLoading(true);
      html2canvas(divRef.current)
        .then((canvas) => {
          const dataURL = canvas.toDataURL("image/png");
          const link = document.createElement("a");
          link.href = dataURL;
          link.download = "image.png";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        })
        .finally(() => {
          setGridLoading(false);
        });
    }
  };

  const handlePrint = () => {
    if (divRef.current) {
      setGridLoading(true);
      html2canvas(divRef.current)
        .then((canvas) => {
          const imageData = canvas.toDataURL("image/png");
          const printWindow = window.open();

          if (printWindow) {
            printWindow.document.open();
            printWindow.document.write(`<html><head>
              <style> @page {size: landscape; margin: 0; }
                  body {display: flex;justify-content: center;align-items: center;height: 100vh;margin: 0;}
                  img {max-width: 100%;max-height: 100%;}
                </style>
              </head>
              <body><img src="${imageData}" /></body>
            </html>
          `);

            printWindow.document.close();

            printWindow.onload = function () {
              printWindow.focus();
              printWindow.print();
              printWindow.close();
            };
          }
        })
        .finally(() => {
          setGridLoading(false);
        });
    }
  };
  const rgbToColorDistance = (rgb1: string, rgb2: string) => {
    const extractRGB = (color: string) => {
      const bigint = parseInt(color.slice(1), 16);
      return {
        r: (bigint >> 16) & 255,
        g: (bigint >> 8) & 255,
        b: bigint & 255,
      };
    };

    const c1 = extractRGB(rgb1);
    const c2 = extractRGB(rgb2);

    return Math.sqrt(
      Math.pow(c2.r - c1.r, 2) +
        Math.pow(c2.g - c1.g, 2) +
        Math.pow(c2.b - c1.b, 2)
    );
  };

  const handleColorChange = (colorType: string, value: string) => {
    const colorPairs = [
      { type: "dotcolor", color: dotcolor },
      { type: "bgcolor", color: bgcolor },
      { type: "cornersquare", color: cornersquare },
      { type: "cornerdot", color: cornerdot },
    ];

    for (let pair of colorPairs) {
      if (
        pair.type !== colorType &&
        rgbToColorDistance(pair.color, value) < 60
      ) {
        // adjust the threshold as needed
        // Swal.fire({
        //   icon: "error",
        //   title: "Oops...",
        //   text: "Please select different color, Otherwise QRCode Can't be scan.",
        // });
        SwalMessage(
          "Oops...",
          "Please select different color, Otherwise QRCode Can't be scan",
          "Ok",
          "error",
          false
        );
        return;
      }
    }

    switch (colorType) {
      case "dotcolor":
        setdotcolor(value);
        break;
      case "bgcolor":
        setbgcolor(value);
        break;
      case "cornersquare":
        setcornersquare(value);
        break;
      case "cornerdot":
        setcornerdot(value);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (qrBase64) {
      const qrCodeInstance = new QRCodeStyling({
        width: qrsize.width,
        height: qrsize.height,
        type: "canvas",
        data: qrBase64
          ? qrBase64.includes("UserId")
            ? qrBase64
            : atob(qrBase64)
          : "No Data Available",
        image: logo,
        dotsOptions: {
          color: dotcolor,
          type: "rounded",
        },
        backgroundOptions: {
          color: bgcolor,
        },
        cornersSquareOptions: {
          color: cornersquare,
        },
        cornersDotOptions: {
          color: cornerdot,
        },
      });

      qrCodeInstance.append(document.getElementById("qrCode") as HTMLElement);
      qrCodeInstance.update({
        width: qrsize.width,
        height: qrsize.height,
        dotsOptions: {
          color: dotcolor,
        },
        backgroundOptions: {
          color: bgcolor,
        },
        cornersSquareOptions: {
          color: cornersquare,
        },
        cornersDotOptions: {
          color: cornerdot,
        },
      });
    }
  }, [qrBase64, dotcolor, bgcolor, cornerdot, cornersquare, qrsize]);

  useEffect(() => {
    setdotcolor("black");
    setbgcolor("white");
    setcornerdot("black");
    setcornersquare("black");
  }, [qrBase64]);

  return (
    <div className="qr-modal-screen-bg card custom-card">
      {gridLoading && <Loader />}
      <div className="card-body">
        {/* Top Center QR */}
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "flex-start",
          }}
        >
          <div ref={divRef} style={{ marginBottom: 16 }}>
            <div
              id="qrCode"
              style={{
                background: "#fff",
                borderRadius: "8px",
                display: "inline-block",
                padding: 8,
              }}
            ></div>
          </div>
        </div>
        {/* Bottom Row: left and right */}
        <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-end",
            marginTop: 32,
          }}
        >
          {/* Bottom-left: Size selection and Download/Share */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
            }}
          >
            <div className="mb-2" style={{ fontWeight: 600 }}>
              Select your size:
            </div>
            <div className="d-flex flex-column">
              <div className="card mb-4">
                <div className="d-flex gap-2 p-2 card-body">
                  <button
                    className="btn btn-light"
                    style={{ minWidth: 80 }}
                    onClick={() => setqrsize({ width: 200, height: 200 })}
                  >
                    Small
                  </button>
                  <button
                    className="btn btn-light"
                    style={{ minWidth: 80 }}
                    onClick={() => setqrsize({ width: 300, height: 300 })}
                  >
                    Medium
                  </button>
                  <button
                    className="btn btn-light"
                    style={{ minWidth: 80 }}
                    onClick={() => setqrsize({ width: 390, height: 390 })}
                  >
                    Large
                  </button>
                </div>
              </div>
              <div className="d-flex gap-2 w-100 justify-content-between">
                <button
                  className="btn btn-dark"
                  onClick={handleDownload}
                  style={{ minWidth: 100 }}
                >
                  Download
                </button>
                <button
                  className="btn btn-dark"
                  onClick={handlePrint}
                  style={{ minWidth: 100 }}
                >
                  Share
                </button>
              </div>
            </div>
          </div>

          {/* Bottom-right: Color pickers */}
          <div>
            <div className="d-flex align-items-center mb-2">
              <span
                style={{
                  display: "inline-block",
                  minWidth: 140,
                  fontWeight: 600,
                }}
              >
                Description:
              </span>
              <span
                style={{
                  display: "inline-block",
                  minWidth: 140,
                  fontWeight: 600,
                }}
              >
                Color:
              </span>
            </div>
            <div className="w-100">
              <div className="d-flex align-items-center mb-2">
                <span style={{ display: "inline-block", minWidth: 140 }}>
                  Dot Color:
                </span>
                <Form.Control
                  type="color"
                  style={{ width: 32, height: 32, marginLeft: 8 }}
                  value={dotcolor}
                  onChange={(e) =>
                    handleColorChange("dotcolor", e.target.value)
                  }
                />
              </div>
              <div className="d-flex align-items-center mb-2">
                <span style={{ display: "inline-block", minWidth: 140 }}>
                  Background Color:
                </span>
                <Form.Control
                  type="color"
                  style={{ width: 32, height: 32, marginLeft: 8 }}
                  value={bgcolor}
                  onChange={(e) => handleColorChange("bgcolor", e.target.value)}
                />
              </div>
              <div className="d-flex align-items-center mb-2">
                <span style={{ display: "inline-block", minWidth: 140 }}>
                  Corner Square Color:
                </span>
                <Form.Control
                  type="color"
                  style={{ width: 32, height: 32, marginLeft: 8 }}
                  value={cornersquare}
                  onChange={(e) =>
                    handleColorChange("cornersquare", e.target.value)
                  }
                />
              </div>
              <div className="d-flex align-items-center mb-2">
                <span style={{ display: "inline-block", minWidth: 140 }}>
                  Corner Dot Color:
                </span>
                <Form.Control
                  type="color"
                  style={{ width: 32, height: 32, marginLeft: 8 }}
                  value={cornerdot}
                  onChange={(e) =>
                    handleColorChange("cornerdot", e.target.value)
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default QRScreen;
