import React from "react";
import { Mo<PERSON>, Row, Col } from "react-bootstrap";
import { FaArrowLeft } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import { FilterModalProps } from "../../types/chartTypes";

interface BaseFilterModalProps extends FilterModalProps {
  title: string;
  children: React.ReactNode;
  showApplyButton?: boolean;
  showResetButton?: boolean;
  onReset?: () => void;
}

const BaseFilterModal: React.FC<BaseFilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  title,
  children,
  showApplyButton = true,
  showResetButton = true,
  onReset,
}) => {
  const handleApply = () => {
    onApply({});
    onClose();
  };

  const handleReset = () => {
    if (onReset) {
      onReset();
    }
    onApply({});
  };

  return (
    <Modal
      className="modal-right modal-right-small p-0 filterModal"
      scrollable={true}
      show={isOpen}
      onHide={onClose}
    >
      <Modal.Header className="border-0 p-0">
        <Row className="align-items-baseline w-100">
          <Col xs={10} className="mt-auto mb-auto">
            <h2 className="mb-0">
              <span className="me-3">
                <FaArrowLeft
                  className="cursor-pointer"
                  onClick={onClose}
                />
              </span>
              {title}
            </h2>
          </Col>
          <Col xs={2} className="text-end mb-3">
            <span
              className="close-btn cursor-pointer"
              onClick={onClose}
            >
              <RxCross2 fontSize={20} />
            </span>
          </Col>
        </Row>
      </Modal.Header>
      
      <Modal.Body className="p-0">
        <Row className="mt-5">
          {children}
        </Row>
      </Modal.Body>
      
      <Modal.Footer className="border-0 p-0 d-flex justify-content-end gap-3">
        {showResetButton && (
          <span
            className="btn rx-btn-outline"
            onClick={handleReset}
          >
            Reset
          </span>
        )}
        {showApplyButton && (
          <span
            className="btn rx-btn"
            onClick={handleApply}
          >
            Apply
          </span>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default BaseFilterModal;
