import React from "react";
import { BiChevronRight } from "react-icons/bi";
import Select, {
  components,
  Props as SelectProps,
  OptionProps,
  StylesConfig,
} from "react-select";
import { QUESTION_TYPE_OPTIONS } from "./util/constant";
import FormSelect from "../../../../component/form/FormSelect";

// Custom Option Component
const CustomOption = (props: OptionProps<any>) => {
  const { data, isSelected, innerRef, innerProps, selectProps, isFocused } =
    props;

  const optionsLength = selectProps.options.length;
  const isLast = selectProps.options[optionsLength - 1].value === data.value;
  const highlightStyle = isSelected
    ? { background: "#e6f0ff", color: "#030303" }
    : {};
  return (
    <div
      ref={innerRef}
      {...innerProps}
      style={{
        ...highlightStyle,
      }}
      className="response-type-option"
    >
      <div
        style={{
          display: "flex",
          alignItems: "center",
          padding: "8px 12px",
          cursor: "pointer",
          margin: "0 8px",
          justifyContent: "space-between",
          // borderBottom: isSelected || isLast ? "none" : "1px solid currentColor",
        }}
      >
        <span>{data.label}</span>
        <BiChevronRight size={16} />
      </div>
      <div className="px-1">{!isLast && <hr className="m-0" />}</div>
    </div>
  );
};

// Custom styles for react-select
const customStyles: StylesConfig = {
  menuList: (base) => ({
    ...base,
    maxHeight: 300, // <- Set height (adjust as needed)
    overflowY: "auto",
  }),
  menu: (base) => ({
    ...base,
    zIndex: 9999, // helpful if dropdown is hidden behind other elements
  }),
};

// Custom Select Component
const ResponseTypeDropdown: React.FC<{ name: string, onChange?: () => void }> = ({ name, onChange }) => {
  return (
    <FormSelect
      name={name}
      options={QUESTION_TYPE_OPTIONS}
      components={{ Option: CustomOption }}
      placeholder="Select Question Type"
      styles={customStyles}
      onChange={onChange}
      // {...props}
    />
  );
};

export default ResponseTypeDropdown;
