import html2canvas from "html2canvas";
import QRCodeStyling from "qr-code-styling";
import { useEffect, useRef, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import { SlClose } from "react-icons/sl";
import logo from "../../../../../efive_assets/images/bhm_dark.png";
import { FaDownload } from "react-icons/fa";
import { TfiPrinter } from "react-icons/tfi";
import Swal from "sweetalert2";
import SwalMessage from "../../../common/SwalMessage";

function HotWorkQrModal({
    qrmodal,
    setqrmodal,
    qrBase64,
    userdetail,
    setGridLoading,
    title,
}: any) {
    const [qrCode, setQrCode] = useState<any>(null);
    const [dotcolor, setdotcolor] = useState("black");
    const [bgcolor, setbgcolor] = useState("white");
    const [cornersquare, setcornersquare] = useState("black");
    const [cornerdot, setcornerdot] = useState("black");
    const [qrsize, setqrsize] = useState({ width: 200, height: 200 });

    const divRef = useRef<HTMLDivElement>(null);

    const handleDownload = () => {
        if (divRef.current) {
            setGridLoading(true);
            html2canvas(divRef.current)
                .then((canvas) => {
                    const dataURL = canvas.toDataURL("image/png");
                    const link = document.createElement("a");
                    link.href = dataURL;
                    link.download = "image.png";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .finally(() => {
                    setGridLoading(false);
                });
        }
    };

    const handlePrint = () => {
        setGridLoading(true);
        if (divRef.current) {
            html2canvas(divRef.current)
                .then((canvas) => {
                    const imageData = canvas.toDataURL("image/png");
                    const printWindow = window.open();

                    if (printWindow) {
                        printWindow.document.open();
                        printWindow.document.write(`<html><head>
                            <style> @page {size: landscape; margin: 0; }
                                body {display: flex;justify-content: center;align-items: center;height: 100vh;margin: 0;}
                                img {max-width: 100%;max-height: 100%;}
                                </style>
                            </head>
                            <body><img src="${imageData}" /></body>
                            </html>
                        `);
                        printWindow.document.close();

                        printWindow.onload = function () {
                            printWindow.focus();
                            printWindow.print();
                            printWindow.close();
                        };
                    }
                })
                .finally(() => {
                    setGridLoading(false);
                });
        }
    };
    const rgbToColorDistance = (rgb1: string, rgb2: string) => {
        const extractRGB = (color: string) => {
            const bigint = parseInt(color.slice(1), 16);
            return {
                r: (bigint >> 16) & 255,
                g: (bigint >> 8) & 255,
                b: bigint & 255,
            };
        };

        const c1 = extractRGB(rgb1);
        const c2 = extractRGB(rgb2);

        return Math.sqrt(
            Math.pow(c2.r - c1.r, 2) +
            Math.pow(c2.g - c1.g, 2) +
            Math.pow(c2.b - c1.b, 2)
        );
    };

    const handleColorChange = (colorType: string, value: string) => {
        const colorPairs = [
            { type: "dotcolor", color: dotcolor },
            { type: "bgcolor", color: bgcolor },
            { type: "cornersquare", color: cornersquare },
            { type: "cornerdot", color: cornerdot },
        ];

        for (let pair of colorPairs) {
            if (
                pair.type !== colorType &&
                rgbToColorDistance(pair.color, value) < 60
            ) {
                // adjust the threshold as needed
                // Swal.fire({
                //   icon: "error",
                //   title: "Oops...",
                //   text: "Please select different color, Otherwise QRCode Can't be scan.",
                // });
                SwalMessage(
                    "Oops...",
                    "Please select different color, Otherwise QRCode Can't be scan",
                    "Ok",
                    "error",
                    false
                );
                return;
            }
        }

        switch (colorType) {
            case "dotcolor":
                setdotcolor(value);
                break;
            case "bgcolor":
                setbgcolor(value);
                break;
            case "cornersquare":
                setcornersquare(value);
                break;
            case "cornerdot":
                setcornerdot(value);
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        if (qrmodal) {
            const qrCodeInstance = new QRCodeStyling({
                width: qrsize.width,
                height: qrsize.height,
                type: "canvas",
                data: qrBase64
                    ? qrBase64.includes("UserId")
                        ? qrBase64
                        : atob(qrBase64)
                    : "No Data Available",
                image: logo,
                dotsOptions: {
                    color: dotcolor,
                    type: "rounded",
                },
                backgroundOptions: {
                    color: bgcolor,
                },
                cornersSquareOptions: {
                    color: cornersquare,
                },
                cornersDotOptions: {
                    color: cornerdot,
                },
            });

            qrCodeInstance.append(document.getElementById("qrCode") as HTMLElement);
            qrCodeInstance.update({
                width: qrsize.width,
                height: qrsize.height,
                dotsOptions: {
                    color: dotcolor,
                },
                backgroundOptions: {
                    color: bgcolor,
                },
                cornersSquareOptions: {
                    color: cornersquare,
                },
                cornersDotOptions: {
                    color: cornerdot,
                },
            });
        }
    }, [qrmodal, dotcolor, bgcolor, cornerdot, cornersquare, qrsize]);

    useEffect(() => {
        setdotcolor("black");
        setbgcolor("white");
        setcornerdot("black");
        setcornersquare("black");
    }, [qrmodal]);

    return (
        <Modal className="modal-right" scrollable={true} show={qrmodal}>
            <Modal.Header className="p-0">
                <Row>
                    <Col xs={6} className="mt-auto mb-auto">
                        <h2 className="mb-0">{title || "DOWNLOAD LOTO QR CODE"}</h2>
                    </Col>
                    <Col xs={6} className=" text-end mb-3">
                        {/* <Link to={''} className="btn rx-btn me-6 " onClick={handleSubmit} ><BsSave className='btn-icon-custom' />Save</Link> */}
                        <span className="btn rx-btn " onClick={() => setqrmodal(false)}>
                            <SlClose className="btn-icon-custom" />
                            Close
                        </span>
                    </Col>
                </Row>
            </Modal.Header>
            <Modal.Body className="p-0">
                <Row className="mt-5">
                    <Col xs={8}>
                        <a
                            className="btn rx-btn mobile-margin me-3"
                            onClick={() => setqrsize({ width: 200, height: 200 })}
                        >
                            Small
                        </a>
                        <a
                            className="btn rx-btn me-3 mobile-margin"
                            onClick={() => setqrsize({ width: 300, height: 300 })}
                        >
                            Medium
                        </a>
                        <a
                            className="btn rx-btn mobile-margin"
                            onClick={() => setqrsize({ width: 390, height: 390 })}
                        >
                            Large
                        </a>
                    </Col>
                    <Col xs={4} className="text-end">
                        <a className="btn rx-btn mx-1 mobile-margin" onClick={handlePrint}>
                            <TfiPrinter size={18} />
                        </a>
                        <a className="btn rx-btn" onClick={handleDownload}>
                            {/* Download QR Code */}
                            <FaDownload size={15} />
                        </a>
                    </Col>
                </Row>
                <Row className="mt-5" ref={divRef}>
                    <span className="text-center fs-4">{userdetail?.name}</span>
                    <Col xs={12} className="text-center" id="qrCode"></Col>
                </Row>
                <Row className="mt-5">
                    <Col xs={6} md={6} lg={6} className="d-flex align-items-center gap-2">
                        <Form.Label style={{ width: 180 }} className="mb-0 mt-2">
                            Dot Color
                        </Form.Label>
                        <span className="pe-2">:</span>
                        <Form.Control
                            type="color"
                            className="mt-0"
                            onChange={(e) => handleColorChange("dotcolor", e.target.value)}
                        />
                    </Col>
                    <Col xs={6} md={6} lg={6} className="d-flex align-items-center gap-2">
                        <Form.Label style={{ width: 180 }} className="mb-0 mt-2">
                            Background Color
                        </Form.Label>
                        <span className="pe-2">:</span>
                        <Form.Control
                            type="color"
                            className="mt-0"
                            onChange={(e) => handleColorChange("bgcolor", e.target.value)}
                        />
                    </Col>
                </Row>
                <Row className="mt-3">
                    <Col xs={6} md={6} lg={6} className="d-flex align-items-center gap-2">
                        <Form.Label style={{ width: 180 }} className="mb-0 mt-2">
                            Corner Square Color
                        </Form.Label>
                        <span className="pe-2">:</span>
                        <Form.Control
                            type="color"
                            className="mt-0"
                            onChange={(e) =>
                                handleColorChange("cornersquare", e.target.value)
                            }
                        />
                    </Col>
                    <Col xs={6} md={6} lg={6} className="d-flex align-items-center gap-2">
                        <Form.Label style={{ width: 180 }} className="mb-0 mt-2">
                            Corner Dot Color
                        </Form.Label>
                        <span className="pe-2">:</span>
                        <Form.Control
                            type="color"
                            className="mt-0"
                            onChange={(e) => handleColorChange("cornerdot", e.target.value)}
                        />
                    </Col>
                </Row>
            </Modal.Body>
        </Modal>
    );
}

export default HotWorkQrModal;
