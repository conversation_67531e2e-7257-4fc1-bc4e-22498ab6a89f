import { FC } from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import { MasterLayout } from '../../_metronic/layout/MasterLayout';
import App from '../App';
import ForgotPassword from '../modules/authentication/ForgotPassword';
import GetStarted from '../modules/authentication/GetStarted';
import Login from '../modules/authentication/Login';
import SetNewPassword from '../modules/authentication/SetNewPassword';
import TermsAndConditions from '../modules/authentication/TermsAndConditions';
import TwoFactorAuthentication from '../modules/authentication/TwoFactorAuthentication';
import TwoFactorEmailAuthentication from '../modules/authentication/TwoFactorEmailAuthentication';
import RouteTracker, { AdminAuthenticater, SuperAdminAuthenticater } from '../modules/authentication/UserTypeAuthenticator';
import Work_Progress from '../modules/common/Work_Progress';
import { Error404 } from '../modules/errors/components/Error404';
import UserMasterKendo from '../modules/Masters/AdminUser/UserMasterKendo';
import Company_master from '../modules/Masters/Company/CompanyMasterKendo';
import Escalation from '../modules/Masters/Configurations/Escalation/Escalation';
import Permission from '../modules/Masters/Configurations/Permisssion/Permisssion';
import Dashboard from '../modules/Masters/Dashboard/Dashboard';
import Add_Department from '../modules/Masters/Departments/Add_Department/Add_Department';
import Departments from '../modules/Masters/Departments/Departments';
import { ViewDepartment } from '../modules/Masters/Departments/ViewDepartment/ViewDepartment';
import GooseChatAction from '../modules/Masters/GooseChatAction/GooseChatAction';
import GooseChatAgents from '../modules/Masters/GooseChatAgents/GooseChatAgents';
import GooseChatConfigurations from '../modules/Masters/GooseChatConfigurations/GooseChatConfigurations';
import GooseChatFAQs from '../modules/Masters/GooseChatFAQs/GooseChatFAQs';
import LoopVendor from '../modules/Masters/LoopVendors/LoopVendor';
import SuperAdminLoopVendor from '../modules/Masters/LoopVendors/SuperAdminLoopVendor';
import { AddProperty } from '../modules/Masters/Properties/AddProperty';
import Properties from '../modules/Masters/Properties/Properties';
import { ViewProperty } from '../modules/Masters/Properties/ViewProperty';
import { SafetyForms } from '../modules/Masters/SafetyForms/SafetyForms';
import AddTicket from '../modules/Masters/Ticktes/AddTicket';
import Ticket from '../modules/Masters/Ticktes/Ticket';
import TicketDetail from '../modules/Masters/Ticktes/TicketDetail';
import Add_new_user from '../modules/Masters/User/Add_new_user';
import User_profile from '../modules/Masters/User/User_profile';
import UserMaster from '../modules/Masters/User/UserMaster';
import UserDashboard from '../modules/Masters/UserDashboard/UserDashboard';
import EscalationAcknowledgement from '../modules/Masters/Configurations/Escalation/EscalationAcknowledgement';
import Inspection from '../modules/pages/Inspection';
import InspectionDetails from '../modules/pages/InspectionDetails';
import InspectionManageTemplate from '../modules/pages/InspectionManageTemplate';
import InspectionTemplate from '../modules/pages/InspectionTemplate';
import HotWorkPermit from '../modules/Masters/HotWorkPermit/HotWorkPermit';
import ManageHotTemplates from '../modules/Masters/HotWorkPermit/ManageHotTemplates';
import HotAddNewTemplate from '../modules/Masters/HotWorkPermit/HotAddNewTemplate';
import { PostWork } from '../modules/Masters/HotWorkPermit/DuringWork/PostWork';
import AddIncidentReports from '../modules/Masters/IncidentReports/AddIncidentReports';
import GooseExtraConfiguration from '../modules/Masters/GooseExtraConfiguration/GooseExtraConfiguration';
import Insurance from '../modules/Masters/Configurations/Insurance/Insurance';
import ShiftManagement from '../modules/Masters/Configurations/ShiftManagement/ShiftManagament';
import ViewHotWorkPermit from '../modules/Masters/HotWorkPermit/ViewHotWorkPermit/ViewHotWorkPermit';
import SurveyCreate from "../modules/pages/SurveyCreate";
import SurveyListPage from "../modules/pages/SurveyListPage";
import SurveyTypeSelection from "../modules/surveyListPage/SurveyTypeSelection";
import SurveyTypeSectionPage from "../modules/pages/SurveyTypeSectionPage";
import SurveyReportingPage from "../modules/pages/SurveyReportingPage";
import SurveySubmitAnswer from "../modules/pages/SurveySubmitAnswer";

const { VITE_BASE_URL } = import.meta.env;

export const BASE_URL = VITE_BASE_URL;
// console.log("BASE_URL", BASE_URL);
// export const BASE_URL = 'rx03-dev'
const AppRoutes: FC = () => {
  return (
    <BrowserRouter basename={BASE_URL}>
      <RouteTracker />
      <Routes>
        <Route element={<App />}>
          <Route path="*" element={<Error404 />} />
          <Route path="/" element={<Login />} />
          <Route path="/login" element={<Login />} />
          <Route
            path="/twofactorauthentication"
            element={<TwoFactorAuthentication />}
          />
          <Route
            path="/twoFactorEmailAuthentication"
            element={<TwoFactorEmailAuthentication />}
          />
          <Route path="/forgotpassword" element={<ForgotPassword />} />
          <Route path="/setnewpassword" element={<SetNewPassword />} />
          <Route path="/getstarted" element={<GetStarted />} />
          <Route path="/termsandconditions" element={<TermsAndConditions />} />
          <Route
            path="/escalation/acknowledgement"
            element={<EscalationAcknowledgement />}
          />
          <Route element={<AdminAuthenticater />}>
            <Route element={<MasterLayout />}>
              <Route path="dashboard" element={<UserDashboard />} />
              <Route path="departments" element={<Departments />} />
              <Route
                path="departments/adddepartments"
                element={<Add_Department />}
              />
              <Route
                path="departments/viewdepartment"
                element={<ViewDepartment />}
              />
              <Route path="usermaster" element={<UserMaster />} />
              <Route
                path="usermaster/permission"
                element={
                  <Work_Progress
                    title={[
                      {
                        path: "/usermaster",
                        state: {},
                        breadcrumb: "User Management",
                      },
                      { path: "", state: {}, breadcrumb: "Permission" },
                    ]}
                  />
                }
              />
              <Route path="usermaster/addnewuser" element={<Add_new_user />} />
              <Route path="usermaster/userprofile" element={<User_profile />} />
              <Route path="properties" element={<Properties />} />
              <Route path="properties/addproperties" element={<AddProperty />} />
              <Route path="properties/viewproperties" element={<ViewProperty />} />
              <Route path='calendar' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Calender" }]} />} />
              <Route path='loopvendors' element={<LoopVendor />} />
              <Route path='tickets' element={<Ticket />} />
              <Route path='tickets/addtickets' element={<AddTicket />} />
              <Route path='tickets/ticketdetail' element={<TicketDetail />} />
              <Route path='safetyforms' element={<SafetyForms />} />
              <Route path='incidentreport' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Incident Report" }]} />} />
              {/* <Route path='hotworkspermit' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Hot Work Permit" }]} />} /> */}
              {/* <Route path='redtagpermit' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Red Tag permit" }]} />} /> */}
              <Route path='hotworkspermit' element={<HotWorkPermit />} />
              <Route path='hotworkspermit/managehottemplates' element={<ManageHotTemplates />} />
              <Route path='hotworkspermit/managehottemplates/addnewhottemplate' element={<HotAddNewTemplate />} />
              <Route path="hotworkspermit/viewhotworkpermit" element={<ViewHotWorkPermit />} />
              <Route path="hotworkspermit/postwork" element={<PostWork />} />
              {/* <Route path='incidentreport' element={<IncidentReports/>}/> */}
              {/* <Route path='incidentreport/addincidentreport' element={<AddIncidentReports/>}/> */}
              <Route path='analytics' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Analytics" }]} />} />
              <Route path='inspections' element={<Inspection />} />
              <Route path='inspections/manage-templates' element={<InspectionManageTemplate />} />
              <Route path='inspections/manage-templates/template' element={<InspectionTemplate />} />
              <Route path='inspections/inspections-details' element={<InspectionDetails />} />
              <Route path='qrcode' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Qrcode" }]} />} />
              <Route path='linked' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Linked" }]} />} />
              <Route path='logs' element={<Work_Progress title={[{ path: "", state: {}, breadcrumb: "Logs" }]} />} />
              <Route path='permission' element={<Permission />} />
              <Route path='escalation' element={<Escalation />} />
              <Route path='insuracecompany' element={<Insurance />} />
              <Route path='shiftmanagement' element={<ShiftManagement />} />
              {/* <Route path="checkactivities" element={<CheckActivities />} /> */}
              <Route path="surveys" element={<SurveyListPage />} />
              <Route path="surveys/survey-create" element={<SurveyCreate />} />
              <Route
                path="surveys/survey-select-Type"
                element={<SurveyTypeSectionPage />}
              />

              <Route
                path="surveys/survey-details"
                element={<SurveyReportingPage />}
                // handle={{ bottomOffset: 140 }}
              />
            </Route>
          </Route>
          <Route element={<SuperAdminAuthenticater />}>
            <Route element={<MasterLayout />}>
              <Route path="admindashboard" element={<Dashboard />} />
              <Route
                path="adminloopvendors"
                element={<SuperAdminLoopVendor />}
              />
              <Route path="adminuser" element={<UserMasterKendo />} />
              <Route path="company" element={<Company_master />} />
              <Route path="goosechatreaction" element={<GooseChatAction />} />
              <Route path='goosechatconfig' element={<GooseChatConfigurations />} />
              <Route path='loopvendors' element={<LoopVendor />} />
              <Route path='goosefaqs' element={<GooseChatFAQs />} />
              <Route path='gooseagents' element={<GooseChatAgents />} />
              <Route path='gooseextraconfigs' element={<GooseExtraConfiguration />} />
            </Route>
          </Route>
        </Route>
        <Route
          path="surveys/survey-answer-fill"
          element={<SurveySubmitAnswer />}
        />
      </Routes>
    </BrowserRouter>
  );
};

export { AppRoutes };
