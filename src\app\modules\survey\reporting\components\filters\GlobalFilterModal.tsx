import React, { useState, useEffect } from "react";
import { Col, Form } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import KendoMultiSelect from "../../../../common/KendoMultiSelect";
import SingleSelectDropdown from "../../../../common/SingleSelectDropdown";
import { DateRangePicker } from "@progress/kendo-react-dateinputs";

interface GlobalFilterData {
  properties: any[];
  departments: any[];
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  status: string;
}

const GlobalFilterModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentFilters = {}
}) => {
  const [filterData, setFilterData] = useState<GlobalFilterData>({
    properties: [],
    departments: [],
    dateRange: {
      start: null,
      end: null
    },
    status: ""
  });

  const [tempFilterData, setTempFilterData] = useState<GlobalFilterData>(filterData);

  // Mock data - in real implementation, this would come from API
  const propertiesData = [
    { label: "Property 1", value: "prop1" },
    { label: "Property 2", value: "prop2" },
    { label: "Property 3", value: "prop3" }
  ];

  const departmentsData = [
    { label: "Health", value: "health" },
    { label: "Safety", value: "safety" },
    { label: "Operations", value: "operations" },
    { label: "Maintenance", value: "maintenance" }
  ];

  const statusData = [
    { label: "All", value: "" },
    { label: "Active", value: "active" },
    { label: "Completed", value: "completed" },
    { label: "Pending", value: "pending" }
  ];

  useEffect(() => {
    if (currentFilters && Object.keys(currentFilters).length > 0) {
      const newFilterData = {
        properties: currentFilters.properties || [],
        departments: currentFilters.departments || [],
        dateRange: currentFilters.dateRange || { start: null, end: null },
        status: currentFilters.status || ""
      };

      // Only update if the data has actually changed
      setFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.properties) !== JSON.stringify(newFilterData.properties) ||
          JSON.stringify(prev.departments) !== JSON.stringify(newFilterData.departments) ||
          JSON.stringify(prev.dateRange) !== JSON.stringify(newFilterData.dateRange) ||
          prev.status !== newFilterData.status;

        return hasChanged ? newFilterData : prev;
      });

      setTempFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.properties) !== JSON.stringify(newFilterData.properties) ||
          JSON.stringify(prev.departments) !== JSON.stringify(newFilterData.departments) ||
          JSON.stringify(prev.dateRange) !== JSON.stringify(newFilterData.dateRange) ||
          prev.status !== newFilterData.status;

        return hasChanged ? newFilterData : prev;
      });
    }
  }, [currentFilters?.properties, currentFilters?.departments, currentFilters?.dateRange, currentFilters?.status]);

  const handleApply = () => {
    setFilterData(tempFilterData);
    onApply(tempFilterData);
    onClose();
  };

  const handleReset = () => {
    const resetData = {
      properties: [],
      departments: [],
      dateRange: { start: null, end: null },
      status: ""
    };
    setTempFilterData(resetData);
    setFilterData(resetData);
  };

  const handlePropertyChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      properties: selected || []
    }));
  };

  const handleDepartmentChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      departments: selected || []
    }));
  };

  const handleStatusChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      status: selected?.value || ""
    }));
  };

  const handleDateRangeChange = (event: any) => {
    setTempFilterData(prev => ({
      ...prev,
      dateRange: {
        start: event.value.start,
        end: event.value.end
      }
    }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Global Filter"
      filterType="global"
    >
      <Col sm={12} className="mb-3">
        <Form.Label>Properties</Form.Label>
        <KendoMultiSelect
          dropdownData={propertiesData}
          getData={tempFilterData.properties}
          setData={handlePropertyChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Departments</Form.Label>
        <KendoMultiSelect
          dropdownData={departmentsData}
          getData={tempFilterData.departments}
          setData={handleDepartmentChange}
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Status</Form.Label>
        <SingleSelectDropdown
          data={statusData}
          getter={statusData.find(item => item.value === tempFilterData.status)}
          setter={handleStatusChange}
          placeholder="Select Status"
        />
      </Col>

      <Col sm={12} className="mb-3">
        <Form.Label>Date Range</Form.Label>
        <DateRangePicker
          value={{
            start: tempFilterData.dateRange.start,
            end: tempFilterData.dateRange.end
          }}
          onChange={handleDateRangeChange}
          className="form-control"
        />
      </Col>
    </BaseFilterModal>
  );
};

export default GlobalFilterModal;
