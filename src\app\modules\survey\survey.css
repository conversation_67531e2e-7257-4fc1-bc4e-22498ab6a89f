:root {
  --btn-fill-color: #141414; /* your page bg for “transparent” look */
  --btn-grad: linear-gradient(90deg, #0000ff, #8000ff);
}

.gradient-outline-btn {
  position: relative;
  padding: 8px 16px;
  border-radius: 9999px;
  font-size: 16px;
  color: var(--Rx-title);
  cursor: pointer;
  /* transition: all 0.2s ease; */

  /* 
      Layer 1 = your fill (transparent look = same as bg)
      Layer 2 = your gradient border 
    */
  background-image: linear-gradient(var(--Rx-bg), var(--Rx-bg)), var(--btn-grad);
  background-origin: padding-box, border-box;
  background-clip: padding-box, border-box;
  border: 1px solid transparent;
}

/* Hover on the outline state */
.gradient-outline-btn:not(.selected):hover {
  background-image:
      /* subtle white overlay for feedback */ linear-gradient(
      rgba(255, 255, 255, 0.05),
      rgba(255, 255, 255, 0.05)
    ),
    var(--btn-grad);
  color: var(--Rx-bg);
}

/* === SELECTED STATE === */
.gradient-outline-btn.selected {
  /* 
      Collapse to a single‐layer: full‐gradient fill, 
      *and* border shows the same gradient (because border is transparent)
    */
  color: var(--Rx-bg);
  background-image: var(--btn-grad);
  background-origin: border-box;
  background-clip: border-box;
  border: 1px solid transparent;
}

.white-outline-btn {
  position: relative;
  padding: 8px 16px;
  border-radius: 9999px;
  font-size: 16px;
  cursor: pointer;

  /* Un‑selected: transparent fill + white stroke */
  background: transparent;
  border: 1px solid var(--Rx-btn-bg);
  color: var(--Rx-btn-bg);
  transition: background 0.2s ease, color 0.2s ease;
}

.white-outline-btn:hover:not(.selected) {
  /* subtle hover feedback */
  background: rgba(255, 255, 255, 0.1);
}

.white-outline-btn.selected {
  /* Selected: solid white fill + white stroke */
  background: var(--Rx-btn-bg);
  color: var(--Rx-bg); /* or whatever your page-bg color is */
  border-color: var(--Rx-btn-bg);
}
