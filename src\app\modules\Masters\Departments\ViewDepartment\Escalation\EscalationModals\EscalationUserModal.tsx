import { useEffect, useState } from 'react';
import { Col, Modal, Row } from 'react-bootstrap'
import { RxCross2 } from 'react-icons/rx'
import { escalationService } from '../escalation.helper';
import userSVG from "../../../../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import encryptDecryptUtil from '../../../../../../utils/encrypt-decrypt-util';
import SwalMessage from '../../../../../common/SwalMessage';
import { getImage } from '../../../../../../utils/CommonUtils';

const EscalationUserModal = ({ addEscalationUserModal, setAddEscalationUserModal, userData, selectedmemberData, setSelectedMemberData, setLoading, propertyId, departmentid }: any) => {
    const [SelectMemberData, setSelectMemberData] = useState<any[]>([]);
    const [search, setSearch] = useState<string>('');
    const [isSearch, setIsSeach] = useState<boolean>(false);
    const [error, setError] = useState<string>('');
    const [isdisabled, setIsDisable] = useState<any>(false);
    const [displayUserData, setDisplayUserData] = useState<any[]>([]);

    // Internal getUserMemberData function for search within modal
    const getUserMemberDataInternal = async (search: string) => {
        try {
            let keyinfo = JSON.parse(localStorage.keyinfo);
            setLoading(true);

            await escalationService.getEscalationUserData("", departmentid, "", search)
                .then((res: any) => {
                    if (res?.data?.success) {
                        const result = encryptDecryptUtil.decryptData(
                            res?.data?.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        console.log("encResponse", encResponse)
                        setDisplayUserData(
                            selectedmemberData.length > 0
                                ? encResponse.sort((a: any, b: any) => {
                                    const aSelected: any = selectedmemberData.some(
                                        (item: any) => item.value === a.value
                                    );
                                    const bSelected: any = selectedmemberData.some(
                                        (item: any) => item.value === b.value
                                    );
                                    return bSelected - aSelected;
                                })
                                : encResponse
                                || []);
                    }
                }).catch((err: any) => {
                    SwalMessage('error', err?.message, 'OK', 'error', false);
                }).finally(() => {
                    setLoading(false);
                });
        } catch (error) {
            console.error('Search error:', error);
            setLoading(false);
        }
    };

    useEffect(() => {
        if (addEscalationUserModal && SelectMemberData.length > 0 && displayUserData.length > 0) {
            setIsDisable(true)
        } else {
            setIsDisable(false)
        }
    }, [SelectMemberData, displayUserData])

    // Store original data when modal opens and userData changes from parent
    useEffect(() => {
        if (addEscalationUserModal && userData?.length > 0) {
            setDisplayUserData(selectedmemberData.length > 0
                ? userData.sort((a: any, b: any) => {
                    const aSelected: any = selectedmemberData.some(
                        (item: any) => item.value == a.value
                    );
                    const bSelected: any = selectedmemberData.some(
                        (item: any) => item.value == b.value
                    );
                    return bSelected - aSelected;
                })
                : userData);
            setSelectMemberData(selectedmemberData); // pre-fill selection from parent
        }
    }, [addEscalationUserModal, selectedmemberData, userData]);

    // Handle search with debounce using internal function
    useEffect(() => {
        if (addEscalationUserModal) {
            const handler = setTimeout(() => {
                getUserMemberDataInternal(search);
            }, 400);
            return () => {
                clearTimeout(handler);
            };
        }
    }, [search, addEscalationUserModal]);

    const value =
        SelectMemberData === null || undefined
            ? []
            : SelectMemberData?.map((data: any) => data?.value);

    const handleSelectMember = (member: any) => {
        const isAlreadySelected = SelectMemberData.some((m: any) => m.value === member.value);

        if (isAlreadySelected) {
            const updatedSelection = SelectMemberData.filter(
                (m: any) => m.value !== member.value
            );
            setSelectMemberData(updatedSelection);
            setError('');
        } else {
            if (SelectMemberData.length >= 5) {
                setError('You can select up to 5 members only.');
                return;
            }
            setSelectMemberData((prevSelectedMembers: any) => [
                ...prevSelectedMembers,
                member
            ]);
            setError('');
        }
    };

    const savedata = () => {
        if (SelectMemberData.length === 0) {
            setError('Please select at least one member.');
            return;
        }

        setSelectedMemberData(SelectMemberData);
        setAddEscalationUserModal(false);
        // Remove the reset here since we handle it in useEffect
    }

    // Clear search function


    // Clean up when modal closes - no API calls
    useEffect(() => {
        if (!addEscalationUserModal) {
            setSelectMemberData([]);
            setSearch('');
            setError('');
            setIsDisable(false);
            setDisplayUserData([]);
            setIsSeach(false);
        }
    }, [addEscalationUserModal]);

    return (
        <>
            <Modal
                show={addEscalationUserModal}
                onHide={() => setAddEscalationUserModal(false)}
                scrollable={true}
                className='modal-right modal-right-small p-0'
            >
                <Modal.Header className="border-0 p-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Add Escalation User</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setAddEscalationUserModal(false)}
                            >
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                        <Col sm={12} className="mt-3 position-relative">
                            <input
                                type="text"
                                className="form-control"
                                placeholder="Search"
                                value={search}
                                onChange={(e) => { setIsSeach(true), setSearch(e.target.value) }}
                            />
                        </Col>
                        <Col sm={5} className="mt-3">
                            <span>Choose upto 5 members</span>
                        </Col>
                        <Col sm={7} className="mt-3 text-end">
                            <span className="text-danger">{error}</span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className='p-0 mt-5'>
                    <div>
                        {displayUserData?.map((item: any) => (
                            <div
                                className="d-flex align-items-center mt-6 pb-5 border-bottom"
                                key={item.value}
                            >
                                <input
                                    type="checkbox"
                                    checked={SelectMemberData?.map(
                                        (data: any) => data?.value || data?.id
                                    ).includes(item?.value)}
                                    className="text-white"
                                    onChange={() => handleSelectMember(item)}
                                    name={item?.value}
                                    id={`savedata-checkbox-${item?.value}`}
                                />
                                <label
                                    htmlFor={`savedata-checkbox-${item?.value}`}
                                    className="user-select-none d-flex align-items-center ms-3 cursor-pointer"
                                >
                                    <img
                                        src={item?.image ? getImage(item?.image) : userSVG}
                                        className="rounded-circle"
                                        height={"32px"}
                                        width={"32px"}
                                    />
                                    {item?.isExternal === 1 ? (
                                        <span className="ms-2">
                                            {item?.label}{" "}
                                            <i className="d-block text-gray-800 fs-12px">
                                                {item?.companyname}
                                            </i>
                                        </span>
                                    ) : (
                                        <span className="ms-2">{item?.label}</span>
                                    )}
                                </label>
                            </div>
                        ))}
                    </div>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 ">
                    <button className={`btn ${isdisabled ? "btn-success" : "rx-btn"}`}
                        onClick={() => savedata()}>
                        Save & Continue
                    </button>
                </Modal.Footer>
            </Modal>
        </>
    )
}

export default EscalationUserModal