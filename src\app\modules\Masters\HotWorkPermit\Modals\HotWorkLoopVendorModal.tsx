import React, { useEffect, useState } from 'react'
import { Col, Modal, Row } from 'react-bootstrap'
import LoopVendorManager from '../../../inspection/LoopVendorManager'
import HotProgressBar from '../HotProgressBar'
import { RxCross2 } from 'react-icons/rx'
import { Form, Formik } from 'formik'
import { hotworkspermitService } from '../HotWorkPermit.helper'
import encryptDecryptUtil from '../../../../utils/encrypt-decrypt-util'
import SwalMessage from '../../../common/SwalMessage'
import { useNavigate } from 'react-router'

const HotWorkLoopVendorModal = ({
    showLoopVendorModal,
    setShowLoopVendorModal,
    setHotWorkPerformedModal,
    loopVendorData,
    setLoopVendorData,
    formikRef,
    parentHwptId,
    hwptid,
    setHwpid,
    step,
    totalSteps,
    onSave,
    selectedItems,
    setSelectedItems,
    selectedDepartment,
    setSelectedDepartment
}: any) => {
    const [isDisabled, setIsDisabled] = useState(false);
    const [errors, setErrors] = useState<any>({});
    const [isLoading, setisLoading] = useState(false);
    const [workToBePerformedAtData, setWorkToBePerformedAtData] = useState<any>(null);
    const navigate = useNavigate()
    const permissiontype = JSON.parse(
        localStorage.getItem("userdetail") as string
    )?.permission;

    useEffect(() => {
        if (!formikRef.current) return;

        const isD = permissiontype === "D";

        const propertyIds = isD
            ? selectedItems[0]?.propertyId || selectedItems[0]
            : selectedItems?.map((item: any) => item.propertyId);

        const departmentIds = isD
            ? selectedDepartment?.map((item: any) => item.departmentId) || []
            : [];

        const loopVendorDetails: any[] = [];

        if (isD && selectedDepartment?.length > 0) {
            selectedDepartment.forEach((dept: any) => {
                if (Array.isArray(dept.loopVendor)) {
                    dept.loopVendor.forEach((loopVendorId: string) => {
                        loopVendorDetails.push({
                            loopVendorId,
                            departmentId: dept.departmentId,
                            propertyId: propertyIds, // string for D
                        });
                    });
                }
            });
        }

        if (!isD && selectedItems?.length > 0) {
            selectedItems.forEach((prop: any) => {
                if (Array.isArray(prop.loopVendor)) {
                    prop.loopVendor.forEach((loopVendorId: string) => {
                        loopVendorDetails.push({
                            loopVendorId,
                            departmentId: "", // no dept in P
                            propertyId: prop.propertyId,
                        });
                    });
                }
            });
        }

        formikRef.current.setValues({
            propertyIds,
            departmentIds,
            loopVendorDetails,
        });
    }, [
        selectedItems,
        selectedDepartment,
        permissiontype,
    ]);
    const validation = (values: any) => {
        const errors: any = {};
        if (!values.propertyIds || values.propertyIds.length === 0) {
            errors.propertyIds = "Property is required";
        }
        if (permissiontype === "D" && (!values.departmentIds || values.departmentIds.length === 0)) {
            errors.departmentIds = "Department is required";
        }
        setErrors(errors);

        return Object.keys(errors).length === 0; // Return true if no errors
    }

    // save data
    const handleSubmit = (values: any) => {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        if (validation(values) === false) {
            return
        }
        const { propertyIds, departmentIds, loopVendorDetails } = values;
        const propertyIdArray = Array.isArray(propertyIds) ? propertyIds : [propertyIds];
        const departmentIdArray = Array.isArray(departmentIds) ? departmentIds : [];

        let properties: any[] = [];
        let departments: any[] = [];

        if (permissiontype === "P") {
            // Build properties by propertyId, collecting matching loopVendors
            properties = propertyIdArray.map((pid: string) => ({
                propertyId: pid,
                loopVendor: loopVendorDetails
                    .filter((vendor: any) => vendor.propertyId === pid)
                    .map((vendor: any) => vendor.loopVendorId)
            }));

            departments = []; // empty
        }

        if (permissiontype === "D") {
            // One property object with loopVendor empty
            properties = propertyIdArray.map((pid: string) => ({
                propertyId: pid,
                loopVendor: []
            }));

            // Unique departments with matching loopVendors
            departments = departmentIdArray.map((did: string) => ({
                departmentId: did,
                loopVendor: loopVendorDetails
                    .filter((vendor: any) => vendor.departmentId === did)
                    .map((vendor: any) => vendor.loopVendorId)
            }));
        }

        const payload = {
            properties: properties,
            departments: departments,
            hwptid: hwptid ? hwptid : null,
            parentHwptId: parentHwptId ? parentHwptId : null,
        };
        // console.devAPILog("Payload for Hot Work Performed at:", payload);
        setisLoading(true);
        hotworkspermitService
            .saveHotperformedat(payload)
            .then((response: any) => {
                const resultData = response.data;
                if (response.status === 200) {
                    if (resultData?.success === true) {
                        const decryptData = encryptDecryptUtil.decryptData(
                            resultData.data,
                            keyinfo.syckey
                        );
                        setisLoading(false);
                        const parseData = JSON.parse(decryptData);
                        // console.log("parseData", parseData);
                        const propertyDisplayName = parseData?.displayData || "";
                        const parts = propertyDisplayName.split(',');

                        // Trim each part to remove extra spaces
                        const trimmedParts = parts.map((part: any) => part.trim());
                        // Set state individually
                        setLoopVendorData(`${trimmedParts[3] ? trimmedParts[3] : ""}`);
                        setHwpid(parseData?.hwptid);
                        SwalMessage(
                            null,
                            resultData?.errormsg,
                            "Ok",
                            "success",
                            false
                        ).then((isConfirmed) => {
                            if (isConfirmed) {
                                // if (isAuthorized) {
                                //   setOpenAuthorizedMembersModal(true);
                                // } else if (isFireWatch) {
                                //   setOpenFireWatchModal(true);
                                // } else {
                                // }
                                formikRef.current?.setFieldValue('loopVendorDetails', loopVendorDetails);
                                setHotWorkPerformedModal(true);
                                setShowLoopVendorModal(false);

                                if (onSave) {
                                    onSave();
                                }
                            }
                        });
                    } else {
                        setisLoading(false);
                        SwalMessage(null, resultData?.errormsg, "Ok", "error", false);
                    }
                } else {
                    if (response?.data?.status === 401) {
                        setisLoading(false);
                        localStorage.removeItem("islogin");
                        navigate("/login");
                        navigate(0);
                    }
                }
            })
            .catch((error: any) => {
                if (error.response?.status == 401) {
                    localStorage.removeItem("islogin");
                    navigate("/login");
                    navigate(0);
                }
                setisLoading(false);
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => setisLoading(false));
    };

    return (
        <>
            <Modal
                className="modal-right modal-right-small p-0"
                scrollable={true}
                show={showLoopVendorModal}
                onHide={() => setShowLoopVendorModal(false)}>
                <Modal.Header className="border-0 p-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Loop Vendors</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setShowLoopVendorModal(false)}>
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                        {/* <Col sm={12} className="mt-3">
                             <input
                               type="text"
                               className="form-control"
                               placeholder="Search"
                               value={search}
                               onChange={(e: any) => setSearch(e.target.value)}
                             />
                           </Col> */}
                    </Row>
                </Modal.Header>
                <Modal.Body className="mt-5 p-0">
                    <Form onSubmit={formikRef.current?.handleSubmit}>
                        {formikRef.current && (
                            <LoopVendorManager
                                formik={formikRef.current}
                                viewOnly={false}
                                isTemplateEdit={true}
                            />
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 d-block">
                    <Row className="align-items-center">
                        <Col sm={6}>
                            {isDisabled && (
                                <HotProgressBar currentStep={step} totalSteps={totalSteps} />
                            )}
                        </Col>
                        <Col sm={6} className="text-end">
                            <button
                                className="btn rx-btn"
                                // disabled={!isDisabled}
                                onClick={() => {
                                    if (!formikRef.current) return;
                                    const values = formikRef.current.values;
                                    handleSubmit(values);
                                }}>
                                Save & Continue
                            </button>
                        </Col>
                    </Row>
                </Modal.Footer>
            </Modal >
        </>
    )
}

export default HotWorkLoopVendorModal