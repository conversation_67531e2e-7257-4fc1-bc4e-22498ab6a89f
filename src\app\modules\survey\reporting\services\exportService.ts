// Enhanced export service for survey reporting
import html2canvas from "html2canvas";
import jsPD<PERSON> from "jspdf";
import { ExportOptions, ChartData, SurveyResponse } from "../types/chartTypes";

// Type definition for XLSX
interface XLSXModule {
  utils: {
    book_new: () => any;
    json_to_sheet: (data: any[]) => any;
    book_append_sheet: (workbook: any, sheet: any, name: string) => void;
  };
  writeFile: (workbook: any, filename: string) => void;
}

// Dynamic import for XLSX to ensure Vite compatibility
const loadXLSX = async (): Promise<XLSXModule> => {
  try {
    const XLSX = await import("xlsx");
    return XLSX as XLSXModule;
  } catch (error) {
    console.error("Failed to load XLSX library:", error);
    throw new Error(
      "Excel export functionality is not available. Please ensure the xlsx library is properly installed."
    );
  }
};

export class ExportService {
  /**
   * Enhanced PDF export with better quality and layout
   */
  static async exportToPDF(options: ExportOptions): Promise<void> {
    const {
      fileName = "survey-report.pdf",
      includeCharts = true,
      includeRawData = false,
    } = options;

    try {
      // Target the main content area to capture
      const contentElement = document.querySelector(
        ".rounded-5"
      ) as HTMLElement;

      if (!contentElement) {
        throw new Error("Content element not found");
      }

      // Enhanced canvas options for better quality
      const canvas = await html2canvas(contentElement, {
        scale: 3, // Higher scale for better quality
        useCORS: true,
        allowTaint: true,
        logging: false,
        backgroundColor: "#ffffff",
        width: contentElement.scrollWidth,
        height: contentElement.scrollHeight,
        onclone: (clonedDoc) => {
          // Enhance styles for PDF export
          const clonedElement = clonedDoc.querySelector(
            ".rounded-5"
          ) as HTMLElement;
          if (clonedElement) {
            clonedElement.style.backgroundColor = "#ffffff";
            clonedElement.style.padding = "20px";

            // Ensure all text is visible
            const textElements = clonedElement.querySelectorAll("*");
            textElements.forEach((el: any) => {
              if (el.style) {
                if (
                  el.style.color === "var(--chart-text)" ||
                  el.style.color === ""
                ) {
                  el.style.color = "#000000";
                }
              }
            });
          }
          return clonedDoc;
        },
      });

      // Calculate PDF dimensions
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Create PDF document with metadata
      const pdf = new jsPDF("p", "mm", "a4");

      // Add metadata
      pdf.setProperties({
        title: "Survey Report",
        subject: "Survey Analysis and Charts",
        author: "Survey Reporting System",
        creator: "Survey Reporting System",
      });

      // Add header
      pdf.setFontSize(16);
      pdf.setFont("helvetica", "bold");
      pdf.text("Survey Report", 20, 20);

      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);

      // Add main content
      const startY = 40;
      const contentHeight = pageHeight - startY - 20; // Leave space for header and footer

      if (imgHeight <= contentHeight) {
        // Content fits on one page
        pdf.addImage(
          canvas.toDataURL("image/png", 1.0),
          "PNG",
          10,
          startY,
          imgWidth - 20,
          (imgHeight * (imgWidth - 20)) / imgWidth
        );
      } else {
        // Content spans multiple pages
        let currentY = startY;
        let remainingHeight = imgHeight;
        let pageNumber = 1;

        while (remainingHeight > 0) {
          const pageContentHeight = Math.min(contentHeight, remainingHeight);
          const sourceY = imgHeight - remainingHeight;

          // Create a temporary canvas for this page
          const pageCanvas = document.createElement("canvas");
          const pageCtx = pageCanvas.getContext("2d");

          if (pageCtx) {
            pageCanvas.width = canvas.width;
            pageCanvas.height = (pageContentHeight / imgHeight) * canvas.height;

            pageCtx.drawImage(
              canvas,
              0,
              (sourceY / imgHeight) * canvas.height,
              canvas.width,
              pageCanvas.height,
              0,
              0,
              canvas.width,
              pageCanvas.height
            );

            pdf.addImage(
              pageCanvas.toDataURL("image/png", 1.0),
              "PNG",
              10,
              currentY,
              imgWidth - 20,
              (pageContentHeight * (imgWidth - 20)) / imgWidth
            );
          }

          remainingHeight -= pageContentHeight;

          if (remainingHeight > 0) {
            pdf.addPage();
            currentY = 20;
            pageNumber++;

            // Add page number
            pdf.setFontSize(8);
            pdf.text(`Page ${pageNumber}`, imgWidth - 30, pageHeight - 10);
          }
        }
      }

      // Add footer to last page
      pdf.setFontSize(8);
      pdf.text(`Page 1`, imgWidth - 30, pageHeight - 10);

      // Save the PDF
      pdf.save(fileName);
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw new Error(`Failed to generate PDF: ${error}`);
    }
  }

  /**
   * Export survey data to Excel
   */
  static async exportToExcel(
    chartData: ChartData[],
    responses: SurveyResponse[] = [],
    options: ExportOptions
  ): Promise<void> {
    const {
      fileName = "survey-report.xlsx",
      includeCharts = true,
      includeRawData = true,
    } = options;

    try {
      // Load XLSX dynamically for Vite compatibility
      const XLSX = await loadXLSX();

      // Create a new workbook
      const workbook = XLSX.utils.book_new();

      // Summary sheet
      if (includeCharts && chartData.length > 0) {
        const summaryData = this.prepareSummaryData(chartData);
        const summarySheet = XLSX.utils.json_to_sheet(summaryData);

        // Set column widths
        summarySheet["!cols"] = [
          { width: 10 }, // Question Number
          { width: 50 }, // Question Text
          { width: 15 }, // Chart Type
          { width: 15 }, // Total Responses
          { width: 15 }, // Answered Responses
          { width: 15 }, // Average Score
        ];

        XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");
      }

      // Detailed responses sheet
      if (includeCharts && chartData.length > 0) {
        chartData.forEach((chart) => {
          const chartSheet = this.prepareChartData(chart, XLSX);
          const sheetName = `Q${chart.questionNumber}_${chart.type}`.substring(
            0,
            31
          );
          XLSX.utils.book_append_sheet(workbook, chartSheet, sheetName);
        });
      }

      // Raw responses sheet
      if (includeRawData && responses.length > 0) {
        const rawDataSheet = this.prepareRawResponseData(responses, XLSX);
        XLSX.utils.book_append_sheet(workbook, rawDataSheet, "Raw Responses");
      }

      // Write the file
      XLSX.writeFile(workbook, fileName);
    } catch (error) {
      console.error("Error generating Excel file:", error);
      throw new Error(`Failed to generate Excel file: ${error}`);
    }
  }

  /**
   * Export to CSV
   */
  static async exportToCSV(
    chartData: ChartData[],
    responses: SurveyResponse[] = [],
    options: ExportOptions
  ): Promise<void> {
    const { fileName = "survey-report.csv", includeRawData = true } = options;

    try {
      let csvContent = "";

      if (includeRawData && responses.length > 0) {
        // Prepare raw response data for CSV
        const headers = [
          "Response ID",
          "Submitted At",
          "Respondent Name",
          "Question",
          "Answer",
        ];
        csvContent += headers.join(",") + "\n";

        responses.forEach((response) => {
          response.answers.forEach((answer) => {
            const row = [
              response.responseId,
              response.submittedAt,
              response.respondentInfo?.name || "Anonymous",
              `"${answer.questionText.replace(/"/g, '""')}"`,
              `"${answer.answerText.replace(/"/g, '""')}"`,
            ];
            csvContent += row.join(",") + "\n";
          });
        });
      } else if (chartData.length > 0) {
        // Export chart summary data
        const headers = [
          "Question Number",
          "Question Text",
          "Chart Type",
          "Total Responses",
          "Answered Responses",
        ];
        csvContent += headers.join(",") + "\n";

        chartData.forEach((chart) => {
          const row = [
            chart.questionNumber,
            `"${chart.title.replace(/"/g, '""')}"`,
            chart.type,
            chart.totalResponses,
            chart.answeredResponses,
          ];
          csvContent += row.join(",") + "\n";
        });
      }

      // Download CSV
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error generating CSV file:", error);
      throw new Error(`Failed to generate CSV file: ${error}`);
    }
  }

  /**
   * Prepare summary data for Excel export
   */
  private static prepareSummaryData(chartData: ChartData[]) {
    return chartData.map((chart) => ({
      "Question Number": chart.questionNumber,
      "Question Text": chart.title,
      "Chart Type": chart.type,
      "Total Responses": chart.totalResponses,
      "Answered Responses": chart.answeredResponses,
      "Average Score": chart.averageScore || "N/A",
    }));
  }

  /**
   * Prepare individual chart data for Excel export
   */
  private static prepareChartData(chart: ChartData, XLSX: XLSXModule) {
    const data = chart.data.map((item) => ({
      Answer: item.name,
      Count: item.value,
      Percentage: `${item.percentage}%`,
    }));

    return XLSX.utils.json_to_sheet(data);
  }

  /**
   * Prepare raw response data for Excel export
   */
  private static prepareRawResponseData(
    responses: SurveyResponse[],
    XLSX: XLSXModule
  ) {
    const flatData: Array<{
      "Response ID": string;
      "Submitted At": string;
      "Respondent Name": string;
      "Respondent Email": string;
      Property: string;
      Department: string;
      Question: string;
      Answer: string;
      "Question Type": string;
    }> = [];

    responses.forEach((response) => {
      response.answers.forEach((answer) => {
        flatData.push({
          "Response ID": response.responseId,
          "Submitted At": response.submittedAt,
          "Respondent Name": response.respondentInfo?.name || "Anonymous",
          "Respondent Email": response.respondentInfo?.email || "",
          Property: response.property || "",
          Department: response.department || "",
          Question: answer.questionText,
          Answer: answer.answerText,
          "Question Type": answer.questionType,
        });
      });
    });

    return XLSX.utils.json_to_sheet(flatData);
  }
}
