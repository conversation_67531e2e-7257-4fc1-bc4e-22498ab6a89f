import { useEffect, useState } from "react";
import { Modal, Row, Col } from "react-bootstrap";
import { FaTimes } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { FaArrowRightLong } from "react-icons/fa6";
import { Tooltip } from '@progress/kendo-react-tooltip'

function ViewStatusModal({
    openStatusModal,
    setopenStatusModal,
    logsData,
    setLogsData
}: any) {

    // Api Integration Start
    const navigate = useNavigate();

    const [search, setSearch] = useState("");
    const [filteredLogs, setFilteredLogs] = useState([]);

    useEffect(() => {
        if (openStatusModal) {
            setSearch("");         // Clear search on modal open
            setFilteredLogs(logsData || []);  // Reset logs
        }
        if (!openStatusModal && logsData?.length > 0) {
            setLogsData([])
        }
    }, [openStatusModal, logsData]);

    useEffect(() => {
        if (search.trim() === "") {
            setFilteredLogs(logsData || []);
        } else {
            const lowerSearch = search.toLowerCase();
            const filtered = (logsData || []).filter((log: any) =>
                log.oldValue?.toLowerCase().includes(lowerSearch) ||
                log.newValue?.toLowerCase().includes(lowerSearch) ||
                log.key?.toLowerCase().includes(lowerSearch)
            );
            setFilteredLogs(filtered);
        }
    }, [search, logsData]);

    return (
        <>
            <div>
                <Modal
                    className="modal-right modal-right-small"
                    scrollable={true}
                    show={openStatusModal}>
                    <Modal.Header className="p-0 border-bottom-0">
                        <Row>
                            <Col xs={8} className="mt-auto mb-auto">
                                <h2 className="mb-0">View Status</h2>
                            </Col>
                            <Col xs={4} className="text-end mb-3">
                                <a className="btn" onClick={() => setopenStatusModal(false)}>
                                    <FaTimes className="btn-icon-custom" />
                                </a>
                            </Col>
                            <Col xs={12} className="mb-3">
                                <div className="mt-5">
                                    <input
                                        type="text"
                                        className="form-control"
                                        placeholder="Search"
                                        value={search}
                                        autoFocus
                                        onChange={(e: any) => setSearch(e.target.value)}
                                        maxLength={180}
                                    />
                                </div>
                            </Col>
                        </Row>
                    </Modal.Header>
                    <Modal.Body className="p-0">
                        <Row>
                            <Col xxl={12} xl={12} lg={12} sm={12}>
                                <div className="mt-5">
                                    {filteredLogs?.length > 0 && filteredLogs.map((item: any, index: any) => {
                                        const showTooltipOld = item.isBoxShow === 1 && item.oldValue?.length > 15;
                                        const showTooltipNew = item.isBoxShow === 1 && item.newValue?.length > 15;

                                        const valueClass = `ms-2 fs-12px px-2 py-1 ${item.isBoxShow === 1 ? 'view-vendor-log' : ''}`;

                                        return (
                                            <div className="d-flex align-items-center mt-5 pb-5 mb-3 border-bottom" key={index}>
                                                <div className="d-flex flex-column ms-3">
                                                    <span className="ms-2 mb-2 fs-5 fw-bold">
                                                        {item?.key}
                                                    </span>
                                                    <div className="d-flex align-items-center">
                                                        {/* Old Value with optional Tooltip */}
                                                        {showTooltipOld ? (
                                                            <Tooltip position="top" anchorElement="target">
                                                                <span className={valueClass}>
                                                                    {item.oldValue}
                                                                </span>
                                                            </Tooltip>
                                                        ) : (
                                                            <span className={valueClass}>
                                                                {item.oldValue}
                                                            </span>
                                                        )}

                                                        <span className="ms-2">
                                                            <FaArrowRightLong />
                                                        </span>

                                                        {/* New Value with optional Tooltip */}
                                                        {showTooltipNew ? (
                                                            <Tooltip position="top" anchorElement="target">
                                                                <span className={valueClass}>
                                                                    {item.newValue}
                                                                </span>
                                                            </Tooltip>
                                                        ) : (
                                                            <span className={valueClass}>
                                                                {item.newValue}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </Col>
                        </Row>
                    </Modal.Body>
                    <Modal.Footer className="border-0 p-0">
                        <button className={`btn rx-btn`} onClick={() => setopenStatusModal(false)}>
                            <FaTimes />  Close
                        </button>
                    </Modal.Footer>
                </Modal>
            </div>
        </>
    );
}

export default ViewStatusModal;
