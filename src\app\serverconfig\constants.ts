// ---------------- AWS Build ----------------
// export const BACKEND_BASE_URL = "https://rx03-dev-live.resolvedx.io/resolvedxapi/";

// ---------------- AWS Stage Build ----------------

// export const BACKEND_BASE_URL = "https://rx03-dev-live.resolvedx.io/resolvedxapi/";
// export const BACKEND_BASE_URL_LOTO = "https://rx03-dev-live.resolvedx.io/api/";
// ---------------- janmay ----------------
// export const BACKEND_BASE_URL = "http://************:5101/resolvedxapi/";


// ---------------- client UAT ----------------
// export const BACKEND_BASE_URL_LOTO = "http://**************:9000/api/";
// export const BACKEND_BASE_URL = "http://**************:9000/resolvedxapi/";
// export const socketUrl = "http://**************:9000/";
// export const socketUrl = "https://dev.emergingfive.com/";

export const BACKEND_BASE_URL_LOTO = "https://dev.emergingfive.com/api/";
export const BACKEND_BASE_URL = "https://dev.emergingfive.com/resolvedxapi/";
export const socketUrl = "https://rx03-dev-live.resolvedx.io/";

// export const BACKEND_BASE_URL_LOTO = "http://*************:1111/api/";
// export const BACKEND_BASE_URL = "http://*************:1101/resolvedxapi/";   

// export const BACKEND_BASE_URL_LOTO = "http://localhost:9501/api/";
// export const BACKEND_BASE_URL = "http://localhost:5101/resolvedxapi/";



// export const BACKEND_BASE_URL_LOTO = "http://*************:6383/api/";
// export const BACKEND_BASE_URL = "http://*************:6383/resolvedxapi/"; 

// export const BACKEND_BASE_URL_LOTO = "http://*************:9501/api/";
// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/"; 

// export const BACKEND_BASE_URL_LOTO = "http://*************:9501/api/";
// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/"; 

// export const BACKEND_BASE_URL = "http://**************:9000/resolvedxapi/";

// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/";
// export const BACKEND_BASE_URL_LOTO = "http://*************:9501/api/";

// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/";


// ---------------- E5 UAT ----------------
// export const BACKEND_BASE_URL = "http://*************:1101/resolvedxapi/";
// export const BACKEND_BASE_URL = "http://*************:8080/resolvedxapi/";

// ---------------- Local WAR ----------------
// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/"; 
// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/";  

// export const BACKEND_BASE_URL = "http://************:5101/resolvedxapi/";
// export const BACKEND_BASE_URL = "http://*************:5101/resolvedxapi/";
// ---------------- System ----------------
// export const BACKEND_BASE_URL = "http://localhost:5101/resolvedxapi/";

export const otpDuration = 60;
export const googleMapKey = "AIzaSyCve5JqjikhpCS0zHFTO-P0wNAUCbnOCio"

// ---------------- AWS Stage Build ----------------
// export const BACKEND_BASE_URL = "https://rx03-stage-live.resolvedx.io/resolvedxapi/";
// export const BACKEND_BASE_URL_LOTO = "https://rx03-stage-live.resolvedx.io/api/";

