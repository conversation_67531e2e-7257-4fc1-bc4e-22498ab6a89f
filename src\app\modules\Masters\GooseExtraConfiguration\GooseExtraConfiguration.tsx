import React, { useEffect, useState } from 'react'
import { useBreadcrumbContext } from '../../../../_metronic/layout/components/header/BreadcrumbsContext';
import { Col, Dropdown, Row } from 'react-bootstrap';
import { Tooltip } from '@progress/kendo-react-tooltip';
import { Grid, GridColumn as Column } from '@progress/kendo-react-grid';
import { orderBy } from '@progress/kendo-data-query';
import { AiOutlineDislike, AiOutlineLike } from 'react-icons/ai';
import { getImage } from '../../../utils/CommonUtils';
import { gooseChatConfigService } from '../GooseChatConfigurations/GooseChatConfig.helper';
import SwalMessage from '../../common/SwalMessage';
import encryptDecryptUtil from '../../../utils/encrypt-decrypt-util';
import Spinner from '../../common/Spinner';
import { IoMdMore } from 'react-icons/io';
import { FaRegEye } from 'react-icons/fa';
import { MdOutlineEdit } from 'react-icons/md';
import { IoQrCodeOutline } from 'react-icons/io5';
import ShowAnswerModal from '../GooseChatAction/Modal/ShowAnswerModal';

const GooseExtraConfiguration = () => {
    const [addGooseChatAgent, setAddGooseChatAgent] = useState<boolean>(false);
    const [totalCount, setTotalCount] = useState<any>();
    const initialSort: Array<any> = [{ field: "user", dir: "asc" }];
    const [sort, setSort] = useState(initialSort);
    const [gridData, setGridData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [search, setSearch] = useState<string>("");
    const [editId, setEditId] = useState<string>("");
    const [editData, setEditData] = useState<any>({});

    const [showAnswer, setShowAnswer] = useState<boolean>(false);
    const [answer, setAnswer] = useState<string>("");
    const [question, setQuestion] = useState<string>("");

    // Pagination state
    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);

    // Pagination start
    const pageSizesArray = [
        { label: "5", value: 5 },
        { label: "10", value: 10 },
        { label: "15", value: 15 },
        { label: "All", value: totalCount },
    ];
    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
    };

    // Breadcrumb
    const { setLabels } = useBreadcrumbContext();
    useEffect(() => {
        setLabels([{ path: "", state: {}, breadcrumb: "Goose Chat Agents" }]);
    }, []);

    //fetch grid data
    // const fetchGridData = async (pageNumber: any, page: any, search: any) => {
    //     let keyinfo = JSON.parse(localStorage.keyinfo);
    //     setLoading(true);
    //     await gooseChatAgentservice.getGoosechatAgentsGrid(pageNumber, page, search).then((res: any) => {
    //         const resultData = res.data
    //         if (resultData.success) {
    //             const decryptedData = encryptDecryptUtil.decryptData(
    //                 resultData.data,
    //                 keyinfo.syckey
    //             );
    //             const parseData = JSON.parse(decryptedData);
    //             setGridData(parseData ? parseData?.data : []); // Ensure gridData is set to an empty array if parseData is null
    //             setTotalCount(parseData?.totalCount);
    //         } else {
    //             SwalMessage(null, resultData.errormsg, "OK", "error", false)
    //         }
    //     }).catch((error: any) => {
    //         console.error("Error fetching grid data:", error);
    //         setGridData([]);
    //         SwalMessage(null, error?.messgae, "OK", "error", false)
    //     }).finally(() => {
    //         setLoading(false);
    //     })

    // }

    // useEffect(() => {
    //     const pageNumber = Math.floor(page.skip / page.take) + 1;
    //     const timeoutId = setTimeout(() => {
    //         fetchGridData(pageNumber, page.take, "");
    //     }, 300)
    //     return () => clearTimeout(timeoutId);
    // }, [page, sort]);

    // open and show answer modal
    const handleOpenQuestionAnswerModal = (data: any) => {
        setQuestion(data?.name);
        setAnswer(data?.prompt);
        setShowAnswer(true);
    };

    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        if (field == "temperature") {
            return (
                <td className="k-table-td">
                    <span className="ellipsis-cell text-center" title={content}>
                        {dataItem[field]}
                    </span>
                </td>
            );
        }
        if (field == "prompt") {
            return (
                <td className="k-table-td cursor-pointer">
                    <span className="ellipsis-cell text-center" onClick={() => handleOpenQuestionAnswerModal(dataItem)}>
                        {dataItem[field]}
                    </span>
                </td>
            );
        }
        return (
            <td className="k-table-td">
                <span className="ellipsis-cell" title={content}>
                    {dataItem[field]}
                </span>
            </td>
        );
    };

    const handleEditData = (id: string) => {
        // setLoading(true);
        // gooseChatAgentservice.gooseChatAgentEdit(id).then((res: any) => {
        //     const resultData = res.data;
        //     if (resultData.success) {
        //         const decryptedData = encryptDecryptUtil.decryptData(
        //             resultData.data,
        //             JSON.parse(localStorage.keyinfo).syckey
        //         );
        //         const parseData = JSON.parse(decryptedData);
        //         setEditData(parseData);
        //         setEditId(id);
        //         setAddGooseChatAgent(true);
        //     } else {
        //         SwalMessage(null, resultData.errormsg, "OK", "error", false)
        //     }
        // }).catch((error: any) => {
        //     SwalMessage(null, error?.message, "OK", "error", false)
        // }).finally(() => {
        //     setLoading(false);
        // })
    }
    // start action
    const renderaction = (props: any) => {
        const content = props.dataItem;
        return (
            <>
                <td>
                    <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                        <Dropdown.Toggle
                            as="span"
                            className="fs-1 cursor-pointer ms-2"
                        >
                            <IoMdMore className="td-icon cursor-pointer" />
                        </Dropdown.Toggle>
                        <Dropdown.Menu align="end">
                            <Dropdown.Item onClick={() => handleEditData(content?.uuid)}>
                                <span
                                    className="fs-5"
                                >
                                    <MdOutlineEdit className="me-4" size={18} />
                                    Edit
                                </span>
                            </Dropdown.Item>
                        </Dropdown.Menu>
                    </Dropdown>
                </td >
            </>
        );
    };
    // end action
    return (
        <>
            {loading && <Spinner />}
            <Row className="mb-13 pageheader">
                <Col xl={6} lg={6} md={6} sm={12} className="mt-auto mb-auto">
                    <h1 className="page-title mobile-margin mb-0">Goose Extra Configuration</h1>
                </Col>
            </Row>
            <div className="card mt-0">
                <div className="card-body p-0">
                    <div className="table_div">
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid
                                data={orderBy(gridData, sort)}
                                skip={page.skip}
                                take={page.take}
                                total={totalCount}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: pageSizesArray.map((size: any) => size.label),
                                    pageSizeValue: pageSizeValue,
                                }}
                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column
                                    field="name"
                                    title="Name"
                                    cell={(props) =>
                                        renderTooltipCell({
                                            ...props,
                                            content: props.dataItem.name,
                                        })
                                    }
                                />
                                <Column
                                    field="value"
                                    title="Value"
                                    cell={(props) =>
                                        renderTooltipCell({
                                            ...props,
                                            content: props.dataItem.value,
                                        })
                                    }
                                />
                                <Column
                                    field="description"
                                    title="Description"
                                    // width={"260px"}
                                    cell={(props) =>
                                        renderTooltipCell({
                                            ...props,
                                            content: props.dataItem.description,
                                        })
                                    }
                                />
                                {/* <Column
                                    field="temperature"
                                    title="Temperature"
                                    // width={"200px"}
                                    headerClassName="center-header"
                                    cell={(props) =>
                                        renderTooltipCell({
                                            ...props,
                                            content: props.dataItem.temperature,
                                        })
                                    }
                                /> */}
                                <Column
                                    title="Action"
                                    field='action'
                                    width="80px"
                                    cell={(props) =>
                                        renderaction({ ...props, content: props.dataItem.id })
                                    }
                                />
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>
            {/* <AddGooseChatAgentModal
                addGooseChatAgent={addGooseChatAgent}
                setAddGooseChatAgent={setAddGooseChatAgent}
                editId={editId}
                setEditId={setEditId}
                editData={editData}
                setEditData={setEditData}
                setLoading={setLoading}
                fetchGridData={fetchGridData}
            /> */}

            <ShowAnswerModal
                setShowAnswer={setShowAnswer}
                showanswer={showAnswer}
                Answer={answer}
                Question={question}
                isShow={true}
            />

        </>
    )
}

export default GooseExtraConfiguration