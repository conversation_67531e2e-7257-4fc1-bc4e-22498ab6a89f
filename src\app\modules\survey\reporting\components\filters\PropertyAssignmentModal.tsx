import React, { useState, useEffect } from "react";
import { Col, Form, Row } from "react-bootstrap";
import BaseFilterModal from "./BaseFilterModal";
import { FilterModalProps } from "../../types/chartTypes";
import KendoMultiSelect from "../../../../common/KendoMultiSelect";

interface PropertyAssignmentData {
  selectedProperties: any[];
  assignmentType: string;
  includeSubProperties: boolean;
}

const PropertyAssignmentModal: React.FC<FilterModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentFilters = {}
}) => {
  const [filterData, setFilterData] = useState<PropertyAssignmentData>({
    selectedProperties: [],
    assignmentType: "include",
    includeSubProperties: false
  });

  const [tempFilterData, setTempFilterData] = useState<PropertyAssignmentData>(filterData);

  // Mock data - in real implementation, this would come from API
  const propertiesData = [
    { 
      label: "Orthopedic Center 1", 
      value: "ortho1",
      subProperties: [
        { label: "Building A", value: "ortho1_a" },
        { label: "Building B", value: "ortho1_b" }
      ]
    },
    { 
      label: "Orthopedic Center 2", 
      value: "ortho2",
      subProperties: [
        { label: "Main Building", value: "ortho2_main" },
        { label: "Annex", value: "ortho2_annex" }
      ]
    },
    { 
      label: "General Hospital", 
      value: "general1",
      subProperties: [
        { label: "Emergency Wing", value: "general1_emergency" },
        { label: "Surgery Wing", value: "general1_surgery" },
        { label: "Outpatient", value: "general1_outpatient" }
      ]
    },
    { 
      label: "Rehabilitation Center", 
      value: "rehab1",
      subProperties: [
        { label: "Physical Therapy", value: "rehab1_pt" },
        { label: "Occupational Therapy", value: "rehab1_ot" }
      ]
    }
  ];

  const assignmentTypeOptions = [
    { label: "Include Selected Properties", value: "include" },
    { label: "Exclude Selected Properties", value: "exclude" },
    { label: "Only Selected Properties", value: "only" }
  ];

  useEffect(() => {
    if (currentFilters && Object.keys(currentFilters).length > 0) {
      const newFilterData = {
        selectedProperties: currentFilters.selectedProperties || [],
        assignmentType: currentFilters.assignmentType || "include",
        includeSubProperties: currentFilters.includeSubProperties || false
      };

      // Only update if the data has actually changed
      setFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.selectedProperties) !== JSON.stringify(newFilterData.selectedProperties) ||
          prev.assignmentType !== newFilterData.assignmentType ||
          prev.includeSubProperties !== newFilterData.includeSubProperties;

        return hasChanged ? newFilterData : prev;
      });

      setTempFilterData(prev => {
        const hasChanged =
          JSON.stringify(prev.selectedProperties) !== JSON.stringify(newFilterData.selectedProperties) ||
          prev.assignmentType !== newFilterData.assignmentType ||
          prev.includeSubProperties !== newFilterData.includeSubProperties;

        return hasChanged ? newFilterData : prev;
      });
    }
  }, [currentFilters?.selectedProperties, currentFilters?.assignmentType, currentFilters?.includeSubProperties]);

  const handleApply = () => {
    setFilterData(tempFilterData);
    onApply(tempFilterData);
    onClose();
  };

  const handleReset = () => {
    const resetData = {
      selectedProperties: [],
      assignmentType: "include",
      includeSubProperties: false
    };
    setTempFilterData(resetData);
    setFilterData(resetData);
  };

  const handlePropertiesChange = (selected: any) => {
    setTempFilterData(prev => ({
      ...prev,
      selectedProperties: selected || []
    }));
  };

  const handleAssignmentTypeChange = (type: string) => {
    setTempFilterData(prev => ({
      ...prev,
      assignmentType: type
    }));
  };

  const handleSubPropertiesToggle = () => {
    setTempFilterData(prev => ({
      ...prev,
      includeSubProperties: !prev.includeSubProperties
    }));
  };

  return (
    <BaseFilterModal
      isOpen={isOpen}
      onClose={onClose}
      onApply={handleApply}
      onReset={handleReset}
      title="Assign Global Property"
      filterType="property"
    >
      <Col sm={12} className="mb-4">
        <Form.Label>Select Properties</Form.Label>
        <KendoMultiSelect
          dropdownData={propertiesData}
          getData={tempFilterData.selectedProperties}
          setData={handlePropertiesChange}
        />
      </Col>

      <Col sm={12} className="mb-4">
        <Form.Label>Assignment Type</Form.Label>
        <div className="d-flex flex-column gap-2">
          {assignmentTypeOptions.map((option) => (
            <Form.Check
              key={option.value}
              type="radio"
              id={`assignment-${option.value}`}
              name="assignmentType"
              label={option.label}
              checked={tempFilterData.assignmentType === option.value}
              onChange={() => handleAssignmentTypeChange(option.value)}
            />
          ))}
        </div>
      </Col>

      <Col sm={12} className="mb-4">
        <Form.Check
          type="checkbox"
          id="includeSubProperties"
          label="Include Sub-Properties"
          checked={tempFilterData.includeSubProperties}
          onChange={handleSubPropertiesToggle}
        />
        <Form.Text className="text-muted">
          When enabled, all sub-properties of selected properties will be included in the filter.
        </Form.Text>
      </Col>

      {tempFilterData.selectedProperties.length > 0 && (
        <Col sm={12} className="mb-3">
          <Form.Label>Selected Properties Summary</Form.Label>
          <div className="p-3 bg-light rounded">
            <small className="text-muted">
              {tempFilterData.assignmentType === "include" && "Including: "}
              {tempFilterData.assignmentType === "exclude" && "Excluding: "}
              {tempFilterData.assignmentType === "only" && "Only showing: "}
              {tempFilterData.selectedProperties.map(prop => prop.label).join(", ")}
              {tempFilterData.includeSubProperties && " (with sub-properties)"}
            </small>
          </div>
        </Col>
      )}
    </BaseFilterModal>
  );
};

export default PropertyAssignmentModal;
