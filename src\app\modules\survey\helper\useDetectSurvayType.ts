import React from "react";
import { useSearchParams, useLocation } from "react-router-dom";
import { SurveyType } from "../../../apis/type";


const useSurveyUtil = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const surveyType =
    (searchParams.get("surveyType")?.toUpperCase() as SurveyType) || "GLOBAL";
  
  const routerState = location.state;

  const surveyIdByParams = searchParams.get("surveyId");

  const surveyId = location.state?.id || surveyIdByParams || null;

  return {
    surveyType,
    routerState,
    surveyId
  };
};

export default useSurveyUtil;
