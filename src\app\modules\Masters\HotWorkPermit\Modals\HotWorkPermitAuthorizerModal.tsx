import React, { useEffect, useState } from "react";
import { Card, Col, Modal, Row } from "react-bootstrap";
import { FaPlus } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import userImage from "../../../../../efive_assets/images/user.jpg";
import noImage from "../../../../../efive_assets/images/noimage.jpg";
import { MdOutlineDeleteForever } from "react-icons/md";
import AddNewEContactModal from "./AddNewEContactModal";
import { hotworkspermitService } from "../HotWorkPermit.helper";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import { useNavigate } from "react-router";
import SwalMessage from "../../../common/SwalMessage";
import { getImage } from "../../../../utils/CommonUtils";
import { ClipLoader } from "react-spinners";
import HotProgressBar from "../HotProgressBar";

const HotWorkPermitAuthorizerModal = ({
  openAuthorizedMembersModal,
  setOpenAuthorizedMembersModal,
  setOpenFireWatchModal,
  setAuthorizedMembersData,
  authorizedMembersData,
  selectedProperty,
  selectedDepartment,
  parentHwptId,
  hwptid,
  setHwpid,
  step,
  totalSteps,
  onSave,
}: any) => {
  const [loading, setLoading] = useState<any>(false);
  const [memberData, setMemberData] = useState([]);
  const [search, setSearch] = useState<any>("");
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const navigate = useNavigate();
  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );

  // disable button
  const [isDisabled, setIsDisabled] = useState(false);
  const validateFeild = () => {
    const anyCheckboxSelected = Object.values(selectedItems).some(Boolean);
    setIsDisabled(!anyCheckboxSelected);
  };
  useEffect(() => {
    validateFeild();
  }, [selectedItems]);

  // empty on modal close
  useEffect(() => {
    if (authorizedMembersData && openAuthorizedMembersModal) {
      console.devAPILog("authorizedMembersData", authorizedMembersData);
      setSelectedItems(authorizedMembersData?.userIds)
      setSearch("");
    } else {
      setSearch("");
      setSelectedItems([]);
    }

  }, [openAuthorizedMembersModal]);

  const getUserList = (search: any) => {
    setLoading(true);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    const properttyIds = selectedProperty ? selectedProperty.map((item: any) => item?.propertyId) : [];
    const departmentIds = selectedDepartment ? selectedDepartment.map((item: any) => item?.departmentId) : [];
    hotworkspermitService
      .getMembersForTemplate(search, hwptid, properttyIds, departmentIds)
      .then((response: any) => {
        const resultData = response.data;
        if (response.status === 200) {
          if (resultData?.success === true) {
            const decryptData = encryptDecryptUtil.decryptData(
              resultData.data,
              keyinfo.syckey
            );
            const parseData = JSON.parse(decryptData);
            setMemberData(parseData);
          } else {
            SwalMessage(null, resultData?.errormsg, "Ok", "error", false);
          }
        } else {
          if (response?.data?.status === 401) {
            localStorage.removeItem("islogin");
            navigate("/login");
            navigate(0);
          }
        }
      })
      .catch((error) => {
        if (error.response?.status == 401) {
          localStorage.removeItem("islogin");
          navigate("/login");
          navigate(0);
        }
        SwalMessage(null, error?.message, "Ok", "error", false);
      })
      .finally(() => setLoading(false));
  }
  // get user list
  useEffect(() => {
    if (openAuthorizedMembersModal) {
      getUserList(search)
    }
  }, [openAuthorizedMembersModal, search]);

  // on Checkbox Change
  const handlePropertyCheckboxChange = (item: any) => {
    const isSelected = selectedItems.some(
      (selectedItem) => selectedItem === item.value
    );
    if (isSelected) {
      setSelectedItems(
        selectedItems.filter((selectedItem) => selectedItem !== item.value)
      );
    } else {
      setSelectedItems([...selectedItems, item?.value]);
    }
  };

  // on Save
  const handleSubmit = () => {
    const payload = {
      hwptid: hwptid ? hwptid : null,
      permitAuthorizers: selectedItems,
      parentHwptId: parentHwptId ? parentHwptId : null,
    };
    setLoading(true);
    let keyinfo = JSON.parse(localStorage.keyinfo);
    hotworkspermitService
      .savepermitAuthorizer(payload)
      .then((response: any) => {
        const resultData = response.data;
        if (response.status === 200) {
          if (resultData?.success === true) {
            console.devAPILog("resultData", resultData);
            const decryptData = encryptDecryptUtil.decryptData(
              resultData.data,
              keyinfo.syckey
            );
            setLoading(false);
            const parseData = JSON.parse(decryptData);
            setAuthorizedMembersData(parseData);
            console.devAPILog("parseData", parseData)
            setHwpid(parseData?.hwptid);
            SwalMessage(
              null,
              resultData?.errormsg,
              "Ok",
              "success",
              false
            ).then((isConfirmed) => {
              if (isConfirmed) {
                setOpenFireWatchModal(true);
                setOpenAuthorizedMembersModal(false);
                if (onSave) {
                  onSave();
                }
              }
            });
          } else {
            setLoading(false);
            SwalMessage(null, resultData?.errormsg, "Ok", "error", false);
          }
        } else {
          if (response?.data?.status === 401) {
            setLoading(false);
            localStorage.removeItem("islogin");
            navigate("/login");
            navigate(0);
          }
        }
      })
      .catch((error) => {
        if (error.response?.status == 401) {
          localStorage.removeItem("islogin");
          navigate("/login");
          navigate(0);
        }
        setLoading(false);
        SwalMessage(null, error?.message, "Ok", "error", false);
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      {loading && spinner}
      <Modal
        className="modal-right modal-right-small p-0"
        scrollable={true}
        show={openAuthorizedMembersModal}
        onHide={() => setOpenAuthorizedMembersModal(false)}>
        <Modal.Header className="border-0 p-0">
          <Row className="align-items-baseline">
            <Col xs={10} className="mt-auto mb-auto">
              <h2 className="mb-0">Permit Authorizer</h2>
            </Col>
            <Col xs={2} className="text-end mb-3">
              <span
                className="close-btn cursor-pointer"
                onClick={() => setOpenAuthorizedMembersModal(false)}>
                <RxCross2 fontSize={20} />
              </span>
            </Col>
          </Row>
          <Row>
            <Col sm={12}>
              <input
                type="text"
                className="form-control"
                placeholder="Search"
                value={search}
                onChange={(e: any) => setSearch(e.target.value)}
              />
            </Col>
          </Row>
        </Modal.Header>
        <Modal.Body className="mt-5 p-0">
          <Row className="mt-3 ">
            {memberData && memberData.length > 0 ? (
              memberData.map((member: any, index: any) => (
                <Col sm={12}>
                  <Card className="mb-2 custom-card" key={index}>
                    <Card.Body className="p-3">
                      <div className="d-flex align-items-center justify-content-between gap-2">
                        <div className="d-flex align-items-center gap-2">
                          <img
                            src={getImage(member?.image)}
                            className="assignlogo rounded-circle"
                            height={"32px"}
                            width={"32px"}
                          />
                          <div className="d-flex flex-column">
                            <span className="member-name">{member.label}</span>
                            <span className="fs-12px text-muted">
                              {member.jobtitle}
                            </span>
                          </div>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedItems.some(
                              (selectedItem) => selectedItem === member?.value
                            )}
                            className="text-white"
                            onChange={() =>
                              handlePropertyCheckboxChange(member)
                            }
                            name={member?.value}
                            id={`savedata-checkbox-${member?.value}`}
                          />
                        </div>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            ) : (
              <div className="text-center">No members exist</div>
            )}
          </Row>
        </Modal.Body>
        <Modal.Footer className="border-0 p-0 d-block">
          <Row className="align-items-center">
            <Col sm={6}>
              {!isDisabled && (
                <HotProgressBar currentStep={step} totalSteps={totalSteps} />
              )}
            </Col>
            <Col sm={6} className="text-end">
              <button
                className="btn rx-btn"
                disabled={isDisabled}
                onClick={handleSubmit}>
                Save & Continue
              </button>
            </Col>
          </Row>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default HotWorkPermitAuthorizerModal;
