import { useState, useCallback } from 'react';
import { useSubmitSurveyResponseMutation } from '../../../apis/survaysAPI';
import { SurveyQuestionResponseDto, ISubmitSurveyResponsePayload, AttachmentDtoList } from '../../../apis/type';

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachmentDtoList?: AttachmentDtoList[];
  branchingAnswer?: {
    answers: string[];
    comment: string;
    attachmentDtoList?: AttachmentDtoList[];
  };
}

interface SurveyResponseState {
  [questionId: string]: {
    answer: SurveyAnswer;
    surveyResponseId?: string;
    attachmentDtoList?: AttachmentDtoList[]
  };
}

export const useSurveyAnswerState = (surveyId: string) => {
  const [answers, setAnswers] = useState<SurveyResponseState>({});
  const [submitSurveyResponse, { isLoading: isSubmitting }] = useSubmitSurveyResponseMutation();

  // Update answer for a specific question
  const updateAnswer = useCallback((questionId: string, answer: SurveyAnswer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        answer,
      }
    }));
  }, []);

  // Submit answer for a specific question
  const handleSubmitAnswer = useCallback(async (
    question: SurveyQuestionResponseDto, 
    questionIndex: number
  ) => {
    const currentAnswer = answers[question.id]?.answer;
    
    if (!currentAnswer) {
      // No answer provided, skip submission
      return;
    }

    try {
      const payload: ISubmitSurveyResponsePayload = {
        surveyId,
        surveyQuestionId: question.id,
        answers: currentAnswer.answers || [],
        comment: currentAnswer.comment || '',
        attachmentDtoList: currentAnswer.attachmentDtoList || [],
        branchingQuestion: currentAnswer.branchingAnswer ? {
          id: question.branchingQuestion?.id || '',
          answers: currentAnswer.branchingAnswer.answers || [],
          comment: currentAnswer.branchingAnswer.comment || '',
          surveyQuestionId: question.branchingQuestion?.id || '',
          surveyResponseId: answers[question.id]?.surveyResponseId || '',
          attachmentDtoList: currentAnswer.branchingAnswer.attachmentDtoList || [],
        } : undefined as any,
        surveyResponseId: answers[question.id]?.surveyResponseId || undefined,
      };

      const response = await submitSurveyResponse(payload).unwrap();
      
      if (response?.data?.surveyResponseId) {
        // Store the surveyResponseId for future edits
        setAnswers(prev => ({
          ...prev,
          [question.id]: {
            ...prev[question.id],
            surveyResponseId: response.data.surveyResponseId,
          }
        }));
      }

      console.log('Answer submitted successfully for question:', question.id);
    } catch (error) {
      console.error('Failed to submit answer for question:', question.id, error);
      // You might want to show an error message to the user here
    }
  }, [answers, surveyId, submitSurveyResponse]);

  // Handle final survey submission
  const handleFinalSubmit = useCallback(async () => {
    console.log("Survey Submitted");
    // Here you can add any final submission logic if needed
    // For now, we just log as requested
  }, []);

  return {
    answers: Object.fromEntries(
      Object.entries(answers).map(([questionId, data]) => [questionId, data.answer])
    ),
    updateAnswer,
    isSubmitting,
    handleSubmitAnswer,
    handleFinalSubmit,
  };
};
