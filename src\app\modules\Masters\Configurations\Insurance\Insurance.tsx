import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useEffect, useState } from 'react';
import { Badge, Dropdown } from "react-bootstrap";
import { IoMdAdd, IoMdMore } from 'react-icons/io';
import { MdOutlineDeleteForever, MdOutlineEdit } from "react-icons/md";
import { useNavigate } from 'react-router-dom';
import { useBreadcrumbContext } from "../../../../../_metronic/layout/components/header/BreadcrumbsContext";
import { insuranceService } from "./insurance.helper";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import SwalMessage from "../../../common/SwalMessage";
import AddInsuranceModal from "./AddInsuranceModal";
import Spinner from "../../../common/Spinner";
import SingleSelectDropdown from "../../../common/SingleSelectDropdown";
import { formatContactInputNumber } from "../../../../utils/CommonUtils";


const statusData = [
    // { label: "All", value: "" },
    { label: "Active", value: 1 },
    { label: "Inactive", value: 0 },
];

function Insurance() {
    const navigate = useNavigate();
    const userinfo = JSON.parse(localStorage.getItem("userinfo") as string);
    const [search, setSearch] = useState<string>("");
    const [gridLoading, setGridLoading] = useState<boolean>(false)
    const [openAddInsuranceModal, setOpenAddInsuranceModal] = useState<boolean>(false)
    const [gridData, setGridData] = useState<any>([])
    const [insuranceId, setInsuranceId] = useState<string>("");
    const [editData, setEditData] = useState<any>({});
    const [status, setStatus] = useState<any>();
    const initialSort: Array<any> = [
        { field: "insuranceCompanyCode", dir: "asc" },
    ];
    const [sort, setSort] = useState(initialSort);

    const initialDataState: any = { skip: 0, take: 10 };
    const [page, setPage] = useState<any>(initialDataState);
    const [totalCount, setTotalCount] = useState<any>();
    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);
    // const pageNumber = Math.floor(page.skip / page.take) + 1;
    const itemPerPage: any = [
        {
            label: "5",
            value: 5,
        },
        {
            label: "10",
            value: 10,
        },
        {
            label: "15",
            value: 15,
        },
        {
            label: "All",
            value: totalCount,
        },
    ];
    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
    };

    //breadcrumb
    const { setLabels } = useBreadcrumbContext();
    useEffect(() => {
        setLabels([{ path: "", state: {}, breadcrumb: "Insurance Company" }]);
    }, []);

    // Grid API 
    const insuranceGrid = () => {
        const pageNumber = Math.floor(page.skip / page.take) + 1;
        const payload = {
            page: pageNumber,
            size: page.take,
            search: search ? search : "",
            active: status?.value || "",
            sortingColumns: [
                {
                    sortOrder: 0,
                    columnName: "",
                },
            ],
        }
        // console.log("payload", payload)
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        insuranceService
            .getGridData(payload)
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        // console.log("encResponse", encResponse)
                        setGridData(encResponse?.data);
                        setTotalCount(encResponse?.totalCount);
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    };
    useEffect(() => {
        setTimeout(() => {
            insuranceGrid();
        }, 500);
    }, [search, page, status]);

    // Delete Insurance
    const handleDeleteInsuranceCompany = async (insuranceId: any) => {
        const payload = {
            id: insuranceId
        }
        const confirm = await SwalMessage(
            "Are You Sure?",
            "Do you really want to delete this Insurance Company? This process cannot be undone.",
            "Delete",
            "info",
            true
        );
        if (confirm) {
            setGridLoading(true);
            insuranceService
                .deleteinstance(payload)
                .then((response) => {
                    // console.log(response);
                    if (response.status == 200) {
                        if (response.data.success == true) {
                            SwalMessage(null, response.data.errormsg, "Ok", "success", false).then((isConfirm) => {
                                if (isConfirm) {
                                    insuranceGrid();
                                    setGridLoading(false);
                                }
                            })
                            setGridLoading(false);
                        } else {
                            SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                            setGridLoading(false);
                        }
                    }
                })
                .catch((error) => {
                    if (error.response?.status == 401) {
                        // localStorage.removeItem("islogin");
                        navigate("/dashboard");
                        // navigate(0);
                    }
                    SwalMessage(null, error.response.data.message, "Ok", "error", false);
                    setGridLoading(false);
                })
                .finally(() => {
                    setGridLoading(false);
                });
        }
    };

    // edit insurance company
    const handleEditInsurance = (insuranceid: any) => {
        const payload = {
            insuranceId: insuranceid
        }
        setGridLoading(true);
        let keyinfo = JSON.parse(localStorage.keyinfo);
        insuranceService
            .getinsurancedata(payload)
            .then((response) => {
                if (response.status == 200) {
                    if (response.data.success == true) {
                        const result = encryptDecryptUtil.decryptData(
                            response.data.data,
                            keyinfo.syckey
                        );
                        const encResponse = JSON.parse(result);
                        setEditData(encResponse);
                        setInsuranceId(insuranceid);
                        setOpenAddInsuranceModal(true)
                    } else {
                        SwalMessage(null, response.data.errormsg, "Ok", "error", false);
                    }
                }
            })
            .catch((error) => {
                if (error.response.status == 401) {
                    // localStorage.removeItem("islogin");
                    navigate("/dashboard");
                    // navigate(0);
                }
                SwalMessage(null, error?.message, "Ok", "error", false);
            })
            .finally(() => {
                setGridLoading(false);
            });
    }

    // render switch
    const renderswitch = (props: any) => {
        const { dataItem } = props;
        return (
            <td className="text-center">
                <Badge
                    bg={dataItem.active == 1 ? "success" : "danger"}
                    className="text-white"
                >
                    {dataItem.active == 1 ? "Active" : "Inactive"}
                </Badge>
            </td>
        );
    };

    const renderaction = ({ content }: any) => {
        return (
            <td className="k-table-td">

                <Dropdown className="new-chat-btn" style={{ position: 'static' }}>
                    <Dropdown.Toggle
                        as="span"
                        className="fs-1 cursor-pointer ms-2"
                    >
                        <IoMdMore className="td-icon cursor-pointer" />
                    </Dropdown.Toggle>
                    <Dropdown.Menu align="end">
                        <Dropdown.Item onClick={() => handleEditInsurance(content)}>
                            <span className="fs-5">
                                <MdOutlineEdit className="me-4" size={18} />
                                Edit
                            </span>
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => handleDeleteInsuranceCompany(content)}>
                            <span className="fs-5">
                                <MdOutlineDeleteForever className="me-4" size={18} />
                                Delete
                            </span>
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </td>
        );
    };
    // end action

    //  start tooltip
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        if (field === "insuranceCompanyPhoneNumber") {
            const formattedPhoneNumber = formatContactInputNumber(dataItem[field]);
            return (
                <td className="k-table-td">
                    <span className="ellipsis-cell" title={formattedPhoneNumber}>
                        {formattedPhoneNumber}
                    </span>
                </td>
            );
        } else {
            return (
                <td className='k-table-td'>
                    <span className='ellipsis-cell' title={content}>{dataItem[field]}</span>
                </td>
            );
        }
    };
    // end tooltip



    return (
        <>
            {gridLoading && <Spinner />}
            <div className='row pageheader mb-7'>
                <div className="col-xl-3 col-lg-3 col-sm-3 col-12 mobile-margin">
                    <input
                        type="text"
                        className="form-control"
                        placeholder="Search..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                </div>
                <div className="col-xl-2 col-lg-2 col-sm-2 col-12 mobile-margin">
                    <SingleSelectDropdown
                        data={statusData}
                        getter={status}
                        setter={setStatus}
                        placeholder="Select Status"
                    />
                </div>
                <div className=" col-xl-7 col-lg-7 col-sm-7 col-12 text-end mobile-margin ">
                    {userinfo?.isfullaccess == 1 && (
                        <span className="btn rx-btn" onClick={() => setOpenAddInsuranceModal(true)}>
                            <IoMdAdd className="btn-icon-custom" />
                            Add Insurance{" "}
                        </span>
                    )}
                </div>
            </div>
            <div className='card mt-0'>
                <div className='card-body p-0'>
                    <div className='table_div' style={{ width: '100%' }}>
                        <Tooltip position="bottom" anchorElement="target">
                            <Grid
                                data={orderBy(gridData, sort)}
                                skip={page.skip}
                                take={page.take}
                                total={totalCount}
                                pageable={{
                                    buttonCount: 4,
                                    pageSizes: itemPerPage.map((item: any) => item.label),
                                    pageSizeValue: pageSizeValue,
                                }}

                                onPageChange={pageChange}
                                sortable={true}
                                sort={sort}
                                onSortChange={(e: any) => {
                                    setSort(e.sort);
                                }}
                            >
                                <Column
                                    title='Company Code'
                                    field='insuranceCompanyCode'
                                    width={200}
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.insuranceCompanyCode })}
                                />
                                <Column
                                    title='Company Name'
                                    field='insuranceCompanyName'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.insuranceCompanyName })}
                                />
                                <Column
                                    title='Company Email'
                                    field='insuranceCompanyEmail'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.insuranceCompanyEmail })}
                                />
                                <Column
                                    title='Company Phone'
                                    field='insuranceCompanyPhoneNumber'
                                    cell={(props) => renderTooltipCell({ ...props, content: props.dataItem.insuranceCompanyPhoneNumber })}
                                />
                                <Column
                                    title='Status'
                                    field='active'
                                    width={150}
                                    headerClassName="center-header"
                                    cell={(props) => renderswitch({ ...props, content: props.dataItem.active })}
                                />
                                {userinfo?.isfullaccess == 1 && (
                                    <Column
                                        title='Action'
                                        width={100}
                                        cell={(props) => renderaction({ ...props, content: props.dataItem.insuranceId })}
                                    />
                                )}
                            </Grid>
                        </Tooltip>
                    </div>
                </div>
            </div>

            <AddInsuranceModal
                openAddInsuranceModal={openAddInsuranceModal}
                setOpenAddInsuranceModal={setOpenAddInsuranceModal}
                setGridLoading={setGridLoading}
                insuranceGrid={insuranceGrid}
                setInsuranceId={setInsuranceId}
                insuranceId={insuranceId}
                setEditData={setEditData}
                editData={editData}
            />
        </>
    )
}

export default Insurance;