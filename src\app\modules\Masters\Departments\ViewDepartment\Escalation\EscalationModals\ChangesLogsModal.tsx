import { orderBy } from '@progress/kendo-data-query';
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from '@progress/kendo-react-tooltip';
import { useEffect, useState } from 'react';
import { Col, Modal, Row } from 'react-bootstrap';
import { IoIosArrowForward } from 'react-icons/io';
import { SlClose } from 'react-icons/sl';
import { useNavigate } from 'react-router';
import usersvg from "../../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
// import ViewStatusModal from './ViewStatusModal';
import { escalationService } from '../escalation.helper';
import SwalMessage from '../../../../../common/SwalMessage';
import encryptDecryptUtil from '../../../../../../utils/encrypt-decrypt-util';
import Spinner from '../../../../../common/Spinner';
import ViewStatusModal from './ViewStatusModal';

const ChangesLogsModal = ({ openChangeLogsModal, setOpenChangeLogsModal, getModule, setGetModule }: any) => {
    const pageSizesArray = [
        { label: "5", value: 5 },
        { label: "10", value: 10 },
        { label: "15", value: 15 },
        { label: "All", value: 100 },
    ];
    const initialDataState: any = { skip: 0, take: 10 };
    const initialSort: Array<any> = [{ field: "User Name", dir: "asc" }];
    const [openStatusModal, setopenStatusModal] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [sort, setSort] = useState(initialSort);
    const [page, setPage] = useState<any>(initialDataState);
    const [totalCount, setTotalCount] = useState<any>();
    const [gridData, setGridData] = useState<any[]>([])
    const [logsData, setLogsData] = useState<any[]>([])
    const [search, setSearch] = useState<string>("")

    const [pageSizeValue, setPageSizeValue] = useState<
        number | string | undefined
    >(initialDataState.take);
    const pageNumber = Math.floor(page.skip / page.take) + 1;

    const navigate = useNavigate()

    const pageChange = (event: any) => {
        const { skip, take } = event.page;
        const targetEvent = event.targetEvent as any;
        const newTake = targetEvent.value == "All" ? totalCount : take;
        const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

        setPage({ skip, take: newTake });
        setPageSizeValue(newPageSizeValue);
        // console.log("Page size value:", newPageSizeValue);
    };
    const renderTooltipCell = (props: any) => {
        const { dataItem, field, content } = props;
        const ticketIdMatch = content.match(/created a ticket (\d+)/i);
        const ticketId = ticketIdMatch ? ticketIdMatch[1] : null;

        const handleTicketClick = () => {
            if (ticketId) {
                // Assuming your ticket detail route looks like /ticketdetail/:ticketId
                navigate(`/ticketdetail/${ticketId}`);
                setOpenChangeLogsModal(false); // close modal if needed
            }
        };

        return (
            <>
                <td className="k-table-td">
                    <div className="d-flex align-items-center justify-content-between">
                        <span className="ellipsis-cell" title={content}>
                            {content}
                        </span>
                        {field === "createdOn" && dataItem?.isShow != 0 ? (
                            <>
                                <span onClick={() => { setLogsData(dataItem?.values), setopenStatusModal(true) }} className='cursor-pointer'>
                                    <IoIosArrowForward className="arrow-icon" />
                                </span>
                            </>
                        ) : null}
                    </div>

                </td>
            </>
        );
    }

    const renderUserandimage = (props: any) => {
        const { dataItem, field, content } = props;
        return (<>
            <td
                className="k-table-td cursor-pointer"
                title={content}
            >
                <div className="d-flex align-items-center">
                    {/* <img src={dataItem?.performedByUserImage ? getImage(dataItem?.performedByUserImage) : usersvg} className="user-image"></img> */}
                    {/* <span
                title={content}
                className="user-name"
                style={{
                  display: "inlineBlock",
                  paddingLeft: "10px",
                  verticalAlign: "middle",
                  lineHeight: "32px",
                }}
              > */}
                    <span className="user-name ellipsis-cell">
                        <span title={content}>{dataItem[field]}</span>
                        <span className="" title={dataItem?.createdBy}>
                            {dataItem?.createdBy}
                        </span>
                    </span>
                    {/* </span> */}
                </div>
            </td>
        </>
        );
    };

    const fetchLogsData = async () => {
        setLoading(true);
        try {
            const payload = {
                page: pageNumber,
                size: page.take,
                search: search ? search : "",
                sortingColumns: [
                    {
                        sortOrder: 0,
                        columnName: ""
                    }
                ],
                id: getModule?.escalationId
            }
            let keyinfo = JSON.parse(localStorage.keyinfo);

            const response = await escalationService
                .getescalationlogs(payload)
                .then((response: any) => {
                    setLoading(false);
                    return response?.data;
                })
                .catch((e: any) => {
                    if (e.response.status == 401) {
                        // localStorage.removeItem("islogin");
                        navigate("/dashboard");
                        // navigate(0);
                    }
                    SwalMessage(null, e.message, "Ok", "error", false);
                });

            if (response.success == true) {
                const result = encryptDecryptUtil.decryptData(
                    response.data,
                    keyinfo.syckey
                );
                const encResponse = JSON.parse(result);
                console.log("encResponse", encResponse)
                if (encResponse?.data?.length > 0) {
                    setGridData(encResponse?.data);

                } else {
                    setGridData([])
                }
                setTotalCount(encResponse?.totalCount)
            } else {
                SwalMessage(null, response.errormsg, "Ok", "error", false);
            }
        } catch (error) {
            console.error("Error fetching data:", error);
            SwalMessage(null, "Something Went Wrong.", "Ok", "error", false);
            setLoading(false)

        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        if (openChangeLogsModal) {
            const timeoutId = setTimeout(() => {
                fetchLogsData();
            }, 300);
            return () => {
                clearTimeout(timeoutId);
            };
        }
    }, [openChangeLogsModal, page, search]);

    useEffect(() => {
        if (!openChangeLogsModal) {
            setGridData([])
            setGetModule("")
        }
    }, [openChangeLogsModal])

    return (
        <>
            <Modal
                className="modal-right-full"
                scrollable={true}
                show={openChangeLogsModal}
            >
                <Modal.Header className="p-0 mb-4">
                    <Row className="d-in ">
                        <Col xs={6} className="mt-auto mb-auto">
                            <h2 className="mb-0">Change Logs</h2>
                        </Col>
                        <Col xs={6} className=" text-end mb-3">
                            <span className="btn rx-btn " onClick={() => setOpenChangeLogsModal(false)}>
                                <SlClose className="btn-icon-custom" />
                                Close
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="p-0">
                    {loading && <Spinner />}
                    <Row>
                        <Col xl={3} lg={3} md={4} sm={12} className="mobile-margin text-end">
                            <input
                                type="text"
                                className="form-control"
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                                placeholder="Search..."
                            />
                        </Col>
                    </Row>
                    <div className="mt-4">
                        <div className="card-body p-0">
                            <div className="table_div" style={{ width: "100%" }}>
                                <Tooltip position="bottom" anchorElement="target">
                                    <Grid
                                        data={orderBy(gridData, sort)}
                                        skip={page.skip}
                                        take={page.take}
                                        total={totalCount}
                                        pageable={{
                                            buttonCount: 4,
                                            pageSizes: pageSizesArray.map((size: any) => size.label),
                                            pageSizeValue: pageSizeValue,
                                            // responsive:true,
                                        }}
                                        onPageChange={pageChange}
                                        sortable={true}
                                        sort={sort}
                                        onSortChange={(e: any) => {
                                            setSort(e.sort);
                                        }}
                                    >
                                        <Column
                                            title="Created By"
                                            field="createdBy"
                                            // width="180px"
                                            cell={(props) =>
                                                renderUserandimage({
                                                    ...props,
                                                    content: props.dataItem.createdBy,
                                                })
                                            }
                                        />
                                        {/* <Column
                                            title="Action Taken"
                                            field="description"
                                            cell={(props: any) =>
                                                renderTooltipCell({
                                                    ...props,
                                                    content: props.dataItem.description,
                                                })
                                            }
                                        /> */}
                                        <Column
                                            title="Created On"
                                            field="createdOn"
                                            cell={(props: any) =>
                                                renderTooltipCell({
                                                    ...props,
                                                    content: props.dataItem.createdOn,
                                                })
                                            }
                                        />
                                    </Grid>
                                </Tooltip>
                            </div>
                        </div>
                    </div>
                </Modal.Body>
            </Modal>

            <ViewStatusModal
                setopenStatusModal={setopenStatusModal}
                openStatusModal={openStatusModal}
                logsData={logsData}
                setLogsData={setLogsData}
            />
        </>
    )
}

export default ChangesLogsModal