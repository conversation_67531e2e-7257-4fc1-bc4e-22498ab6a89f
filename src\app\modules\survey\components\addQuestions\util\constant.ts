import { QuestionOptions } from "../../../../../apis/type";
import { QuestionType } from "../../../createSurvey/types";

export const enum ResponseType {
  MULTIPLE_CHOICE = "MULTIPLE_CHOICE",
  SCALE = "SCALE",
  YES_NO = "YES_NO",
  THUMBS_UP_DOWN = "THUMBS_UP_DOWN",
  COMMENT = "COMMENT",
  EMOJI = "EMOJI",
  RATING = "RATING",
  NET_PROMOTER_SCORE = "NET_PROMOTER_SCORE",
}

// Utility functions for generating options based on response type
export const generateScaleOptions = (
  startScale: number,
  endScale: number
): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = startScale; i <= endScale; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export const generateRatingOptions = (maxRating: number): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = 1; i <= maxRating; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export const generateNpsOptions = (
  startNps: number = 0,
  endNps: number = 10
): QuestionOptions[] => {
  const options: QuestionOptions[] = [];
  for (let i = startNps; i <= endNps; i++) {
    options.push({
      optionText: i.toString(),
      optionValue: i.toString(),
      isTicketRequired: false,
    });
  }
  return options;
};

export const generateEmojiOptions = (): QuestionOptions[] => {
  return emojiOptions.map((emoji) => ({
    optionText: emoji.label,
    optionValue: emoji.value,
    isTicketRequired: false,
  }));
};

export const generateThumbsOptions = (): QuestionOptions[] => {
  return [
    {
      optionText: "Thumbs Up",
      optionValue: "thumb_up",
      isTicketRequired: false,
    },
    {
      optionText: "Thumbs Down",
      optionValue: "thumb_down",
      isTicketRequired: false,
    },
  ];
};

export const generateYesNoOptions = (): QuestionOptions[] => {
  return [
    {
      optionText: "Yes",
      optionValue: "Yes",
      isTicketRequired: false,
    },
    {
      optionText: "No",
      optionValue: "No",
      isTicketRequired: false,
    },
  ];
};

export const generateMultipleChoiceOptions = (
  optionTexts: string[]
): QuestionOptions[] => {
  return optionTexts.map((text) => ({
    optionText: text,
    optionValue: text,
    isTicketRequired: false,
  }));
};

export type AttachmentType = "IMAGE" | "VIDEO" | "AUDIO";

export interface QuestionFormValues {
  questionText: string;
  responseType: ResponseType;
  allowAttachment: boolean;
  attachmentType?: AttachmentType[];
  allowComment: boolean;
  autoTicketGeneration: boolean;
  allowBranching: boolean;
  isRequired: boolean;
  // Multiple Choice and other options
  options?: QuestionOptions[];
  // Rating - rattingIcon managed by form, ratting managed internally by RatingViewer
  rattingIcon?: string;
}

export const QUESTION_TYPE_OPTIONS = [
  { label: "Multiple Choice", value: "MULTIPLE_CHOICE" },
  { label: "Scale", value: "SCALE" },
  { label: "Yes / No", value: "YES_NO" },
  { label: "Thumbs Up/ Thumbs Down", value: "THUMBS_UP_DOWN" },
  { label: "Comment", value: "COMMENT" },
  { label: "Emoji", value: "EMOJI" },
  { label: "Rating", value: "RATING" },
  { label: "Net Promoter Score", value: "NET_PROMOTER_SCORE" },
];

export const emojiOptions = [
  {
    value: "slightly_smiling_face",
    icon: "🙂",
    label: "Slightly Smiling Face",
  },
  {
    value: "slightly_frowning_face",
    icon: "🙁",
    label: "Slightly Frowning Face",
  },
  { value: "neutral_face", icon: "😐", label: "Neutral Face" },
];

export const thumbsOptions = [
  { value: "thumb_up", label: "Thumb Up" },
  { value: "thumb_down", label: "Thumb Down" },
];

export const mockQuestion: any[] = [
  {
    id: "6c7a2b4a564933424a6866594e4c6f75765a6e4c45413d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Test",
    responseType: "MULTIPLE_CHOICE",
    options: [
      {
        id: "744c77396e4b2f413551757a634868426e59457854673d3d",
        optionText: "Test",
        optionValue: "Test",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "62697432313062364345725068344d485146596439413d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question 2 scale",
    responseType: "SCALE",
    options: [
      {
        id: "36386d71727044323450526b3155474e652f6e7935413d3d",
        optionText: "0",
        optionValue: "0",
        isTicketRequired: false,
      },
      {
        id: "42526439724c50324439316d684a5945574d724b36513d3d",
        optionText: "1",
        optionValue: "1",
        isTicketRequired: false,
      },
      {
        id: "3676725652304a656949657a657543784b4a454446673d3d",
        optionText: "2",
        optionValue: "2",
        isTicketRequired: false,
      },
      {
        id: "44566e376d71327a786e6136764b307936506e756e413d3d",
        optionText: "3",
        optionValue: "3",
        isTicketRequired: false,
      },
      {
        id: "6a3567396c3534646734734e6a2b6964754742394f673d3d",
        optionText: "4",
        optionValue: "4",
        isTicketRequired: false,
      },
      {
        id: "756a6c4e35304e6530693549726976394233467049413d3d",
        optionText: "5",
        optionValue: "5",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "6468537548736e446b364569316531364e49762f65413d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question 3",
    responseType: "YES_NO",
    options: [
      {
        id: "30775565786f707163486e59787453674d35467561673d3d",
        optionText: "Yes",
        optionValue: "Yes",
        isTicketRequired: false,
      },
      {
        id: "6e434c6874316b68395a63794d7a786c797352452f773d3d",
        optionText: "No",
        optionValue: "No",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "5762646b544966497856637543362b524e4b567543773d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question4",
    responseType: "THUMBS_UP_DOWN",
    options: [
      {
        id: "43326d4e447676363677692f674442416664476678673d3d",
        optionText: "Thumbs Up",
        optionValue: "thumb_up",
        isTicketRequired: false,
      },
      {
        id: "32576855695a3356557151587368564a692b757834673d3d",
        optionText: "Thumbs Down",
        optionValue: "thumb_down",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "556b4e4944696c466d2f376247672b624b706c4830773d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question 5",
    responseType: "COMMENT",
    options: [],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "7a7672546a517933512f6964654c45696e2f65724d673d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question 6",
    responseType: "EMOJI",
    options: [
      {
        id: "48624e7a4f7262316d663431593475394f30537a49513d3d",
        optionText: "Slightly Smiling Face",
        optionValue: "slightly_smiling_face",
        isTicketRequired: false,
      },
      {
        id: "4e4145374e566472593875723551326450735a5a55673d3d",
        optionText: "Slightly Frowning Face",
        optionValue: "slightly_frowning_face",
        isTicketRequired: false,
      },
      {
        id: "2b426333496d4631714a5237696a30635056373048413d3d",
        optionText: "Neutral Face",
        optionValue: "neutral_face",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "366d6232716547364f347659757244467148645635773d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Questio 7",
    responseType: "RATING",
    options: [
      {
        id: "7853434e5452446d625963336a74392f5258674979673d3d",
        optionText: "1",
        optionValue: "1",
        isTicketRequired: false,
      },
      {
        id: "3233597a3659373242334c34454e6161576b707561513d3d",
        optionText: "2",
        optionValue: "2",
        isTicketRequired: false,
      },
      {
        id: "61686d6e6e30524d6c6a4b6a6368694f7739544249513d3d",
        optionText: "3",
        optionValue: "3",
        isTicketRequired: false,
      },
      {
        id: "44446d56694165744d475731736949564643487034513d3d",
        optionText: "4",
        optionValue: "4",
        isTicketRequired: false,
      },
      {
        id: "4a6268697943504533486e666c65657139566f6e50413d3d",
        optionText: "5",
        optionValue: "5",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
  {
    id: "75525a385930304672527738623962425978356344513d3d",
    surveyId: "4d4b59556c72667a35423537624456626f5669514c413d3d",
    questionText: "Question 9",
    responseType: "NET_PROMOTER_SCORE",
    options: [
      {
        id: "6a6451383266643156734d3430654f505430746c4a513d3d",
        optionText: "0",
        optionValue: "0",
        isTicketRequired: false,
      },
      {
        id: "344a5859676a6e7159484a712b587142595646576a673d3d",
        optionText: "1",
        optionValue: "1",
        isTicketRequired: false,
      },
      {
        id: "476b3771754139614a3954586d63354f6e2f4c6249513d3d",
        optionText: "2",
        optionValue: "2",
        isTicketRequired: false,
      },
      {
        id: "76784136452b3434786c354a4558744a4672785666513d3d",
        optionText: "3",
        optionValue: "3",
        isTicketRequired: false,
      },
      {
        id: "4c4270727a74634e65443253564d4b4577584f3061513d3d",
        optionText: "4",
        optionValue: "4",
        isTicketRequired: false,
      },
      {
        id: "6856656639386b6a535777346859384f7570627032673d3d",
        optionText: "5",
        optionValue: "5",
        isTicketRequired: false,
      },
      {
        id: "69504e373944327646345a67714364567a456f4b39413d3d",
        optionText: "6",
        optionValue: "6",
        isTicketRequired: false,
      },
      {
        id: "37704b41445a67654e64726a4877436f623231486f773d3d",
        optionText: "7",
        optionValue: "7",
        isTicketRequired: false,
      },
      {
        id: "6b385561476f63394443454f34516449704c386953513d3d",
        optionText: "8",
        optionValue: "8",
        isTicketRequired: false,
      },
      {
        id: "506b6a31575564464979734b734f4b2f766e494d50513d3d",
        optionText: "9",
        optionValue: "9",
        isTicketRequired: false,
      },
      {
        id: "33626837446d795761683578494136686a49576c61673d3d",
        optionText: "10",
        optionValue: "10",
        isTicketRequired: false,
      },
    ],
    rattingIcon: "star",
    isRequired: false,
    allowAttachment: false,
    attachmentType: [],
    allowComment: false,
    autoTicketGeneration: false,
    allowBranching: false,
    parentId: null,
    questionOrder: 0,
    branchingQuestion: null,
  },
];
