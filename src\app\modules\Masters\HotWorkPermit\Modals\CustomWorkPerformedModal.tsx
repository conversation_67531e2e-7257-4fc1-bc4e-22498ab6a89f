import { useEffect, useState } from "react";
import { <PERSON>, Col, Modal, Row } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON>ck, FaPlus } from "react-icons/fa";
import {
    MdModeEdit,
    MdOutlineDeleteForever,
    MdOutlineMenu,
} from "react-icons/md";
import { RxCross2 } from "react-icons/rx";
import { DndContext, closestCorners } from "@dnd-kit/core";
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import SwalMessage from "../../../common/SwalMessage";
import { hotworkspermitService } from "../HotWorkPermit.helper";
import { ClipLoader } from "react-spinners";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";
import HotProgressBar from "../HotProgressBar";
import { useNavigate } from "react-router";

const CustomWorkPerformedModal = ({
    openCustomWorkPerformedModal,
    setOpenCustomWorkPerformedModal,
    data,
    setData,
    setHwpid,
    hwptid,
    step,
    totalSteps,
    onSave,
    handleSubmit
}: any) => {
    const [sections, setSections] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [customWorkPerformedName, setCustomWorkPerformedName] = useState<string>("")
    const [errors, setErrors] = useState<any>({})
    const spinner = (
        <div className="spinner-page">
            <ClipLoader size={60} className="spinner" />
        </div>
    );

    useEffect(() => {
        if (!openCustomWorkPerformedModal) {
            setSections([]);
            setCustomWorkPerformedName("");
            setData([{
                type: "Precaution",
                sections: [],
            },
            {
                type: "Post Work",
                sections: [],
            },]);
        }
    }, [openCustomWorkPerformedModal]);
    // Sortable item component for questions
    const SortableItem = ({
        item,
        onDelete,
        onEdit,
    }: {
        item: any;
        onDelete: any;
        onEdit: any;
    }) => {
        const { attributes, listeners, setNodeRef, transform, transition } =
            useSortable({ id: item.questionid });
        const style = {
            transform: CSS.Transform.toString(transform),
            transition,
        };
        return (
            <div ref={setNodeRef} style={style}>
                <Card className="custom-card mb-3">
                    <Card.Body className="p-3">
                        <Row className="ps-5 align-items-center">
                            <Col sm={10}>
                                <li className="mb-0">
                                    {item.isEditing ? (
                                        <input
                                            type="text"
                                            value={item.question}
                                            onChange={(e) => onEdit(e.target.value)}
                                            autoFocus
                                            className="form-control mb-0"
                                        />
                                    ) : (
                                        <span>{item.question}</span>
                                    )}
                                </li>
                            </Col>
                            <Col sm={2}>
                                {item?.isEditing ? (
                                    <>
                                        <span
                                            className="text-muted cursor-pointer me-2"
                                            onClick={onDelete}>
                                            <MdOutlineDeleteForever size={20} />
                                        </span>
                                        <span
                                            className="cursor-pointer"
                                            onClick={() => {
                                                if (!item.question.trim()) {
                                                    SwalMessage(
                                                        null,
                                                        "Precaution cannot be empty!",
                                                        "Ok",
                                                        "warning",
                                                        false
                                                    );
                                                    return;
                                                }
                                                item.isEditing = false;
                                                setSections([...sections]);
                                            }}>
                                            <FaCheck size={16} />
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        <span
                                            className="text-muted cursor-pointer me-2"
                                            onClick={onDelete}>
                                            <MdOutlineDeleteForever size={20} />
                                        </span>
                                        <span
                                            className="text-muted cursor-pointer"
                                            {...attributes}
                                            {...listeners}>
                                            <MdOutlineMenu size={20} />
                                        </span>
                                    </>
                                )}
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>
            </div>
        );
    };

    // Handle drag and drop logic
    const handleDragEnd = (
        event: DragEvent,
        typeIndex: number,
        sectionIndex: number
    ) => {
        const { active, over }: any = event;

        if (!over || active.id === over.id) return;

        setData((prev: any) => {
            const updated = [...prev];
            const questions = updated[typeIndex].sections[sectionIndex].questions;

            const oldIndex = questions.findIndex((q: any) => q.questionid === active.id);
            const newIndex = questions.findIndex((q: any) => q.questionid === over.id);

            const [movedItem] = questions.splice(oldIndex, 1);
            questions.splice(newIndex, 0, movedItem);

            return updated;
        });
    };


    // add new sections
    const handleAddSection = (typeIndex: number) => {
        setData((prev: any) => {
            const updated = [...prev];
            updated[typeIndex].sections.push({
                sectionname: "",
                isEditing: true,
                questions: [],
            });
            return updated;
        });
    };



    // add new precautions
    const handleAddPrecaution = (typeIndex: number, sectionIndex: number) => {
        setData((prev: any) => {
            const updated = [...prev];
            updated[typeIndex].sections[sectionIndex].questions.push({
                questionid: new Date().toISOString(),
                question: "",
                isEditing: true,
                isLinkedQuestion: 0,
                linkedQuestionId: null
            });
            return updated;
        });
    };

    return (
        <>
            {loading && spinner}
            <Modal
                className="modal-right modal-right-small p-0"
                scrollable={true}
                show={openCustomWorkPerformedModal}
                onHide={() => setOpenCustomWorkPerformedModal(false)}>
                <Modal.Header className="border-0 p-0">
                    <Row className="align-items-baseline">
                        <Col xs={10} className="mt-auto mb-auto">
                            <h2 className="mb-0">Add Custom Work Performed</h2>
                        </Col>
                        <Col xs={2} className="text-end mb-3">
                            <span
                                className="close-btn cursor-pointer"
                                onClick={() => setOpenCustomWorkPerformedModal(false)}>
                                <RxCross2 fontSize={20} />
                            </span>
                        </Col>
                    </Row>
                </Modal.Header>
                <Modal.Body className="mt-8 p-0">
                    <Row className="align-items-baseline justify-content-start gap-5">
                        {/* <Col sm={12} className="mb-3">
                            <span className="form-title">
                                Custom Work To Be Performed<span className="text-danger">*</span>
                            </span>
                            <input
                                className="form-control"
                                value={customWorkPerformedName}
                                onChange={(e) => setCustomWorkPerformedName(e.target.value)}
                                placeholder="Enter Custom Work To Be Performed"
                                autoFocus
                            />
                            {errors?.customWorkPerformedName && (
                                <div className="text-danger mt-1">
                                    {errors.customWorkPerformedName}
                                </div>
                            )}
                        </Col> */}
                        <Col sm={12}>
                            {data.map((typeItem: any, typeIndex: any) => (
                                <div key={typeItem.type} className="mb-4">
                                    <div className="mb-3 mobile-margin">
                                        <span className="form-label">{typeItem.type}</span>
                                    </div>

                                    {typeItem.sections.map((section: any, sectionIndex: any) => (
                                        <Card className="custom-card mb-2" key={sectionIndex}>
                                            <Card.Body className="p-3">
                                                <Row className="align-items-center">
                                                    <Col sm={10}>
                                                        {section.isEditing ? (
                                                            <input
                                                                type="text"
                                                                value={section.sectionname}
                                                                onChange={(e) => {
                                                                    const updated: any = [...data];
                                                                    updated[typeIndex].sections[sectionIndex].sectionname = e.target.value;
                                                                    setData(updated);
                                                                }}
                                                                autoFocus
                                                                className="form-control mb-1"
                                                            />
                                                        ) : (
                                                            <div className="form-title mb-2">{section.sectionname}</div>
                                                        )}
                                                    </Col>
                                                    <Col sm={2}>
                                                        {section.isEditing ? (
                                                            <>
                                                                <span
                                                                    className="cursor-pointer me-2 text-danger"
                                                                    onClick={() => {
                                                                        const updated = [...data];
                                                                        updated[typeIndex].sections.splice(sectionIndex, 1);
                                                                        setData(updated);
                                                                    }}
                                                                >
                                                                    <MdOutlineDeleteForever size={20} />
                                                                </span>
                                                                <span
                                                                    className="cursor-pointer"
                                                                    onClick={() => {
                                                                        if (!section.sectionname.trim()) {
                                                                            SwalMessage(null, "Section name cannot be empty!", "OK", "warning", false);
                                                                            return;
                                                                        }
                                                                        const updated: any = [...data];
                                                                        updated[typeIndex].sections[sectionIndex].isEditing = false;
                                                                        setData(updated);
                                                                    }}
                                                                >
                                                                    <FaCheck size={16} />
                                                                </span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <span
                                                                    className="cursor-pointer me-2 text-danger"
                                                                    onClick={() => {
                                                                        const updated = [...data];
                                                                        updated[typeIndex].sections.splice(sectionIndex, 1);
                                                                        setData(updated);
                                                                    }}
                                                                >
                                                                    <MdOutlineDeleteForever size={20} />
                                                                </span>
                                                                <span
                                                                    className="cursor-pointer text-muted"
                                                                    onClick={() => {
                                                                        const updated: any = [...data];
                                                                        updated[typeIndex].sections[sectionIndex].isEditing = true;
                                                                        setData(updated);
                                                                    }}
                                                                >
                                                                    <MdModeEdit size={20} />
                                                                </span>
                                                            </>
                                                        )}
                                                    </Col>

                                                    <Col sm={12}>
                                                        <DndContext
                                                            onDragEnd={(event: any) =>
                                                                handleDragEnd(event, typeIndex, sectionIndex)
                                                            }
                                                            collisionDetection={closestCorners}
                                                        >
                                                            <SortableContext
                                                                items={section.questions.map((q: any) => q.questionid)}
                                                                strategy={verticalListSortingStrategy}
                                                            >
                                                                <ol className="p-0">
                                                                    {section.questions.map((q: any, qIndex: number) => (
                                                                        <SortableItem
                                                                            key={q.questionid}
                                                                            item={q}
                                                                            onDelete={() => {
                                                                                const updated: any = [...data];
                                                                                updated[typeIndex].sections[sectionIndex].questions =
                                                                                    updated[typeIndex].sections[sectionIndex].questions.filter(
                                                                                        (item: any) => item.questionid !== q.questionid
                                                                                    );
                                                                                setData(updated);
                                                                            }}
                                                                            onEdit={(newQuestion: string) => {
                                                                                const updated: any = [...data];
                                                                                updated[typeIndex].sections[sectionIndex].questions[qIndex].question =
                                                                                    newQuestion;
                                                                                setData(updated);
                                                                            }}
                                                                        />
                                                                    ))}
                                                                </ol>
                                                            </SortableContext>
                                                        </DndContext>
                                                    </Col>

                                                    <Col sm={12}>
                                                        <span
                                                            className="btn rx-btn mt-2"
                                                            onClick={() => handleAddPrecaution(typeIndex, sectionIndex)}
                                                        >
                                                            <FaPlus className="me-2" /> New Precaution
                                                        </span>
                                                    </Col>
                                                </Row>
                                            </Card.Body>
                                        </Card>
                                    ))}

                                    <span
                                        className="btn rx-btn mt-2"
                                        onClick={() => handleAddSection(typeIndex)}
                                    >
                                        <FaPlus className="me-2" /> Add Section in {typeItem.type}
                                    </span>
                                </div>
                            ))}

                        </Col>
                        {/* <Col sm={12}>
                            <span className="btn rx-btn" onClick={handleAddSections}>
                                <FaPlus className="me-2" /> New Section
                            </span>
                        </Col> */}
                    </Row>
                </Modal.Body>
                <Modal.Footer className="border-0 p-0 d-block">
                    <Row className="align-items-center">
                        <Col sm={6}>
                            {/* {isDisabled && ( */}
                            <HotProgressBar currentStep={step} totalSteps={totalSteps} />
                            {/* )} */}
                        </Col>
                        <Col sm={6} className="text-end">
                            <button
                                className="btn rx-btn"
                                // disabled={!isDisabled}
                                onClick={() => { handleSubmit() }}>
                                Save & Continue
                            </button>
                        </Col>
                    </Row>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default CustomWorkPerformedModal;
