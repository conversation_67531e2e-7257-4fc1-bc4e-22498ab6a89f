import { APIs } from "../../../../serverconfig/apiURLs";
import axiosInstance from "../../../../serverconfig/axiosInstance";
import encryptDecryptUtil from "../../../../utils/encrypt-decrypt-util";




class InsuranceService {

    async getGridData(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.GRIDCALLS.insurancegrid, {
            encryptedData: payloadData,
        });
    }

    async createInsurance(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.createinsurance, {
            encryptedData: payloadData,
        });
    }

    async getinsurancedata(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.getinsurancedata, {
            encryptedData: payloadData,
        });
    }

    async deleteinstance(payload: any) {
        let keyinfo = JSON.parse(localStorage.keyinfo);
        const payloadData = encryptDecryptUtil.encryptData(
            JSON.stringify(payload),
            keyinfo.syckey
        );
        return await axiosInstance.post(APIs.ALL_HEADERS.deleteinsurance, {
            encryptedData: payloadData,
        });
    }

}
export const insuranceService = new InsuranceService();
