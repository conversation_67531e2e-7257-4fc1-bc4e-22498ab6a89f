import React from "react";
import { <PERSON>ton, ButtonGroup } from "react-bootstrap";
import { LuDownload, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/lu";
import { SortingSvg } from "../../../../utils/SvgUtils";
import { PiUploadBold } from "react-icons/pi";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { useActionWithFeedback } from "../../../../hooks/useActionWithFeedback";
import { Spinner } from "react-bootstrap";
import FilterManager from "./filters/FilterManager";
import { ExportOptions } from "../types/chartTypes";
import { ExportService } from "../services/exportService";
import { useChartContext } from "../context/ChartContext";

interface ActionButtonsProps {
  onFiltersChange?: (filters: any) => void;
  currentFilters?: any;
  onExport?: (options: ExportOptions) => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onFiltersChange,
  currentFilters,
  onExport,
}) => {
  const { chartData } = useChartContext();

  const downloadPDF = async () => {
    // Target the main content area to capture
    const contentElement = document.querySelector(".rounded-5");

    if (!contentElement) {
      console.error("Content element not found");
      return;
    }

    try {
      // Use html2canvas to capture the content
      const canvas = await html2canvas(contentElement as HTMLElement, {
        scale: 2, // Higher scale for better quality
        useCORS: true, // Enable CORS for external images
        logging: false,
        backgroundColor: null,
        onclone: (document: Document) => {
          // Any modifications to the cloned document before rendering
          return document;
        },
      });

      // Calculate dimensions
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Create PDF document
      const pdf = new jsPDF("p", "mm", "a4");
      let currentPage = 1;

      // Add first page
      pdf.addImage(
        canvas.toDataURL("image/png", 1.0),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      );
      heightLeft -= pageHeight;

      // Add additional pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL("image/png", 1.0),
          "PNG",
          0,
          position,
          imgWidth,
          imgHeight
        );
        heightLeft -= pageHeight;
        currentPage++;
      }

      // Save the PDF
      pdf.save("survey-report.pdf");
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw error; // Re-throw to be caught by the hook
    }
  };

  const { status: pdfStatus, executeAction: executePdfDownload } =
    useActionWithFeedback(downloadPDF);

  // Render the appropriate content based on status
  const renderDownloadButtonContent = () => {
    switch (pdfStatus) {
      case "processing":
        return (
          <>
            <Spinner
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
            />
            {/* <span className="ms-1">Processing...</span> */}
          </>
        );
      case "success":
        return (
          <>
            <LuCheck size={18} />
            {/* <span className="ms-1">Done!</span> */}
          </>
        );
      default:
        return <LuDownload size={18} />;
    }
  };

  const handleExcelExport = async () => {
    try {
      await ExportService.exportToExcel(
        chartData,
        [], // responses - would come from API in real implementation
        {
          format: "excel",
          includeCharts: true,
          includeRawData: true,
          fileName: "survey-report.xlsx",
        }
      );

      if (onExport) {
        onExport({
          format: "excel",
          includeCharts: true,
          includeRawData: true,
          fileName: "survey-report.xlsx",
        });
      }
    } catch (error) {
      console.error("Error generating Excel file:", error);
    }
  };

  return (
    <div className="d-flex gap-2 justify-content-end">
      <ButtonGroup>
        <div className="user-image d-flex align-items-center justify-content-center text-center mt-3  ms-3  cursor-pointer">
          <SortingSvg width="17" height="17" className="svgicon " />
        </div>

        {/* Filter Manager Component */}
        <div className="user-image d-flex align-items-center justify-content-center text-center mt-3  ms-3  cursor-pointer">
          <FilterManager
            onFiltersChange={onFiltersChange}
            currentFilters={currentFilters}
          />
        </div>

        <div
          className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer"
          onClick={executePdfDownload}
          title="Download as PDF"
        >
          {renderDownloadButtonContent()}
        </div>
      </ButtonGroup>
      <Button
        variant="outline-light"
        size="sm"
        className="border border-white py-1 mt-3"
        onClick={handleExcelExport}
      >
        <PiUploadBold className="btn-icon-custom" />
        Export as Excel
      </Button>
    </div>
  );
};

export default ActionButtons;
