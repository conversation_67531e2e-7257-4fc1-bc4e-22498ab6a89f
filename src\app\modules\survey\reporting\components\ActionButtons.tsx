import React, { useState } from "react";
import { Button, ButtonGroup } from "react-bootstrap";
import { LuDownload, Lu<PERSON>heck } from "react-icons/lu";
import { FilterSvg, SortingSvg } from "../../../../utils/SvgUtils";
import { PiUploadBold } from "react-icons/pi";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { useActionWithFeedback } from "../../../../hooks/useActionWithFeedback";
import { Spinner } from "react-bootstrap";

import ResponseListFilterModal from "./filters/ResponseListFilterModal";
import ResponseListSortModal from "./filters/ResponseListSortModal";
import { ExportOptions, FilterState, SortState } from "../types/chartTypes";
import { ExportService } from "../services/exportService";
import { useChartContext } from "../context/ChartContext";

interface ActionButtonsProps {
  onExport?: (options: ExportOptions) => void;
  // Response List specific props
  onResponseListFiltersChange?: (filters: FilterState) => void;
  onResponseListSortChange?: (sort: SortState) => void;
  currentResponseListFilters?: FilterState;
  currentResponseListSort?: SortState;
  showResponseListFilter?: boolean;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onExport,
  onResponseListFiltersChange,
  onResponseListSortChange,
  currentResponseListFilters = {},
  currentResponseListSort = { field: "submittedAt", direction: "desc" },
  showResponseListFilter = true,
}) => {
  const [showResponseListFilterModal, setShowResponseListFilterModal] =
    useState(false);
  const [showResponseListSortModal, setShowResponseListSortModal] =
    useState(false);
  // Chart context available if needed
  useChartContext();

  // Count active response list filters
  const getActiveResponseListFilterCount = (): number => {
    if (!currentResponseListFilters || Object.keys(currentResponseListFilters).length === 0) {
      return 0;
    }

    let count = 0;
    Object.entries(currentResponseListFilters).forEach(([key, filterValue]) => {
      if (Array.isArray(filterValue) && filterValue.length > 0) {
        count++;
      } else if (
        filterValue &&
        typeof filterValue === "string" &&
        filterValue.trim() !== ""
      ) {
        count++;
      } else if (filterValue && typeof filterValue === "object" && filterValue !== null) {
        // Handle date range and score range objects
        const objValue = filterValue as Record<string, unknown>;
        if (key === 'dateRange') {
          if (objValue.start || objValue.end) {
            count++;
          }
        } else if (key === 'score') {
          if (objValue.min !== null && objValue.min !== undefined ||
              objValue.max !== null && objValue.max !== undefined) {
            count++;
          }
        } else if (Object.keys(objValue).length > 0) {
          count++;
        }
      }
    });
    return count;
  };

  const activeResponseListFilterCount = getActiveResponseListFilterCount();

  const downloadPDF = async () => {
    // Target the main content area to capture
    const contentElement = document.querySelector(".rounded-5");

    if (!contentElement) {
      console.error("Content element not found");
      return;
    }

    try {
      // Use html2canvas to capture the content
      const canvas = await html2canvas(contentElement as HTMLElement, {
        scale: 2, // Higher scale for better quality
        useCORS: true, // Enable CORS for external images
        logging: false,
        backgroundColor: null,
        onclone: (document: Document) => {
          // Any modifications to the cloned document before rendering
          return document;
        },
      });

      // Calculate dimensions
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 297; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Create PDF document
      const pdf = new jsPDF("p", "mm", "a4");

      // Add first page
      pdf.addImage(
        canvas.toDataURL("image/png", 1.0),
        "PNG",
        0,
        position,
        imgWidth,
        imgHeight
      );
      heightLeft -= pageHeight;

      // Add additional pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL("image/png", 1.0),
          "PNG",
          0,
          position,
          imgWidth,
          imgHeight
        );
        heightLeft -= pageHeight;
        // Continue to next page
      }

      // Save the PDF
      pdf.save("survey-report.pdf");
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw error; // Re-throw to be caught by the hook
    }
  };

  const { status: pdfStatus, executeAction: executePdfDownload } =
    useActionWithFeedback(downloadPDF);

  // Render the appropriate content based on status
  const renderDownloadButtonContent = () => {
    switch (pdfStatus) {
      case "processing":
        return (
          <>
            <Spinner
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
            />
            {/* <span className="ms-1">Processing...</span> */}
          </>
        );
      case "success":
        return (
          <>
            <LuCheck size={18} />
            {/* <span className="ms-1">Done!</span> */}
          </>
        );
      default:
        return <LuDownload size={18} />;
    }
  };

  const handleExcelExport = async () => {
    try {
      await ExportService.exportToExcel(
        [], // chartData - would come from context in real implementation
        [], // responses - would come from API in real implementation
        {
          format: "excel",
          includeCharts: true,
          includeRawData: true,
          fileName: "survey-report.xlsx",
        }
      );

      if (onExport) {
        onExport({
          format: "excel",
          includeCharts: true,
          includeRawData: true,
          fileName: "survey-report.xlsx",
        });
      }
    } catch (error) {
      console.error("Error generating Excel file:", error);
    }
  };

  return (
    <div className="d-flex gap-2 justify-content-end">
      <ButtonGroup>
        {/* Response List Sort - Direct Modal Trigger */}
        {showResponseListFilter && (
          <div
            className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer"
            onClick={() => setShowResponseListSortModal(true)}
            title="Sort Responses"
          >
            <SortingSvg width="17" height="17" className="svgicon" />
          </div>
        )}

        {/* Response List Filter - Direct Modal Trigger */}
        {showResponseListFilter && (
          <div
            className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer position-relative"
            onClick={() => setShowResponseListFilterModal(true)}
            title="Filter Responses"
          >
            <FilterSvg width="17" height="17" className="svgicon" />
            {activeResponseListFilterCount > 0 && (
              <span
                className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                style={{ fontSize: "0.6rem" }}
              >
                {activeResponseListFilterCount}
              </span>
            )}
          </div>
        )}



        <div
          className="user-image d-flex align-items-center justify-content-center text-center mt-3 ms-3 cursor-pointer"
          onClick={executePdfDownload}
          title="Download as PDF"
        >
          {renderDownloadButtonContent()}
        </div>
      </ButtonGroup>
      <Button
        variant="outline-light"
        size="sm"
        className="border border-white py-1 mt-3"
        onClick={handleExcelExport}
      >
        <PiUploadBold className="btn-icon-custom" />
        Export as Excel
      </Button>

      {/* Response List Filter Modal */}
      <ResponseListFilterModal
        isOpen={showResponseListFilterModal}
        onClose={() => setShowResponseListFilterModal(false)}
        onApply={() => {}}
        currentFilters={currentResponseListFilters}
        onFiltersChange={onResponseListFiltersChange}
        filterType="responseList"
      />

      {/* Response List Sort Modal */}
      <ResponseListSortModal
        isOpen={showResponseListSortModal}
        onClose={() => setShowResponseListSortModal(false)}
        onApply={() => {}}
        currentSort={currentResponseListSort}
        onSortChange={onResponseListSortChange}
        filterType="responseList"
      />
    </div>
  );
};

export default ActionButtons;
