import React, { useEffect, useState } from "react";
import { useGetSurveyFullDetailsMutation } from "../../apis/survaysAPI";
import { ISurveyFullDetails } from "../../apis/type";
import { Loader } from "../../component";
import SwalMessage from "../common/SwalMessage";
import SurveyAnswerContainer from "../surveySubmitAnswer/SurveyAnswerContainer";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import { Form, Formik } from "formik";
import { useAppDispatch } from "../../redux/store";
import { getSecurityDetails } from "../../redux/authSlice";

const SurveySubmitAnswer = () => {
  const { surveyId } = useSurveyUtil();
  const dispatch = useAppDispatch();

  const [getSurveyFullDetails, { isLoading }] =
    useGetSurveyFullDetailsMutation();
  const [surveyDetails, setSurveyDetails] = useState<ISurveyFullDetails | null>(
    null
  );
  const [isSecurityLoading, setIsSecurityLoading] = useState(false);
  const [footerInfo, setFooterInfo] = useState<any>(null);

  // Check for keyinfo and call security details API if needed
  useEffect(() => {
    const handleSecurityDetails = async () => {
      if (!localStorage.getItem("keyinfo")) {
        setIsSecurityLoading(true);
        try {
          const response = await dispatch(getSecurityDetails()).unwrap();
          const responseData: any = response;
          if (responseData && responseData.success === true) {
            localStorage.setItem("keyinfo", JSON.stringify(responseData.data));
            setFooterInfo(responseData?.data);
          }
        } catch (error) {
          console.error("Failed to get security details:", error);
        } finally {
          setIsSecurityLoading(false);
        }
      }
    };

    handleSecurityDetails();
  }, [dispatch]);

  useEffect(() => {
    if (surveyId) {
      getSurveyFullDetails({ surveyId })
        .unwrap()
        .then((res) => {
          if (res?.data) {
            setSurveyDetails(res.data);
          }
        })
        .catch(() => {
          SwalMessage(
            null,
            "Failed to load survey details",
            "Ok",
            "error",
            false
          );
        });
    }
  }, [surveyId, getSurveyFullDetails]);

  if (isLoading) {
    return <Loader />;
  }

  if (!surveyId) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "50vh" }}
      >
        <div className="text-center">
          <h4>Invalid Survey Template</h4>
          {/* <p>Please provide a valid survey ID in the URL parameters.</p> */}
        </div>
      </div>
    );
  }

  if (!surveyDetails) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "50vh" }}
      >
        <div className="text-center">
          <h4>Survey not found</h4>
          <p>The requested survey could not be found.</p>
        </div>
      </div>
    );
  }

  // // Check if survey type is LOCAL and show permission error
  // if (surveyDetails.surveyType === 'LOCAL') {
  //   return (
  //     <div
  //       className="d-flex justify-content-center align-items-center"
  //       style={{ height: "50vh" }}
  //     >
  //       <div className="text-center">
  //         <h4 className="text-danger">No Permission</h4>
  //         <p className="text-muted">You don't have permission to access this QR code</p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="survey-submit-answer-page h-100">
      <SurveyAnswerContainer surveyDetails={surveyDetails} />
    </div>
  );
};

export default SurveySubmitAnswer;
