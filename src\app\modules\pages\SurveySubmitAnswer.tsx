import React, { useEffect, useState } from "react";
import { useGetSurveyFullDetailsMutation } from "../../apis/survaysAPI";
import { ISurveyFullDetails } from "../../apis/type";
import { Loader } from "../../component";
import SwalMessage from "../common/SwalMessage";
import SurveyAnswerContainer from "../surveySubmitAnswer/SurveyAnswerContainer";
import useSurveyUtil from "../survey/helper/useDetectSurvayType";
import { Form, Formik } from "formik";

const SurveySubmitAnswer = () => {
  const { surveyId } = useSurveyUtil();

  const [getSurveyFullDetails, { isLoading }] =
    useGetSurveyFullDetailsMutation();
  const [surveyDetails, setSurveyDetails] = useState<ISurveyFullDetails | null>(
    null
  );

  useEffect(() => {
    if (surveyId) {
      getSurveyFullDetails({ surveyId })
        .unwrap()
        .then((res) => {
          if (res?.data) {
            setSurveyDetails(res.data);
          }
        })
        .catch(() => {
          SwalMessage(
            null,
            "Failed to load survey details",
            "Ok",
            "error",
            false
          );
        });
    }
  }, [surveyId, getSurveyFullDetails]);

  if (isLoading) {
    return <Loader />;
  }

  if (!surveyId) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "50vh" }}
      >
        <div className="text-center">
          <h4>Survey ID is required</h4>
          <p>Please provide a valid survey ID in the URL parameters.</p>
        </div>
      </div>
    );
  }

  if (!surveyDetails) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "50vh" }}
      >
        <div className="text-center">
          <h4>Survey not found</h4>
          <p>The requested survey could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-submit-answer-page h-100">
      <SurveyAnswerContainer surveyDetails={surveyDetails} />
    </div>
  );
};

export default SurveySubmitAnswer;
