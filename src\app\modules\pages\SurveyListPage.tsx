import { orderBy } from "@progress/kendo-data-query";
import { GridColumn as Column, Grid } from "@progress/kendo-react-grid";
import { Tooltip } from "@progress/kendo-react-tooltip";
import React, { useEffect, useState } from "react";
import { IoSettingsOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import { ClipLoader } from "react-spinners";
import { useBreadcrumbContext } from "../../../_metronic/layout/components/header/BreadcrumbsContext";
import { getImage } from "../../utils/CommonUtils";
import { mock_inspection_template_table_data } from "../../../mockData";
import { Badge, Dropdown } from "react-bootstrap";
import { IoMdAdd, IoMdMore } from "react-icons/io";
import { FaRegEye, FaWpforms } from "react-icons/fa";
import {
  Md<PERSON>ontentCopy,
  MdOutlineEdit,
  MdOutlineLocalPrintshop,
  MdOutlineRemoveRedEye,
} from "react-icons/md";
import { FilterSvg, SortingSvg } from "../../utils/SvgUtils";
import usersvg from "../../../_metronic/assets/SideMenuIcon/MenuallSVGIcon/user.svg";
import {
  GetAllInspectionTemplateRes,
  useGetInspectionListMutation,
} from "../../apis/inspectionListAPI";
import FiltersModal from "../Masters/Ticktes/Modal/FiltersModal";
import SortingModal from "../Masters/Ticktes/Modal/SortingModal";
import { processApiResponse } from "../../utils/helper";
import SearchBar from "../../shared/component/SearchBar";
import {
  GetAllSurveysTemplateRes,
  useGetAllSurveyMutation,
  useDuplicateSurveyMutation,
} from "../../apis/survaysAPI";
import SwalMessage from "../common/SwalMessage";
import { swalMessages } from "../../utils/CommonUtils";
import { BsEye } from "react-icons/bs";
import Print_QRCode_Modal from "./Print_QRCode_Modal";
import { HiQrcode } from "react-icons/hi";

interface InspectionProps {
  extraPayload?: Record<string, any>;
  hideManageTemplate?: boolean;
}

const Inspection: React.FC<InspectionProps> = ({ extraPayload }) => {
  const initialSort: Array<any> = [{ field: "", dir: "asc" }];
  const [viewQRcode, setViewQRcode] = useState<string | null>(null);
  const [sort, setSort] = useState(initialSort);

  const [searchKey, setSearhKey] = useState<string>("");
  const [surveyRes, setSurveyRes] = useState<GetAllSurveysTemplateRes | null>(
    null
  );

  const [getAllSurvey, { data, isLoading }] = useGetAllSurveyMutation();
  const [duplicateSurvey, { isLoading: isDuplicateLoading }] =
    useDuplicateSurveyMutation();

  const gridTotalCount = surveyRes?.data?.totalCount || 0;
  const navigate = useNavigate();

  // Pagination start
  const initialDataState: any = { skip: 0, take: 10 };
  const [page, setPage] = useState<any>(initialDataState);
  const [pageSizeValue, setPageSizeValue] = useState<
    number | string | undefined
  >(initialDataState.take);

  const pageSizesArray = [
    { label: "5", value: 5 },
    { label: "10", value: 10 },
    { label: "15", value: 15 },
    { label: "All", value: gridTotalCount },
  ];

  const spinner = (
    <div className="spinner-page">
      <ClipLoader size={60} className="spinner" />
    </div>
  );

  const pageChange = (event: any) => {
    const { skip, take } = event.page;
    const targetEvent = event.targetEvent as any;
    const newTake = targetEvent.value == "All" ? gridTotalCount : take;
    const newPageSizeValue = targetEvent.value == "All" ? "All" : take;

    setPage({ skip, take: newTake });
    setPageSizeValue(newPageSizeValue);
  };

  // useEffect(() => {
  //   const payload: any = {
  //     page: 1,
  //     size: 10,
  //     search: "",
  //     sortingColumns: [],
  //     ...(extraPayload || {}),
  //   };
  //   getAllSurvey(payload);
  // }, []);

  const { setLabels } = useBreadcrumbContext();

  useEffect(() => {
    setLabels([{ path: "", state: {}, breadcrumb: "Survey" }]);
  }, [setLabels]);

  const goTOSurvey = (surveyId: string, surveyType: string) => {
    navigate(`/surveys/survey-details?surveyType=${surveyType || "GLOBAL"}`, {
      state: {
        id: surveyId,
      },
    });
  };

  const submitSurvey = (surveyId: string, surveyType: string) => {
    navigate(
      `/surveys/survey-answer-fill?surveyId=${surveyId}&surveyType=${
        surveyType || "GLOBAL"
      }`
      // {
      //   state: {
      //     id: surveyId,
      //   },
      // }
    );
  };

  const handleDuplicate = async (surveyId: string) => {
    const confirm = await SwalMessage(
      swalMessages.title.commonTitle,
      swalMessages.text.duplicateTemplate,
      "Duplicate",
      swalMessages.icon.info,
      true
    );
    if (confirm) {
      duplicateSurvey({ surveyId })
        .unwrap()
        .then((res) => {
          const isSuccess = res?.success;
          if (isSuccess) {
            fetchTemplate();
            SwalMessage(
              null,
              "Survey Template has been duplicated successfully. Keep in mind you will need the property update,to whom the survey goes to and any ticket assignment fields prior to the QR code generation.",
              "Ok",
              "success",
              false
            );
          } else {
            SwalMessage(
              null,
              res?.errormsg || "Failed to duplicate survey",
              "Ok",
              "error",
              false
            );
          }
        })
        .catch((error) => {
          SwalMessage(null, "Failed to duplicate survey", "Ok", "error", false);
        });
    }
  };

  const renderName = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <span
          className="ellipsis-cell cursor-pointer "
          title={content.surveyName}
          onClick={() => goTOSurvey(content.surveyId, content.surveyType)}
        >
          {content.surveyName}
        </span>
      </td>
    );
  };

  const handleQRView = (qrcode: any) => {
    setViewQRcode(qrcode);
  };

  const renderQrcode = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="text-center">
        <HiQrcode
          className="td-icon cursor-pointer user-image p-1"
          onClick={() => handleQRView(content.qrCodeBase64)}
        />
      </td>
    );
  };

  const renderaction = (props: any) => {
    const content = props.dataItem;
    return (
      <>
        <td className="k-table-td">
          <Dropdown className="new-chat-btn" style={{ position: "static" }}>
            <Dropdown.Toggle as="span" className="fs-1 cursor-pointer ms-2">
              <IoMdMore className="td-icon cursor-pointer" />
            </Dropdown.Toggle>
            <Dropdown.Menu align="end">
              <Dropdown.Item
                onClick={() =>
                  goTOSurvey(content?.surveyId, content?.surveyType)
                }
              >
                <span className="fs-5">
                  <BsEye className="me-4" />
                  View Template
                </span>
              </Dropdown.Item>
              <Dropdown.Item onClick={() => handleDuplicate(content?.surveyId)}>
                <span className="fs-5">
                  <MdContentCopy className="me-4" />
                  Duplicate Survey
                </span>
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() =>
                  submitSurvey(content?.surveyId, content?.surveyType)
                }
              >
                <span className="fs-5">
                  <FaWpforms className="me-4" />
                  Submit Survey
                </span>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </td>
      </>
    );
  };

  const renderOutSideContact = (props: any) => {
    const content = props.dataItem;
    return (
      <td className="k-table-td">
        <div>
          <input type="checkbox" checked={!!content.outsideContact} />
        </div>
      </td>
    );
  };

  const fetchTemplate = async () => {
    const pageNumber = Math.floor(page.skip / page.take) + 1;
    const payload = {
      page: pageNumber,
      size: page.take,
      search: searchKey,
      ...(extraPayload || {}),
    };
    await getAllSurvey(payload)
      .unwrap()
      .then((res) => {
        processApiResponse({
          res,
          onSuccess: () => {
            setSurveyRes(res);
          },
        });
      })
      .catch((err) => {});
  };

  //Get Inspection List
  useEffect(() => {
    fetchTemplate();
  }, [searchKey, page]); //Add dep

  const userinfo = localStorage.userinfo
    ? JSON.parse(localStorage.userinfo)
    : null;

  const isValidUserPermission =
    userinfo?.isfullaccess?.toString() === "1" &&
    userinfo?.displayusertype === "COMPANY_ADMIN";

  return (
    <>
      <div>
        {(isLoading || isDuplicateLoading) && spinner}
        <div className="row pageheader mb-7">
          <div className=" col-xl-6 col-lg-6 col-sm-6 mt-auto mb-auto">
            <div className="row">
              <div className=" col-xl-6 col-lg-6 col-sm-6 col-6">
                <SearchBar
                  type="text"
                  onChange={(e) => setSearhKey(e.target.value)}
                />
              </div>
            </div>
          </div>
          <div className=" col-xl-6 col-lg-6 col-sm-6 text-end">
            <div className="d-flex justify-content-end">
              <div className=" p-0">
                <Link
                  to={"/surveys/survey-select-Type"}
                  className="btn  rx-btn ms-3 mt-3 mt-md-0"
                >
                  <IoMdAdd className="btn-icon-custom" />
                  Create New Survey
                </Link>
              </div>
            </div>
          </div>
        </div>
        <div className="card mt-0 ">
          <div className="card-body p-0" style={{ margin: "0.8px" }}>
            <div className="table_div" style={{ width: "100%" }}>
              <Tooltip position="bottom" anchorElement="target">
                {mock_inspection_template_table_data && (
                  <Grid
                    data={orderBy(surveyRes?.data?.data || [], sort)}
                    skip={page.skip}
                    take={page.take}
                    total={gridTotalCount}
                    pageable={{
                      buttonCount: 4,
                      pageSizes: pageSizesArray.map((size: any) => size.label),
                      pageSizeValue: pageSizeValue,
                    }}
                    onPageChange={pageChange}
                    sortable
                    sort={sort}
                    onSortChange={(e: any) => setSort(e.sort)}
                  >
                    <Column
                      title="QR Code"
                      headerClassName="center-header"
                      width="90px"
                      cell={renderQrcode}
                    />
                    <Column title="ID" field="id" width={"120px"} />
                    <Column
                      title="Survey Name"
                      field="surveyName"
                      width={"420px"}
                      cell={(props) =>
                        renderName({
                          ...props,
                        })
                      }
                    />
                    {/* <Column
                    title="Property/Department"
                    field="surveyName"
                    cell={(props) =>
                      renderTooltipCell({
                        ...props,
                        content:
                          props.dataItem.departmentName ||
                          props.dataItem.propertyName,
                      })
                    }
                  /> */}

                    <Column title="No. of Questions" field="numberOfQuestion" />
                    <Column title="No. of Responses" field="numberOfResponse" />

                    <Column
                      title="Outside Contact"
                      field="outsideContact"
                      width="200px"
                      cell={(props) =>
                        renderOutSideContact({
                          ...props,
                        })
                      }
                    />

                    {/* Action */}
                    <Column
                      title="Action"
                      width="80px"
                      cell={(props) =>
                        renderaction({
                          ...props,
                        })
                      }
                    />
                  </Grid>
                )}
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
      <Print_QRCode_Modal
        qrmodal={!!viewQRcode}
        setqrmodal={setViewQRcode}
        qrBase64={viewQRcode}
        setGridLoading={() => {}}
        title={"Download QR Code"}
      />
    </>
  );
};

export default Inspection;
