// QuestionAttchmentInput.tsx
import React, { ChangeEvent, useEffect } from "react";
import AttachmentInputUI, {
  UploadedAttachment,
} from "../../../common/AttachmentInputUI";
import { useFileUpload } from "../../../../shared/hooks/useFileUpload";

interface Props {
  attachments?: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>;
  allowedAttachmentTypes?: string[];
  onAttachmentsChange?: (attachments: Array<{
    attachmentUrl: string;
    attachmentType: string;
    attachmentId: string;
  }>) => void;
  disabled?: boolean;
  id?: string;
  onUploadingStateChange?: (isUploading: boolean) => void;
}

const QuestionAttchmentInput: React.FC<Props> = ({
  attachments = [],
  allowedAttachmentTypes = [],
  onAttachmentsChange,
  disabled = false,
  id = "question-attachment-input",
  onUploadingStateChange,
}) => {
  const { uploadedAttachments, removeAttachment, processAndUploadFiles } =
    useFileUpload();

  // Helper function to convert survey attachment types to MIME types
  const getAcceptedFileTypes = (types: string[]): string => {
    const mimeTypeMap: Record<string, string> = {
      IMAGE: "image/*",
      VIDEO: "video/*",
      AUDIO: "audio/*",
    };

    if (!types || types.length === 0) {
      return "image/*"; // Default fallback
    }

    return types
      .map((type) => mimeTypeMap[type.toUpperCase()] || type)
      .join(",");
  };

  const handleRemoveAttachment = (
    attachment: UploadedAttachment,
    idx: number
  ) => {
    const filterList = removeAttachment(attachment?.id || "");
    
    // Filter out the removed attachment from the existing attachments
    const filteredAttachments = attachments.filter(
      (file) => file.attachmentUrl !== attachment.url
    );

    // Notify parent component of the change
    if (onAttachmentsChange) {
      onAttachmentsChange(filteredAttachments);
    }
  };

  const handleUploadFile = async (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && onAttachmentsChange) {
      const filesData = await processAndUploadFiles(files);
      const modifiedFiles = filesData.map((file) => ({
        attachmentUrl: file.url,
        attachmentType: file.type,
        attachmentId: file.id,
      }));

      // Combine existing attachments with new ones
      const updatedAttachments = [...attachments, ...modifiedFiles];
      onAttachmentsChange(updatedAttachments);
    }
  };

  // Convert existing attachments to UploadedAttachment format
  const modifiedPreFilledFiles: UploadedAttachment[] = attachments.map((file) => ({
    id: file.attachmentId,
    url: file.attachmentUrl,
    type: file.attachmentType,
    name: file.attachmentName,
    previewUrl: file.attachmentUrl,
    isUploading: false,
  }));

  // Combine pre-filled and newly uploaded attachments
  const combinedAttachments = [
    ...modifiedPreFilledFiles,
    ...uploadedAttachments,
  ];

  // Remove duplicates based on ID
  const uniqueAttachments = Array.from(
    new Map(combinedAttachments.map((item) => [item.id, item])).values()
  );

  // Track uploading state and notify parent
  useEffect(() => {
    if (onUploadingStateChange) {
      const isUploading = uploadedAttachments.some(attachment => attachment.isUploading);
      onUploadingStateChange(isUploading);
    }
  }, [uploadedAttachments, onUploadingStateChange]);

  return (
    <AttachmentInputUI
      attachments={uniqueAttachments}
      onUploadFile={handleUploadFile}
      onRemoveAttachment={handleRemoveAttachment}
      id={id}
      disabled={disabled}
      acceptedFileTypes={getAcceptedFileTypes(allowedAttachmentTypes)}
    />
  );
};

export default QuestionAttchmentInput;
