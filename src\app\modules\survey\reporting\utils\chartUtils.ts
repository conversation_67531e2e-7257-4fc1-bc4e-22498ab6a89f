// Chart utility functions for data transformation and chart generation

import { ApexOptions } from 'apexcharts';
import { ChartData, ChartDataPoint, ApiChartData, ChartType, ChartGenerationOptions } from '../types/chartTypes';
import { getColorForValue, COLOR_SCHEMES } from '../config/chartConfig';

/**
 * Transform API data to chart data format
 */
export const transformApiDataToChartData = (
  apiData: ApiChartData,
  chartType: ChartType,
  colorScheme: 'default' | 'sentiment' | 'category' = 'default'
): ChartData => {
  const dataPoints: ChartDataPoint[] = apiData.answers.map((answer, index) => ({
    name: answer.answerText,
    value: answer.responseCount,
    percentage: answer.percentage,
    color: getColorForValue(answer.answerText, colorScheme),
    count: answer.responseCount
  }));

  return {
    id: `${chartType}_${apiData.questionId}`,
    questionId: apiData.questionId,
    questionNumber: apiData.questionNumber,
    title: apiData.questionText,
    type: chartType,
    totalResponses: apiData.totalResponses,
    answeredResponses: apiData.answeredResponses,
    data: dataPoints,
    averageScore: apiData.averageScore,
    responseType: apiData.responseType
  };
};

/**
 * Generate chart options for different chart types
 */
export const generateChartOptions = (
  chartData: ChartData,
  theme: 'light' | 'dark' = 'light',
  customOptions: any = {}
): ApexOptions => {
  const baseOptions = getBaseChartOptions(theme);
  
  switch (chartData.type) {
    case 'pie':
      return { ...baseOptions, ...getPieChartOptions(chartData, theme), ...customOptions };
    case 'bar':
      return { ...baseOptions, ...getBarChartOptions(chartData, theme), ...customOptions };
    case 'donut':
      return { ...baseOptions, ...getDonutChartOptions(chartData, theme), ...customOptions };
    case 'stackedBar':
      return { ...baseOptions, ...getStackedBarChartOptions(chartData, theme), ...customOptions };
    case 'line':
      return { ...baseOptions, ...getLineChartOptions(chartData, theme), ...customOptions };
    case 'area':
      return { ...baseOptions, ...getAreaChartOptions(chartData, theme), ...customOptions };
    default:
      return { ...baseOptions, ...getPieChartOptions(chartData, theme), ...customOptions };
  }
};

/**
 * Base chart options for all chart types
 */
const getBaseChartOptions = (theme: 'light' | 'dark'): ApexOptions => {
  const textColor = theme === 'dark' ? '#ffffff' : '#000000';
  const gridColor = theme === 'dark' ? '#444444' : '#e0e0e0';
  
  return {
    chart: {
      fontFamily: 'inherit',
      toolbar: { show: false },
      background: 'transparent'
    },
    theme: {
      mode: theme
    },
    grid: {
      borderColor: gridColor
    },
    xaxis: {
      labels: {
        style: {
          colors: textColor
        }
      }
    },
    yaxis: {
      labels: {
        style: {
          colors: textColor
        }
      }
    },
    legend: {
      labels: {
        colors: textColor
      }
    }
  };
};

/**
 * Pie chart specific options
 */
const getPieChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  const textColor = theme === 'dark' ? '#ffffff' : '#000000';

  return {
    series: chartData.data.map(item => item.value),
    chart: {
      type: 'pie',
      height: 170
    },
    labels: chartData.data.map(item => item.name),
    colors: chartData.data.map(item => item.color),
    legend: {
      position: 'right',
      horizontalAlign: 'left',
      labels: {
        colors: Array(chartData.data.length).fill(textColor),
        useSeriesColors: false
      }
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '11px',
        fontWeight: 500,
        colors: chartData.data.map(item => getContrastColor(item.color))
      },
      dropShadow: { enabled: false },
      formatter: (val: number) => Math.round(val) + '%'
    },
    plotOptions: {
      pie: {
        expandOnClick: false,
        customScale: 0.85,
        dataLabels: {
          offset: -10,
          minAngleToShowLabel: 10
        }
      }
    },
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: textColor
      }
    },
    subtitle: {
      text: `${chartData.answeredResponses} out of ${chartData.totalResponses} answered`,
      align: 'left',
      style: {
        fontSize: '12px',
        color: theme === 'dark' ? '#cccccc' : '#666666'
      }
    },
    tooltip: {
      enabled: true,
      theme: theme,
      fillSeriesColor: false,
      style: {
        fontSize: '12px',
        fontFamily: 'inherit'
      },
      y: {
        formatter: (val: number) => `${Math.round(val)}%`,
        title: {
          formatter: (seriesName: string) => ''
        }
      }
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { height: 250 },
          legend: { position: 'bottom' }
        }
      }
    ]
  };
};

/**
 * Bar chart specific options
 */
const getBarChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  const textColor = theme === 'dark' ? '#ffffff' : '#000000';

  return {
    series: [{
      name: 'Responses',
      data: chartData.data.map(item => item.value)
    }],
    chart: {
      type: 'bar',
      height: 350,
      toolbar: { show: false }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 4,
        borderRadiusApplication: 'end',
        distributed: true
      }
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => {
        return val.toString();
      },
      style: {
        fontSize: '12px',
        fontWeight: 'bold',
        colors: ['#000000']
      }
    },
    xaxis: {
      categories: chartData.data.map(item => item.name),
      labels: {
        style: {
          colors: textColor,
          fontSize: '11px'
        },
        rotate: -45
      },
      axisBorder: {
        show: true,
        color: theme === 'dark' ? '#444444' : '#e0e0e0'
      },
      axisTicks: {
        show: true,
        color: theme === 'dark' ? '#444444' : '#e0e0e0'
      }
    },
    yaxis: {
      labels: {
        style: {
          colors: textColor,
          fontSize: '11px'
        },
        formatter: (val: number) => Math.round(val).toString()
      },
      title: {
        text: 'Number of Responses',
        style: {
          color: textColor,
          fontSize: '12px'
        }
      }
    },
    grid: {
      show: true,
      borderColor: theme === 'dark' ? '#444444' : '#e0e0e0',
      strokeDashArray: 3
    },
    legend: {
      show: false
    },
    tooltip: {
      y: {
        formatter: function (val: number, opts: any) {
          const dataPoint = chartData.data[opts.dataPointIndex];
          return `${val} responses (${dataPoint.percentage}%)`;
        }
      },
      theme: theme
    },
    colors: chartData.data.map(item => item.color),
    title: {
      text: `${chartData.questionNumber}. ${chartData.title}`,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: textColor
      }
    },
    subtitle: {
      text: `Total responses: ${chartData.answeredResponses}/${chartData.totalResponses}`,
      align: 'left',
      style: {
        fontSize: '12px',
        color: theme === 'dark' ? '#cccccc' : '#666666'
      }
    }
  };
};

/**
 * Get contrast color for text on colored backgrounds
 */
export const getContrastColor = (hex: string): string => {
  const clean = hex.replace('#', '');
  const r = parseInt(clean.slice(0, 2), 16);
  const g = parseInt(clean.slice(2, 4), 16);
  const b = parseInt(clean.slice(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.7 ? '#000000' : '#ffffff';
};

// Placeholder functions for other chart types
const getDonutChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  const pieOptions = getPieChartOptions(chartData, theme);
  return {
    ...pieOptions,
    chart: {
      ...pieOptions.chart,
      type: 'donut',
      height: 300
    },
    plotOptions: {
      pie: {
        donut: {
          size: '65%',
          labels: {
            show: true,
            total: {
              show: true,
              label: 'Total',
              formatter: function () {
                return `${chartData.answeredResponses}/${chartData.totalResponses}`;
              }
            }
          }
        }
      }
    },
    legend: {
      ...pieOptions.legend,
      position: 'bottom',
      horizontalAlign: 'center'
    }
  };
};

const getStackedBarChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  // Implementation for stacked bar chart
  return getPieChartOptions(chartData, theme); // Placeholder
};

const getLineChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  // Implementation for line chart
  return getPieChartOptions(chartData, theme); // Placeholder
};

const getAreaChartOptions = (chartData: ChartData, theme: 'light' | 'dark'): ApexOptions => {
  // Implementation for area chart
  return getPieChartOptions(chartData, theme); // Placeholder
};
